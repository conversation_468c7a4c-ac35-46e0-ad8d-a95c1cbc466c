@extends('backend.layouts.app')

@section('title', 'Slider Management - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Slider Management</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Sliders</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Stats Dashboard -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card card-animate bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-white-50 mb-0">Total Sliders</p>
                        <h4 class="fs-22 fw-semibold mb-0"><span class="counter-value" data-target="{{ $sliders->count() }}">0</span></h4>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded">
                            <div class="avatar-title bg-soft-light rounded">
                                <i class="ri-slideshow-line fs-22"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card card-animate bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-white-50 mb-0">Active Sliders</p>
                        <h4 class="fs-22 fw-semibold mb-0"><span class="counter-value" data-target="{{ $sliders->where('status', 'active')->count() }}">0</span></h4>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded">
                            <div class="avatar-title bg-soft-light rounded">
                                <i class="ri-play-circle-line fs-22"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card card-animate bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-white-50 mb-0">Inactive Sliders</p>
                        <h4 class="fs-22 fw-semibold mb-0"><span class="counter-value" data-target="{{ $sliders->where('status', 'inactive')->count() }}">0</span></h4>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded">
                            <div class="avatar-title bg-soft-light rounded">
                                <i class="ri-pause-circle-line fs-22"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card card-animate bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <p class="text-uppercase fw-medium text-white-50 mb-0">Last Updated</p>
                        <h6 class="fs-14 fw-medium mb-0">{{ $sliders->max('updated_at')?->diffForHumans() ?? 'Never' }}</h6>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded">
                            <div class="avatar-title bg-soft-light rounded">
                                <i class="ri-time-line fs-22"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Action Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ route('sliders.create') }}" class="btn btn-primary btn-animation">
                            <i class="ri-add-line me-1"></i>Add New Slider
                        </a>
                        <button type="button" class="btn btn-outline-secondary" id="toggleReorder">
                            <i class="ri-drag-move-line me-1"></i>Reorder Sliders
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="autoOptimize()">
                            <i class="ri-magic-line me-1"></i>Auto Optimize
                        </button>
                        <button type="button" class="btn btn-outline-purple" onclick="openSliderWizard()">
                            <i class="ri-magic-fill me-1"></i>Quick Wizard
                        </button>
                    </div>
                    <div class="d-flex gap-2 flex-wrap">
                        <div class="input-group" style="width: 250px;">
                            <input type="text" class="form-control" placeholder="Search sliders..." id="searchSliders">
                            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                <i class="ri-close-line"></i>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-filter-line me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="filterSliders('all')">
                                    <i class="ri-list-check me-2"></i>All Sliders
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="filterSliders('active')">
                                    <i class="ri-check-line me-2"></i>Active Only
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="filterSliders('inactive')">
                                    <i class="ri-close-line me-2"></i>Inactive Only
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="filterSliders('recent')">
                                    <i class="ri-time-line me-2"></i>Recently Updated
                                </a></li>
                            </ul>
                        </div>
                        <button type="button" class="btn btn-outline-info" onclick="previewSliders()">
                            <i class="ri-eye-line me-1"></i>Live Preview
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ri-more-line me-1"></i>Bulk Actions
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="bulkActivate()">
                                    <i class="ri-check-line me-2"></i>Activate All
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="bulkDeactivate()">
                                    <i class="ri-close-line me-2"></i>Deactivate All
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportSliders()">
                                    <i class="ri-download-line me-2"></i>Export Data
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="openImportModal()">
                                    <i class="ri-upload-line me-2"></i>Import Data
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="bulkDelete()">
                                    <i class="ri-delete-bin-line me-2"></i>Delete Inactive
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sliders Grid -->
<div class="row" id="slidersContainer">
    @forelse($sliders as $slider)
        <div class="col-xl-6 col-lg-6 col-md-12 slider-item" data-id="{{ $slider->id }}">
            <div class="card slider-card h-100">
                <!-- Media Preview -->
                <div class="slider-media-preview position-relative">
                    @if($slider->slider_one_img)
                        <img src="{{ $slider->slider_one_img_url }}" class="card-img-top" 
                             style="height: 200px; object-fit: cover;" alt="{{ $slider->first_slogan_one }}">
                        <div class="media-type-badge">
                            <i class="ri-image-line"></i> Image
                        </div>
                    @else
                        <div class="no-media-placeholder d-flex align-items-center justify-content-center" 
                             style="height: 200px; background: #f8f9fa;">
                            <i class="ri-image-line text-muted" style="font-size: 3rem;"></i>
                        </div>
                    @endif
                    
                    <!-- Status Badge -->
                    <div class="status-badge position-absolute top-0 end-0 m-2">
                        {!! $slider->status_badge !!}
                    </div>
                    
                    <!-- Drag Handle (hidden by default) -->
                    <div class="drag-handle position-absolute top-0 start-0 m-2" style="display: none;">
                        <i class="ri-drag-move-2-line text-white bg-dark p-2 rounded"></i>
                    </div>
                </div>

                <!-- Card Body -->
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-1">{{ $slider->first_slogan_one ?: 'Untitled Slider' }}</h5>
                        <span class="badge bg-light text-dark">#{{ $slider->sort_order ?? $slider->id }}</span>
                    </div>
                    
                    @if($slider->second_slogan_one)
                        <p class="text-muted small mb-2">{{ $slider->second_slogan_one }}</p>
                    @endif
                    
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Slogan Two:</small>
                            <p class="mb-1">{{ Str::limit($slider->first_slogan_two, 50) }}</p>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Second Slogan:</small>
                            <p class="mb-1">{{ Str::limit($slider->second_slogan_two, 50) }}</p>
                        </div>
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="{{ route('sliders.show', $slider) }}" class="btn btn-outline-info" title="View">
                                <i class="ri-eye-line"></i>
                            </a>
                            <a href="{{ route('sliders.edit', $slider) }}" class="btn btn-outline-primary" title="Edit">
                                <i class="ri-edit-line"></i>
                            </a>
                            <button type="button" class="btn btn-outline-secondary" 
                                    onclick="duplicateSlider({{ $slider->id }})" title="Duplicate">
                                <i class="ri-file-copy-line"></i>
                            </button>
                        </div>
                        
                        <div class="d-flex gap-1">
                            <button type="button" class="btn btn-sm btn-outline-{{ ($slider->status ?? 'active') === 'active' ? 'warning' : 'success' }}" 
                                    onclick="toggleStatus({{ $slider->id }})" title="Toggle Status">
                                <i class="ri-{{ ($slider->status ?? 'active') === 'active' ? 'pause' : 'play' }}-line"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="deleteSlider({{ $slider->id }})" title="Delete">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="text-center py-5">
                <i class="ri-image-line text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No Sliders Found</h4>
                <p class="text-muted">Create your first slider to get started</p>
                <a href="{{ route('sliders.create') }}" class="btn btn-primary">
                    <i class="ri-add-line me-1"></i>Create First Slider
                </a>
            </div>
        </div>
    @endforelse
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Slider Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="previewContainer" style="height: 500px; background: #000;">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.slider-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.slider-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.slider-item.reorder-mode .slider-card {
    cursor: move;
    border-color: var(--vz-primary);
}

.media-type-badge {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
}

.status-badge .badge {
    font-size: 0.7rem;
}

.drag-handle {
    cursor: move;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    transform: scale(1.05);
}

/* Preview Modal Styles */
.preview-slider-wrapper {
    background: #000;
    overflow: hidden;
}

.preview-slide {
    display: none;
    position: relative;
    width: 100%;
    height: 100%;
}

.preview-slide.active {
    display: block;
}

.preview-media {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.preview-content {
    z-index: 2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.preview-title {
    font-size: 2.5rem;
    font-weight: bold;
}

.preview-subtitle {
    font-size: 1.5rem;
}

.preview-description {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.preview-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    padding: 0;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
let sortable = null;
let reorderMode = false;

// Initialize sortable
function initSortable() {
    const container = document.getElementById('slidersContainer');
    if (container) {
        sortable = Sortable.create(container, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            disabled: true,
            onEnd: function(evt) {
                if (reorderMode) {
                    updateOrder();
                }
            }
        });
    }
}

// Toggle reorder mode
document.getElementById('toggleReorder').addEventListener('click', function() {
    reorderMode = !reorderMode;
    const btn = this;
    const dragHandles = document.querySelectorAll('.drag-handle');
    const sliderItems = document.querySelectorAll('.slider-item');

    if (reorderMode) {
        btn.innerHTML = '<i class="ri-save-line me-1"></i>Save Order';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');
        sortable.option('disabled', false);

        dragHandles.forEach(handle => handle.style.display = 'block');
        sliderItems.forEach(item => item.classList.add('reorder-mode'));
    } else {
        btn.innerHTML = '<i class="ri-drag-move-line me-1"></i>Reorder Sliders';
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-secondary');
        sortable.option('disabled', true);

        dragHandles.forEach(handle => handle.style.display = 'none');
        sliderItems.forEach(item => item.classList.remove('reorder-mode'));

        updateOrder();
    }
});

// Update order
function updateOrder() {
    const order = Array.from(document.querySelectorAll('.slider-item')).map(item => item.dataset.id);

    fetch('{{ route("sliders.reorder") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ order: order })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
            notifyFrontendUpdate();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to update order');
    });
}

// Toggle status
function toggleStatus(id) {
    const sliderCard = document.querySelector(`[data-id="${id}"]`);
    const statusBadge = sliderCard.querySelector('.status-badge .badge');
    const toggleBtn = sliderCard.querySelector(`button[onclick="toggleStatus(${id})"]`);

    fetch(`/admin/sliders/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update status badge
            const newStatus = data.status;
            statusBadge.className = `badge ${newStatus === 'active' ? 'bg-success' : 'bg-secondary'}`;
            statusBadge.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);

            // Update toggle button
            const isActive = newStatus === 'active';
            toggleBtn.className = `btn btn-sm btn-outline-${isActive ? 'warning' : 'success'}`;
            toggleBtn.innerHTML = `<i class="ri-${isActive ? 'pause' : 'play'}-line"></i>`;
            toggleBtn.title = isActive ? 'Deactivate' : 'Activate';

            showToast('success', data.message);
            notifyFrontendUpdate();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to toggle status');
    });
}

// Delete slider
function deleteSlider(id) {
    if (confirm('Are you sure you want to delete this slider? This action cannot be undone.')) {
        fetch(`/admin/sliders/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => {
            if (response.ok) {
                showToast('success', 'Slider deleted successfully!');
                notifyFrontendUpdate();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('error', 'Failed to delete slider');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Failed to delete slider');
        });
    }
}

// Duplicate slider
function duplicateSlider(id) {
    fetch(`/admin/sliders/${id}/duplicate`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => {
        if (response.ok) {
            showToast('success', 'Slider duplicated successfully!');
            notifyFrontendUpdate();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('error', 'Failed to duplicate slider');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to duplicate slider');
    });
}

// Preview sliders
function previewSliders() {
    const previewContainer = document.getElementById('previewContainer');
    previewContainer.innerHTML = '<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

    // Fetch active sliders
    fetch(`/api/sliders?t=${Date.now()}`)
        .then(response => response.json())
        .then(sliders => {
            if (sliders && sliders.length > 0) {
                renderPreviewSliders(sliders);
            } else {
                previewContainer.innerHTML = '<div class="d-flex justify-content-center align-items-center h-100 text-muted"><i class="ri-image-line me-2"></i>No active sliders to preview</div>';
            }
        })
        .catch(error => {
            console.error('Error loading sliders for preview:', error);
            previewContainer.innerHTML = '<div class="d-flex justify-content-center align-items-center h-100 text-danger"><i class="ri-error-warning-line me-2"></i>Error loading preview</div>';
        });

    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

function renderPreviewSliders(sliders) {
    const previewContainer = document.getElementById('previewContainer');
    let currentSlide = 0;

    const sliderHtml = `
        <div class="preview-slider-wrapper position-relative w-100 h-100">
            <div class="preview-slides">
                ${sliders.map((slider, index) => `
                    <div class="preview-slide ${index === 0 ? 'active' : ''}"
                         style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('${slider.slider_one_img_url}') center/cover;">
                        <div class="preview-content position-absolute top-50 start-50 translate-middle text-center text-white">
                            <h2 class="preview-title mb-3">${slider.first_slogan_one}</h2>
                            <h4 class="preview-subtitle mb-3">${slider.second_slogan_one}</h4>
                            <p class="preview-description mb-4">${slider.first_slogan_two}</p>
                            <p class="preview-description">${slider.second_slogan_two}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
            ${sliders.length > 1 ? `
                <button class="btn btn-outline-light position-absolute top-50 start-0 translate-middle-y ms-3" onclick="prevPreviewSlide()">
                    <i class="ri-arrow-left-line"></i>
                </button>
                <button class="btn btn-outline-light position-absolute top-50 end-0 translate-middle-y me-3" onclick="nextPreviewSlide()">
                    <i class="ri-arrow-right-line"></i>
                </button>
                <div class="preview-indicators position-absolute bottom-0 start-50 translate-middle-x mb-3">
                    ${sliders.map((_, index) => `
                        <button class="btn btn-sm ${index === 0 ? 'btn-light' : 'btn-outline-light'} me-1"
                                onclick="goToPreviewSlide(${index})" data-slide="${index}"></button>
                    `).join('')}
                </div>
            ` : ''}
        </div>
    `;

    previewContainer.innerHTML = sliderHtml;
}

let currentPreviewSlide = 0;

function nextPreviewSlide() {
    const slides = document.querySelectorAll('.preview-slide');
    const indicators = document.querySelectorAll('.preview-indicators button');

    slides[currentPreviewSlide].classList.remove('active');
    indicators[currentPreviewSlide].classList.remove('btn-light');
    indicators[currentPreviewSlide].classList.add('btn-outline-light');

    currentPreviewSlide = (currentPreviewSlide + 1) % slides.length;

    slides[currentPreviewSlide].classList.add('active');
    indicators[currentPreviewSlide].classList.remove('btn-outline-light');
    indicators[currentPreviewSlide].classList.add('btn-light');
}

function prevPreviewSlide() {
    const slides = document.querySelectorAll('.preview-slide');
    const indicators = document.querySelectorAll('.preview-indicators button');

    slides[currentPreviewSlide].classList.remove('active');
    indicators[currentPreviewSlide].classList.remove('btn-light');
    indicators[currentPreviewSlide].classList.add('btn-outline-light');

    currentPreviewSlide = currentPreviewSlide === 0 ? slides.length - 1 : currentPreviewSlide - 1;

    slides[currentPreviewSlide].classList.add('active');
    indicators[currentPreviewSlide].classList.remove('btn-outline-light');
    indicators[currentPreviewSlide].classList.add('btn-light');
}

function goToPreviewSlide(index) {
    const slides = document.querySelectorAll('.preview-slide');
    const indicators = document.querySelectorAll('.preview-indicators button');

    slides[currentPreviewSlide].classList.remove('active');
    indicators[currentPreviewSlide].classList.remove('btn-light');
    indicators[currentPreviewSlide].classList.add('btn-outline-light');

    currentPreviewSlide = index;

    slides[currentPreviewSlide].classList.add('active');
    indicators[currentPreviewSlide].classList.remove('btn-outline-light');
    indicators[currentPreviewSlide].classList.add('btn-light');
}

// Utility function for toast notifications
function showToast(type, message) {
    // Create toast notification
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';

    const toastHtml = `
        <div class="toast align-items-center text-white ${bgClass} border-0" role="alert" id="${toastId}">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="ri-${type === 'success' ? 'check' : type === 'error' ? 'error-warning' : 'information'}-line me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 4000 });
    toast.show();

    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// Function to notify frontend of slider changes
function notifyFrontendUpdate() {
    // Try to refresh frontend sliders if the homepage is open in another tab
    try {
        if (window.opener && !window.opener.closed) {
            window.opener.postMessage({ type: 'REFRESH_SLIDERS' }, window.location.origin);
        }

        // Also try to refresh if this is opened in an iframe
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({ type: 'REFRESH_SLIDERS' }, window.location.origin);
        }
    } catch (e) {
        // Ignore cross-origin errors
    }
}

// Bulk Actions
function bulkActivate() {
    if (confirm('Are you sure you want to activate all sliders?')) {
        const sliderIds = Array.from(document.querySelectorAll('.slider-item')).map(item => item.dataset.id);
        bulkUpdateStatus(sliderIds, 'active', 'All sliders activated successfully!');
    }
}

function bulkDeactivate() {
    if (confirm('Are you sure you want to deactivate all sliders?')) {
        const sliderIds = Array.from(document.querySelectorAll('.slider-item')).map(item => item.dataset.id);
        bulkUpdateStatus(sliderIds, 'inactive', 'All sliders deactivated successfully!');
    }
}

function bulkDelete() {
    if (confirm('Are you sure you want to delete all inactive sliders? This action cannot be undone.')) {
        const inactiveSliders = Array.from(document.querySelectorAll('.slider-item'))
            .filter(item => {
                const statusBadge = item.querySelector('.status-badge .badge');
                return statusBadge && statusBadge.textContent.toLowerCase().includes('inactive');
            })
            .map(item => item.dataset.id);

        if (inactiveSliders.length === 0) {
            showToast('info', 'No inactive sliders found to delete.');
            return;
        }

        bulkDeleteSliders(inactiveSliders);
    }
}

function bulkUpdateStatus(sliderIds, status, successMessage) {
    const promises = sliderIds.map(id =>
        fetch(`/admin/sliders/${id}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
    );

    Promise.all(promises)
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(results => {
            const successful = results.filter(r => r.success).length;
            if (successful > 0) {
                showToast('success', successMessage);
                notifyFrontendUpdate();
                setTimeout(() => location.reload(), 1000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Failed to update slider status');
        });
}

function bulkDeleteSliders(sliderIds) {
    const promises = sliderIds.map(id =>
        fetch(`/admin/sliders/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
    );

    Promise.all(promises)
        .then(responses => {
            const successful = responses.filter(r => r.ok).length;
            if (successful > 0) {
                showToast('success', `${successful} inactive slider(s) deleted successfully!`);
                notifyFrontendUpdate();
                setTimeout(() => location.reload(), 1000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Failed to delete sliders');
        });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initSortable();
});
</script>
@endpush
