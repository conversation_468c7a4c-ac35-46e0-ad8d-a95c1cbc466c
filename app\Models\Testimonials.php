<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Testimonials extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'company',
        'comment',
        'rating',
        'client_image',
        'status',
        'sort_order',
        'featured',
    ];

    protected $casts = [
        'rating' => 'integer',
        'sort_order' => 'integer',
        'featured' => 'boolean',
    ];

    /**
     * Scope to get only active testimonials
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get only featured testimonials
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope to get testimonials ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get the full URL for client image
     */
    public function getClientImageUrlAttribute()
    {
        if ($this->client_image) {
            // Check if it's already a full URL
            if (filter_var($this->client_image, FILTER_VALIDATE_URL)) {
                return $this->client_image;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->client_image)) {
                return Storage::url($this->client_image);
            }
            // Fallback to asset path
            return asset($this->client_image);
        }
        return null;
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => 'bg-success',
            'inactive' => 'bg-secondary',
        ];

        $class = $badges[$this->status] ?? 'bg-secondary';
        $text = ucfirst($this->status);
        return "<span class='badge {$class}'>{$text}</span>";
    }

    /**
     * Get star rating HTML
     */
    public function getStarRatingAttribute()
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $stars .= '<i class="fas fa-star text-warning"></i>';
            } else {
                $stars .= '<i class="far fa-star text-muted"></i>';
            }
        }
        return $stars;
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-assign sort order when creating
        static::creating(function ($testimonial) {
            if (is_null($testimonial->sort_order)) {
                $testimonial->sort_order = static::max('sort_order') + 1;
            }
            if (is_null($testimonial->status)) {
                $testimonial->status = 'active';
            }
            if (is_null($testimonial->rating)) {
                $testimonial->rating = 5;
            }
        });

        // Clean up files when deleting
        static::deleting(function ($testimonial) {
            if ($testimonial->client_image && Storage::disk('public')->exists($testimonial->client_image)) {
                Storage::disk('public')->delete($testimonial->client_image);
            }
        });
    }
}
