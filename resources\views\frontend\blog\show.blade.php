@extends('frontend.layouts.app')

@section('title', $post->meta_title)
@section('description', $post->meta_description)
@section('keywords', $post->meta_keywords ?: implode(', ', $post->tags ?: []))

@section('og_title', $post->og_title ?: $post->title)
@section('og_description', $post->og_description ?: $post->excerpt)
@section('og_image', $post->og_image ?: $post->featured_image_url)

@section('twitter_title', $post->twitter_title ?: $post->title)
@section('twitter_description', $post->twitter_description ?: $post->excerpt)
@section('twitter_image', $post->twitter_image ?: $post->featured_image_url)

@if($post->canonical_url)
@section('canonical', $post->canonical_url)
@endif

@push('structured_data')
@if($post->structured_data)
<script type="application/ld+json">
{!! json_encode($post->structured_data) !!}
</script>
@else
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BlogPosting",
  "headline": "{{ $post->title }}",
  "description": "{{ $post->excerpt }}",
  "image": "{{ $post->featured_image_url }}",
  "author": {
    "@type": "Person",
    "name": "{{ $post->author_name ?: 'GrandTek IT Solutions' }}"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GrandTek IT Solutions",
    "logo": {
      "@type": "ImageObject",
      "url": "{{ asset('assets/img/grandtek-logo.png') }}"
    }
  },
  "datePublished": "{{ $post->published_at->toISOString() }}",
  "dateModified": "{{ $post->updated_at->toISOString() }}",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "{{ route('blog.show', $post->slug) }}"
  }
}
</script>
@endif
@endpush

@section('content')
<div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
    <div class="container py-5">
        <h1 class="display-4 text-white mb-3 animated slideInDown">{{ $post->title }}</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb text-uppercase mb-0">
                <li class="breadcrumb-item"><a class="text-white" href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a class="text-white" href="{{ route('blog.index') }}">Blog</a></li>
                <li class="breadcrumb-item text-primary active" aria-current="page">{{ Str::limit($post->title, 30) }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container-fluid py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <article class="blog-post">
                    <!-- Featured Image -->
                    <div class="mb-4">
                        <img src="{{ $post->featured_image_url }}" class="img-fluid rounded" alt="{{ $post->title }}">
                    </div>

                    <!-- Post Meta -->
                    <div class="post-meta mb-4 p-3 bg-light rounded">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-user me-2"></i>{{ $post->author_name ?: 'Administrator' }}
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-2"></i>{{ $post->formatted_published_date }}
                                </small>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <small class="text-muted">
                                    <i class="fas fa-eye me-2"></i>{{ $post->views_count }} Views
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-2"></i>{{ $post->reading_time }} min read
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Categories and Tags -->
                    @if($post->categories || $post->tags)
                    <div class="mb-4">
                        @if($post->categories)
                            @foreach($post->categories as $category)
                                <a href="{{ route('blog.category', $category) }}" class="badge bg-primary text-decoration-none me-2">{{ $category }}</a>
                            @endforeach
                        @endif
                        @if($post->tags)
                            @foreach($post->tags as $tag)
                                <a href="{{ route('blog.tag', $tag) }}" class="badge bg-secondary text-decoration-none me-2">#{{ $tag }}</a>
                            @endforeach
                        @endif
                    </div>
                    @endif

                    <!-- Post Content -->
                    <div class="post-content">
                        {!! $post->content !!}
                    </div>

                    <!-- Share Buttons -->
                    <div class="share-buttons mt-5 p-3 bg-light rounded">
                        <h6 class="mb-3">Share this post:</h6>
                        <div class="d-flex gap-2">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('blog.show', $post->slug)) }}"
                               target="_blank" class="btn btn-primary btn-sm">
                                <i class="fab fa-facebook-f me-1"></i>Facebook
                            </a>
                            <a href="https://twitter.com/intent/tweet?url={{ urlencode(route('blog.show', $post->slug)) }}&text={{ urlencode($post->title) }}"
                               target="_blank" class="btn btn-info btn-sm">
                                <i class="fab fa-twitter me-1"></i>Twitter
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(route('blog.show', $post->slug)) }}"
                               target="_blank" class="btn btn-primary btn-sm">
                                <i class="fab fa-linkedin me-1"></i>LinkedIn
                            </a>
                            <a href="https://wa.me/?text={{ urlencode($post->title . ' - ' . route('blog.show', $post->slug)) }}"
                               target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </a>
                        </div>
                    </div>
                </article>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Related Posts -->
                @if($relatedPosts && $relatedPosts->count() > 0)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Related Posts</h5>
                    </div>
                    <div class="card-body">
                        @foreach($relatedPosts as $relatedPost)
                        <div class="d-flex mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                            <img src="{{ $relatedPost->featured_image_url }}" class="me-3 rounded"
                                 alt="{{ $relatedPost->title }}" style="width: 80px; height: 60px; object-fit: cover;">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{{ route('blog.show', $relatedPost->slug) }}" class="text-decoration-none">
                                        {{ Str::limit($relatedPost->title, 50) }}
                                    </a>
                                </h6>
                                <small class="text-muted">{{ $relatedPost->formatted_published_date }}</small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Back to Blog -->
                <div class="card">
                    <div class="card-body text-center">
                        <h6>Explore More</h6>
                        <a href="{{ route('blog.index') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Blog
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
