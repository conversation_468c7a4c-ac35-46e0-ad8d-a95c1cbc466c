@extends('admin.layouts.app')

@section('title', $page->exists ? 'Edit Page SEO' : 'Create Page SEO')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">{{ $page->exists ? 'Edit Page SEO' : 'Create Page SEO' }}</h1>
        <a href="{{ route('admin.seo.pages') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Pages
        </a>
    </div>

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <form action="{{ $page->exists ? route('admin.seo.pages.update', $page->id) : route('admin.seo.pages.store') }}"
          method="POST">
        @csrf
        @if($page->exists)
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="page_type" class="form-label">Page Type *</label>
                                <select name="page_type" id="page_type" class="form-select" required>
                                    <option value="">Select Page Type</option>
                                    @foreach($pageTypes as $key => $label)
                                        <option value="{{ $key }}"
                                                {{ old('page_type', $page->page_type) === $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="page_identifier" class="form-label">Page Identifier *</label>
                                <input type="text" name="page_identifier" id="page_identifier"
                                       class="form-control" required
                                       value="{{ old('page_identifier', $page->page_identifier) }}"
                                       placeholder="e.g., home, about, contact">
                                <div class="form-text">Unique identifier for the page (route name, slug, etc.)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Meta Tags -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Meta Tags</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">Meta Title</label>
                            <input type="text" name="title" id="title" class="form-control"
                                   maxlength="60" value="{{ old('title', $page->title) }}"
                                   placeholder="Optimized title for search engines">
                            <div class="form-text">
                                <span id="titleCount">0</span>/60 characters. Keep it under 60 for best results.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Meta Description</label>
                            <textarea name="description" id="description" class="form-control"
                                      rows="3" maxlength="160"
                                      placeholder="Compelling description that appears in search results">{{ old('description', $page->description) }}</textarea>
                            <div class="form-text">
                                <span id="descriptionCount">0</span>/160 characters. Keep it under 160 for best results.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="keywords" class="form-label">Keywords</label>
                            <textarea name="keywords" id="keywords" class="form-control"
                                      rows="2" placeholder="Comma-separated keywords">{{ old('keywords', $page->keywords) }}</textarea>
                            <div class="form-text">Separate keywords with commas</div>
                        </div>

                        <div class="mb-3">
                            <label for="canonical_url" class="form-label">Canonical URL</label>
                            <input type="url" name="canonical_url" id="canonical_url" class="form-control"
                                   value="{{ old('canonical_url', $page->canonical_url) }}"
                                   placeholder="https://example.com/page">
                            <div class="form-text">Leave empty to use default URL</div>
                        </div>
                    </div>
                </div>

                <!-- Open Graph -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Open Graph (Facebook)</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="og_title" class="form-label">OG Title</label>
                            <input type="text" name="og_title" id="og_title" class="form-control"
                                   maxlength="60" value="{{ old('og_title', $page->og_title) }}">
                            <div class="form-text">Leave empty to use meta title</div>
                        </div>

                        <div class="mb-3">
                            <label for="og_description" class="form-label">OG Description</label>
                            <textarea name="og_description" id="og_description" class="form-control"
                                      rows="3" maxlength="160">{{ old('og_description', $page->og_description) }}</textarea>
                            <div class="form-text">Leave empty to use meta description</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="og_image" class="form-label">OG Image URL</label>
                                <input type="url" name="og_image" id="og_image" class="form-control"
                                       value="{{ old('og_image', $page->og_image) }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="og_type" class="form-label">OG Type</label>
                                <select name="og_type" id="og_type" class="form-select">
                                    <option value="website" {{ old('og_type', $page->og_type) === 'website' ? 'selected' : '' }}>Website</option>
                                    <option value="article" {{ old('og_type', $page->og_type) === 'article' ? 'selected' : '' }}>Article</option>
                                    <option value="product" {{ old('og_type', $page->og_type) === 'product' ? 'selected' : '' }}>Product</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Twitter Cards -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Twitter Cards</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="twitter_title" class="form-label">Twitter Title</label>
                            <input type="text" name="twitter_title" id="twitter_title" class="form-control"
                                   maxlength="60" value="{{ old('twitter_title', $page->twitter_title) }}">
                            <div class="form-text">Leave empty to use OG title</div>
                        </div>

                        <div class="mb-3">
                            <label for="twitter_description" class="form-label">Twitter Description</label>
                            <textarea name="twitter_description" id="twitter_description" class="form-control"
                                      rows="3" maxlength="160">{{ old('twitter_description', $page->twitter_description) }}</textarea>
                            <div class="form-text">Leave empty to use OG description</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="twitter_image" class="form-label">Twitter Image URL</label>
                                <input type="url" name="twitter_image" id="twitter_image" class="form-control"
                                       value="{{ old('twitter_image', $page->twitter_image) }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="twitter_card" class="form-label">Twitter Card Type</label>
                                <select name="twitter_card" id="twitter_card" class="form-select">
                                    <option value="summary" {{ old('twitter_card', $page->twitter_card) === 'summary' ? 'selected' : '' }}>Summary</option>
                                    <option value="summary_large_image" {{ old('twitter_card', $page->twitter_card) === 'summary_large_image' ? 'selected' : '' }}>Summary Large Image</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Settings -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="index" id="index" class="form-check-input"
                                       value="1" {{ old('index', $page->index) ? 'checked' : '' }}>
                                <label class="form-check-label" for="index">
                                    Allow search engines to index this page
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="follow" id="follow" class="form-check-input"
                                       value="1" {{ old('follow', $page->follow) ? 'checked' : '' }}>
                                <label class="form-check-label" for="follow">
                                    Allow search engines to follow links
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" id="is_active" class="form-check-input"
                                       value="1" {{ old('is_active', $page->is_active ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sitemap Settings -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Sitemap Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="sitemap_priority" class="form-label">Priority</label>
                            <select name="sitemap_priority" id="sitemap_priority" class="form-select">
                                <option value="0.1" {{ old('sitemap_priority', $page->sitemap_priority) == '0.1' ? 'selected' : '' }}>0.1 - Lowest</option>
                                <option value="0.3" {{ old('sitemap_priority', $page->sitemap_priority) == '0.3' ? 'selected' : '' }}>0.3 - Low</option>
                                <option value="0.5" {{ old('sitemap_priority', $page->sitemap_priority ?? '0.5') == '0.5' ? 'selected' : '' }}>0.5 - Normal</option>
                                <option value="0.7" {{ old('sitemap_priority', $page->sitemap_priority) == '0.7' ? 'selected' : '' }}>0.7 - High</option>
                                <option value="0.9" {{ old('sitemap_priority', $page->sitemap_priority) == '0.9' ? 'selected' : '' }}>0.9 - Highest</option>
                                <option value="1.0" {{ old('sitemap_priority', $page->sitemap_priority) == '1.0' ? 'selected' : '' }}>1.0 - Critical</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="sitemap_changefreq" class="form-label">Change Frequency</label>
                            <select name="sitemap_changefreq" id="sitemap_changefreq" class="form-select">
                                @foreach($changeFrequencies as $key => $label)
                                    <option value="{{ $key }}"
                                            {{ old('sitemap_changefreq', $page->sitemap_changefreq ?? 'weekly') === $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {{ $page->exists ? 'Update Page SEO' : 'Create Page SEO' }}
                            </button>
                            <a href="{{ route('admin.seo.pages') }}" class="btn btn-outline-secondary">
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counters
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const titleCount = document.getElementById('titleCount');
    const descriptionCount = document.getElementById('descriptionCount');

    function updateCount(input, counter) {
        counter.textContent = input.value.length;
        if (input === titleInput && input.value.length > 60) {
            counter.style.color = 'red';
        } else if (input === descriptionInput && input.value.length > 160) {
            counter.style.color = 'red';
        } else {
            counter.style.color = '';
        }
    }

    titleInput.addEventListener('input', () => updateCount(titleInput, titleCount));
    descriptionInput.addEventListener('input', () => updateCount(descriptionInput, descriptionCount));

    // Initial count
    updateCount(titleInput, titleCount);
    updateCount(descriptionInput, descriptionCount);
});
</script>
@endpush
