@extends('admin.layouts.app')

@section('title', 'SEO Settings')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">SEO Settings</h1>
        <a href="{{ route('admin.seo.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Settings Form -->
    <form action="{{ route('admin.seo.settings.update') }}" method="POST">
        @csrf
        
        @foreach($groups as $groupKey => $groupName)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ $groupName }}</h6>
                </div>
                <div class="card-body">
                    @if(isset($settings[$groupKey]) && $settings[$groupKey]->count() > 0)
                        <div class="row">
                            @foreach($settings[$groupKey] as $setting)
                                <div class="col-md-6 mb-3">
                                    <label for="setting_{{ $setting->key }}" class="form-label">
                                        {{ $setting->label }}
                                        @if($setting->description)
                                            <i class="fas fa-info-circle text-muted ms-1" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ $setting->description }}"></i>
                                        @endif
                                    </label>
                                    
                                    @if($setting->type === 'textarea')
                                        <textarea name="settings[{{ $setting->key }}]" 
                                                  id="setting_{{ $setting->key }}" 
                                                  class="form-control" 
                                                  rows="3">{{ old('settings.' . $setting->key, $setting->value) }}</textarea>
                                    @elseif($setting->type === 'boolean')
                                        <div class="form-check">
                                            <input type="hidden" name="settings[{{ $setting->key }}]" value="0">
                                            <input type="checkbox" 
                                                   name="settings[{{ $setting->key }}]" 
                                                   id="setting_{{ $setting->key }}" 
                                                   class="form-check-input" 
                                                   value="1"
                                                   {{ old('settings.' . $setting->key, $setting->value) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="setting_{{ $setting->key }}">
                                                Enable
                                            </label>
                                        </div>
                                    @elseif($setting->type === 'url')
                                        <input type="url" 
                                               name="settings[{{ $setting->key }}]" 
                                               id="setting_{{ $setting->key }}" 
                                               class="form-control" 
                                               value="{{ old('settings.' . $setting->key, $setting->value) }}">
                                    @elseif($setting->type === 'email')
                                        <input type="email" 
                                               name="settings[{{ $setting->key }}]" 
                                               id="setting_{{ $setting->key }}" 
                                               class="form-control" 
                                               value="{{ old('settings.' . $setting->key, $setting->value) }}">
                                    @else
                                        <input type="text" 
                                               name="settings[{{ $setting->key }}]" 
                                               id="setting_{{ $setting->key }}" 
                                               class="form-control" 
                                               value="{{ old('settings.' . $setting->key, $setting->value) }}">
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted mb-0">No settings found for this group.</p>
                    @endif
                </div>
            </div>
        @endforeach

        <!-- Save Button -->
        <div class="card shadow">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Settings
                    </button>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSettingModal">
                        <i class="fas fa-plus me-2"></i>Add New Setting
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Add Setting Modal -->
<div class="modal fade" id="addSettingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.seo.settings.create') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add New SEO Setting</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="key" class="form-label">Key</label>
                        <input type="text" name="key" id="key" class="form-control" required>
                        <div class="form-text">Unique identifier for the setting (e.g., site_name)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="label" class="form-label">Label</label>
                        <input type="text" name="label" id="label" class="form-control" required>
                        <div class="form-text">Display name for the setting</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select name="type" id="type" class="form-select" required>
                            <option value="text">Text</option>
                            <option value="textarea">Textarea</option>
                            <option value="url">URL</option>
                            <option value="email">Email</option>
                            <option value="boolean">Boolean</option>
                            <option value="integer">Integer</option>
                            <option value="json">JSON</option>
                            <option value="array">Array</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="group" class="form-label">Group</label>
                        <select name="group" id="group" class="form-select" required>
                            @foreach($groups as $groupKey => $groupName)
                                <option value="{{ $groupKey }}">{{ $groupName }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="value" class="form-label">Default Value</label>
                        <input type="text" name="value" id="value" class="form-control">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="2"></textarea>
                        <div class="form-text">Optional description for the setting</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Setting</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endpush
