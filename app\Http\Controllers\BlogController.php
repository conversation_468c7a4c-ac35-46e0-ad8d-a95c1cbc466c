<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BlogPost;
use App\Services\SEOService;

class BlogController extends Controller
{
    /**
     * Display blog index page
     */
    public function index(Request $request)
    {
        $query = BlogPost::published()->latest();
        
        // Filter by category if provided
        if ($request->has('category') && $request->category) {
            $query->whereJsonContains('categories', $request->category);
        }
        
        // Filter by tag if provided
        if ($request->has('tag') && $request->tag) {
            $query->whereJsonContains('tags', $request->tag);
        }
        
        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('excerpt', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%");
            });
        }
        
        $posts = $query->paginate(12);
        $featuredPosts = BlogPost::published()->featured()->latest()->limit(3)->get();
        
        // Get all categories and tags for filters
        $allPosts = BlogPost::published()->get();
        $categories = $allPosts->pluck('categories')->flatten()->unique()->filter()->values();
        $tags = $allPosts->pluck('tags')->flatten()->unique()->filter()->values();
        
        return view('frontend.blog.index', compact('posts', 'featuredPosts', 'categories', 'tags'));
    }

    /**
     * Display a specific blog post
     */
    public function show(BlogPost $post)
    {
        // Check if post is published
        if ($post->status !== 'published' || $post->published_at > now()) {
            abort(404);
        }
        
        // Increment views
        $post->incrementViews();
        
        // Get related posts
        $relatedPosts = $post->getRelatedPosts(3);
        
        return view('frontend.blog.show', compact('post', 'relatedPosts'));
    }

    /**
     * Display posts by category
     */
    public function category(Request $request, $category)
    {
        $posts = BlogPost::published()
                        ->whereJsonContains('categories', $category)
                        ->latest()
                        ->paginate(12);
        
        return view('frontend.blog.category', compact('posts', 'category'));
    }

    /**
     * Display posts by tag
     */
    public function tag(Request $request, $tag)
    {
        $posts = BlogPost::published()
                        ->whereJsonContains('tags', $tag)
                        ->latest()
                        ->paginate(12);
        
        return view('frontend.blog.tag', compact('posts', 'tag'));
    }

    /**
     * Search blog posts
     */
    public function search(Request $request)
    {
        $searchTerm = $request->get('q', '');
        
        if (empty($searchTerm)) {
            return redirect()->route('blog.index');
        }
        
        $posts = BlogPost::published()
                        ->where(function ($query) use ($searchTerm) {
                            $query->where('title', 'like', "%{$searchTerm}%")
                                  ->orWhere('excerpt', 'like', "%{$searchTerm}%")
                                  ->orWhere('content', 'like', "%{$searchTerm}%");
                        })
                        ->latest()
                        ->paginate(12);
        
        return view('frontend.blog.search', compact('posts', 'searchTerm'));
    }

    /**
     * Generate RSS feed
     */
    public function rss()
    {
        $posts = BlogPost::published()->latest()->limit(20)->get();
        
        $rss = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $rss .= '<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">' . "\n";
        $rss .= '<channel>' . "\n";
        $rss .= '<title>GrandTek IT Solutions Blog</title>' . "\n";
        $rss .= '<description>Latest insights on software development, technology trends, and IT solutions in Kenya</description>' . "\n";
        $rss .= '<link>' . url('/') . '</link>' . "\n";
        $rss .= '<atom:link href="' . route('blog.rss') . '" rel="self" type="application/rss+xml" />' . "\n";
        $rss .= '<language>en-ke</language>' . "\n";
        $rss .= '<lastBuildDate>' . now()->toRSSString() . '</lastBuildDate>' . "\n";
        
        foreach ($posts as $post) {
            $rss .= '<item>' . "\n";
            $rss .= '<title><![CDATA[' . $post->title . ']]></title>' . "\n";
            $rss .= '<description><![CDATA[' . $post->excerpt . ']]></description>' . "\n";
            $rss .= '<link>' . route('blog.show', $post->slug) . '</link>' . "\n";
            $rss .= '<guid>' . route('blog.show', $post->slug) . '</guid>' . "\n";
            $rss .= '<pubDate>' . $post->published_at->toRSSString() . '</pubDate>' . "\n";
            $rss .= '<author>' . $post->author_email . ' (' . $post->author_name . ')</author>' . "\n";
            
            if ($post->categories) {
                foreach ($post->categories as $category) {
                    $rss .= '<category>' . $category . '</category>' . "\n";
                }
            }
            
            $rss .= '</item>' . "\n";
        }
        
        $rss .= '</channel>' . "\n";
        $rss .= '</rss>';
        
        return response($rss, 200)
            ->header('Content-Type', 'application/rss+xml');
    }
}
