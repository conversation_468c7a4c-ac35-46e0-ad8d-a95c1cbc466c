@extends('backend.layouts.app')

@section('title', 'Site Settings - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Site Settings</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Site Settings</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Contact Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('store.sitesettings') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control @error('location') is-invalid @enderror" 
                               id="location" name="location" value="{{ $sitesetting->location ?? old('location') }}" 
                               placeholder="Enter company location" required>
                        @error('location')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control @error('email') is-invalid @enderror" 
                               id="email" name="email" value="{{ $sitesetting->email ?? old('email') }}" 
                               placeholder="Enter company email" required>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="advert_slider" class="form-label">Advertisement Slider Text</label>
                        <input type="text" class="form-control @error('advert_slider') is-invalid @enderror" 
                               id="advert_slider" name="advert_slider" value="{{ $sitesetting->slider_advert ?? old('advert_slider') }}" 
                               placeholder="Enter slider advertisement text" required>
                        @error('advert_slider')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <h6 class="mb-3">Social Media Links</h6>
                    
                    <div class="mb-3">
                        <label for="facebook" class="form-label">Facebook URL</label>
                        <input type="url" class="form-control @error('facebook') is-invalid @enderror" 
                               id="facebook" name="facebook" value="{{ $sitesetting->facebook ?? old('facebook') }}" 
                               placeholder="https://facebook.com/grandtek" required>
                        @error('facebook')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="twitter" class="form-label">Twitter URL</label>
                        <input type="url" class="form-control @error('twitter') is-invalid @enderror" 
                               id="twitter" name="twitter" value="{{ $sitesetting->twitter ?? old('twitter') }}" 
                               placeholder="https://twitter.com/grandtek" required>
                        @error('twitter')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="instagram" class="form-label">Instagram URL</label>
                        <input type="url" class="form-control @error('instagram') is-invalid @enderror" 
                               id="instagram" name="instagram" value="{{ $sitesetting->instagram ?? old('instagram') }}" 
                               placeholder="https://instagram.com/grandtek" required>
                        @error('instagram')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="linkedin" class="form-label">LinkedIn URL</label>
                        <input type="url" class="form-control @error('linkedin') is-invalid @enderror" 
                               id="linkedin" name="linkedin" value="{{ $sitesetting->linkedin ?? old('linkedin') }}" 
                               placeholder="https://linkedin.com/company/grandtek" required>
                        @error('linkedin')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Update Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Company Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('store.companyinfo') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="company_name" class="form-label">Company Name</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="company_name" name="name" value="{{ old('name') }}" 
                               placeholder="Enter company name" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="company_phone" class="form-label">Company Phone</label>
                        <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                               id="company_phone" name="phone" value="{{ old('phone') }}" 
                               placeholder="Enter company phone number" required>
                        @error('phone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Update Company Info
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Settings Guidelines</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Important Notes:</h6>
                    <ul class="mb-0">
                        <li>Contact information appears on the frontend</li>
                        <li>Social media links should be complete URLs</li>
                        <li>Advertisement text appears in the top slider</li>
                        <li>Changes take effect immediately</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
