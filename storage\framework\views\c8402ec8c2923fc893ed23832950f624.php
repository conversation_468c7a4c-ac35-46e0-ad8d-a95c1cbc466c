<?php if(isset($projects) && $projects && $projects->count() > 0): ?>
<div class="container-fluid project py-5 mb-5" id="project">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Projects</h5>
            <h1>Our Recently Completed Projects</h1>
        </div>

        <div class="row g-5">
            <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6 col-lg-4 wow fadeIn" data-wow-delay="<?php echo e(0.3 + ($index * 0.2)); ?>s">
                    <div class="project-item">
                        <div class="project-img">
                            <?php if($project['project_image_url']): ?>
                                <img src="<?php echo e($project['project_image_url']); ?>" class="img-fluid w-100 rounded" alt="<?php echo e($project['title']); ?>">
                            <?php else: ?>
                                <div class="img-fluid w-100 rounded bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                    <i class="ri-image-line text-muted" style="font-size: 3rem;"></i>
                                </div>
                            <?php endif; ?>
                            <div class="project-content">
                                <a href="<?php echo e(route('project.detail', $project['slug'])); ?>" class="text-center">
                                    <h4 class="text-secondary"><?php echo e($project['title']); ?></h4>
                                    <p class="m-0 text-white"><?php echo e($project['category'] ?? $project['short_description']); ?></p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- View All Projects Button -->
        <div class="text-center mt-5 wow fadeIn" data-wow-delay=".9s">
            <a href="<?php echo e(route('projects.all')); ?>" class="btn btn-primary btn-lg px-5 py-3">
                <i class="ri-eye-line me-2"></i>View All Projects
            </a>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/include/projects.blade.php ENDPATH**/ ?>