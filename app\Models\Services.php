<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Services extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'icon_class',
        'service_image',
        'button_text',
        'button_link',
        'status',
        'sort_order',
    ];

    protected $casts = [
        'sort_order' => 'integer',
    ];

    /**
     * Get the full URL for service image
     */
    public function getServiceImageUrlAttribute()
    {
        if ($this->service_image) {
            // Check if it's already a full URL
            if (filter_var($this->service_image, FILTER_VALIDATE_URL)) {
                return $this->service_image;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->service_image)) {
                return Storage::url($this->service_image);
            }
            // Fallback to asset path
            return asset($this->service_image);
        }
        return null;
    }

    /**
     * Scope to get only active services
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get services ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Generate a unique slug from the name
     */
    public function generateSlug()
    {
        $slug = Str::slug($this->name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Generate slug when creating
        static::creating(function ($service) {
            if (empty($service->slug)) {
                $service->slug = $service->generateSlug();
            }
        });

        // Update slug when name changes
        static::updating(function ($service) {
            if ($service->isDirty('name') && empty($service->slug)) {
                $service->slug = $service->generateSlug();
            }
        });

        // Clean up files when deleting
        static::deleting(function ($service) {
            if ($service->service_image && Storage::disk('public')->exists($service->service_image)) {
                Storage::disk('public')->delete($service->service_image);
            }
        });
    }
}
