<?php if(isset($teams) && $teams && $teams->count() > 0): ?>
<div class="container-fluid py-5 mb-5 team">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Team</h5>
            <h1>Meet our expert Team</h1>
        </div>
        <div class="owl-carousel team-carousel wow fadeIn" data-wow-delay=".5s">
            <?php $__currentLoopData = $teams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="rounded team-item">
                    <div class="team-content">
                        <div class="team-img-icon">
                            <div class="team-img rounded-circle">
                                <?php if($team['team_image_url']): ?>
                                    <img src="<?php echo e($team['team_image_url']); ?>" class="img-fluid w-100 rounded-circle" alt="<?php echo e($team['name']); ?>">
                                <?php else: ?>
                                    <div class="img-fluid w-100 rounded-circle bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="ri-user-line text-muted" style="font-size: 3rem;"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="team-name text-center py-3">
                                <h4 class=""><?php echo e($team['name']); ?></h4>
                                <p class="m-0"><?php echo e($team['designation']); ?></p>
                            </div>
                            <div class="team-icon d-flex justify-content-center pb-4">
                                <?php if($team['facebook']): ?>
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="<?php echo e($team['facebook']); ?>" target="_blank">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if($team['twitter']): ?>
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="<?php echo e($team['twitter']); ?>" target="_blank">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if($team['instagram']): ?>
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="<?php echo e($team['instagram']); ?>" target="_blank">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if($team['linkedin']): ?>
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="<?php echo e($team['linkedin']); ?>" target="_blank">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/include/team.blade.php ENDPATH**/ ?>