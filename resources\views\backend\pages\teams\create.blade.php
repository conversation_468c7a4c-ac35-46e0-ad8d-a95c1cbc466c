@extends('backend.layouts.app')

@section('title', 'Add New Team Member')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Add New Team Member</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('teams.index') }}">Team</a></li>
                    <li class="breadcrumb-item active">Add New</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Team Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Team Member Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('teams.store') }}" method="POST" enctype="multipart/form-data" id="teamForm">
                    @csrf
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Name -->
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Designation -->
                                    <div class="mb-3">
                                        <label for="designation" class="form-label">Designation/Position <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('designation') is-invalid @enderror" 
                                               id="designation" name="designation" value="{{ old('designation') }}" required>
                                        @error('designation')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Bio -->
                                    <div class="mb-3">
                                        <label for="bio" class="form-label">Bio/Description</label>
                                        <textarea class="form-control @error('bio') is-invalid @enderror" 
                                                  id="bio" name="bio" rows="4" maxlength="1000">{{ old('bio') }}</textarea>
                                        <div class="form-text">Brief description about the team member (max 1000 characters)</div>
                                        @error('bio')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Contact Information -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email Address</label>
                                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                       id="email" name="email" value="{{ old('email') }}">
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">Phone Number</label>
                                                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                       id="phone" name="phone" value="{{ old('phone') }}">
                                                @error('phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Media Links -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Social Media Links</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="facebook" class="form-label">Facebook URL</label>
                                                <input type="url" class="form-control @error('facebook') is-invalid @enderror" 
                                                       id="facebook" name="facebook" value="{{ old('facebook') }}" 
                                                       placeholder="https://facebook.com/username">
                                                @error('facebook')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="twitter" class="form-label">Twitter URL</label>
                                                <input type="url" class="form-control @error('twitter') is-invalid @enderror" 
                                                       id="twitter" name="twitter" value="{{ old('twitter') }}" 
                                                       placeholder="https://twitter.com/username">
                                                @error('twitter')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="instagram" class="form-label">Instagram URL</label>
                                                <input type="url" class="form-control @error('instagram') is-invalid @enderror" 
                                                       id="instagram" name="instagram" value="{{ old('instagram') }}" 
                                                       placeholder="https://instagram.com/username">
                                                @error('instagram')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="linkedin" class="form-label">LinkedIn URL</label>
                                                <input type="url" class="form-control @error('linkedin') is-invalid @enderror" 
                                                       id="linkedin" name="linkedin" value="{{ old('linkedin') }}" 
                                                       placeholder="https://linkedin.com/in/username">
                                                @error('linkedin')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Sidebar -->
                        <div class="col-lg-4">
                            <!-- Team Image -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Profile Image</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="team_image" class="form-label">Team Member Photo</label>
                                        <input type="file" class="form-control @error('team_image') is-invalid @enderror" 
                                               id="team_image" name="team_image" accept="image/*">
                                        <div class="form-text">Upload team member photo (JPEG, PNG, JPG, GIF, WebP - Max: 5MB)</div>
                                        @error('team_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div id="image_preview" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                            <option value="active" {{ old('status') === 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        </select>
                                        <div class="form-text">Active team members will be displayed on the website</div>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('teams.index') }}" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Team
                                </a>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Add Team Member
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview functionality
    document.getElementById('team_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('image_preview');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" class="image-preview" alt="Team Member Preview">`;
            };
            reader.readAsDataURL(file);
        } else {
            preview.innerHTML = '';
        }
    });

    // Form validation
    document.getElementById('teamForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const designation = document.getElementById('designation').value.trim();
        const status = document.getElementById('status').value;

        if (!name || !designation || !status) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ri-loader-4-line me-1 spinner-border spinner-border-sm"></i>Adding...';
    });

    // Character counter for bio
    const bioTextarea = document.getElementById('bio');
    if (bioTextarea) {
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        bioTextarea.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = 1000 - bioTextarea.value.length;
            counter.textContent = `${bioTextarea.value.length}/1000 characters`;
            counter.className = `form-text text-end ${remaining < 100 ? 'text-warning' : remaining < 0 ? 'text-danger' : ''}`;
        }

        bioTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>
@endpush
