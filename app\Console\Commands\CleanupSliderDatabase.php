<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\HomepageSlider;
use App\Models\Sliders;

class CleanupSliderDatabase extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sliders:cleanup {--force : Force cleanup without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old slider database tables and migrate data to homepage_sliders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Starting Slider Database Cleanup...');
        
        if (!$this->option('force')) {
            if (!$this->confirm('This will clean up old slider data. Do you want to continue?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        // Step 1: Check current database state
        $this->checkDatabaseState();

        // Step 2: Migrate data from old sliders table to homepage_sliders
        $this->migrateOldSliderData();

        // Step 3: Clean up old tables
        $this->cleanupOldTables();

        // Step 4: Optimize homepage_sliders table
        $this->optimizeHomepageSliders();

        $this->info('✅ Database cleanup completed successfully!');
        return 0;
    }

    private function checkDatabaseState()
    {
        $this->info('📊 Checking current database state...');
        
        $tables = [
            'sliders' => Schema::hasTable('sliders') ? DB::table('sliders')->count() : 0,
            'homepage_sliders' => Schema::hasTable('homepage_sliders') ? DB::table('homepage_sliders')->count() : 0,
        ];

        $this->table(['Table', 'Record Count'], [
            ['sliders (old)', $tables['sliders']],
            ['homepage_sliders (new)', $tables['homepage_sliders']],
        ]);
    }

    private function migrateOldSliderData()
    {
        if (!Schema::hasTable('sliders')) {
            $this->warn('⚠️  Old sliders table not found. Skipping migration.');
            return;
        }

        $oldSliders = DB::table('sliders')->get();
        
        if ($oldSliders->isEmpty()) {
            $this->info('ℹ️  No data found in old sliders table.');
            return;
        }

        $this->info("🔄 Migrating {$oldSliders->count()} sliders...");
        $bar = $this->output->createProgressBar($oldSliders->count());
        $bar->start();

        $migrated = 0;
        foreach ($oldSliders as $slider) {
            // Check if slider already exists in homepage_sliders
            $exists = DB::table('homepage_sliders')
                ->where('title', $slider->first_slogan_one)
                ->exists();

            if (!$exists) {
                DB::table('homepage_sliders')->insert([
                    'title' => $slider->first_slogan_one ?? 'Migrated Slider',
                    'subtitle' => $slider->second_slogan_one ?? '',
                    'description' => $slider->first_slogan_two ?? '',
                    'secondary_description' => $slider->second_slogan_two ?? '',
                    'image_path' => $slider->slider_one_img ?? null,
                    'secondary_image_path' => $slider->slogan_two_img ?? null,
                    'media_type' => 'image',
                    'status' => $slider->status ?? 'active',
                    'sort_order' => $slider->sort_order ?? 0,
                    'background_color' => '#000000',
                    'text_color' => '#ffffff',
                    'text_alignment' => 'center',
                    'created_at' => $slider->created_at ?? now(),
                    'updated_at' => $slider->updated_at ?? now(),
                ]);
                $migrated++;
            }
            
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("✅ Successfully migrated {$migrated} sliders to homepage_sliders table.");
    }

    private function cleanupOldTables()
    {
        $this->info('🗑️  Cleaning up old tables...');
        
        $tablesToCleanup = [
            'sliders' => 'truncate', // Keep structure, remove data
            'slider_images' => 'drop',
            'slider_settings' => 'drop',
            'old_sliders' => 'drop',
        ];

        foreach ($tablesToCleanup as $table => $action) {
            if (Schema::hasTable($table)) {
                if ($action === 'truncate') {
                    DB::table($table)->truncate();
                    $this->info("🧹 Truncated table: {$table}");
                } else {
                    Schema::dropIfExists($table);
                    $this->info("🗑️  Dropped table: {$table}");
                }
            }
        }
    }

    private function optimizeHomepageSliders()
    {
        $this->info('⚡ Optimizing homepage_sliders table...');
        
        // Reset sort orders
        $sliders = DB::table('homepage_sliders')->orderBy('created_at')->get();
        foreach ($sliders as $index => $slider) {
            DB::table('homepage_sliders')
                ->where('id', $slider->id)
                ->update(['sort_order' => $index + 1]);
        }

        // Update any null statuses
        DB::table('homepage_sliders')
            ->whereNull('status')
            ->update(['status' => 'active']);

        $this->info('✅ Homepage sliders table optimized.');
    }
}
