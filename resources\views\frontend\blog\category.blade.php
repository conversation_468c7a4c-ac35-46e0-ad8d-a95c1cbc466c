@extends('frontend.layouts.app')

@section('title', 'Blog Category: ' . $category . ' | GrandTek IT Solutions')
@section('description', 'Browse blog posts in the ' . $category . ' category. Expert insights and articles about software development and technology from GrandTek IT Solutions.')
@section('keywords', $category . ', blog category, software development, technology articles, IT solutions Kenya')

@section('content')
<div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
    <div class="container py-5">
        <h1 class="display-4 text-white mb-3 animated slideInDown">{{ $category }}</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb text-uppercase mb-0">
                <li class="breadcrumb-item"><a class="text-white" href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a class="text-white" href="{{ route('blog.index') }}">Blog</a></li>
                <li class="breadcrumb-item text-primary active" aria-current="page">{{ $category }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container-fluid py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h3>Posts in "{{ $category }}" ({{ $posts->total() }})</h3>
                    <a href="{{ route('blog.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>All Categories
                    </a>
                </div>
            </div>
        </div>

        @if($posts->count() > 0)
            <div class="row">
                @foreach($posts as $post)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <img src="{{ $post->featured_image_url }}" class="card-img-top" alt="{{ $post->title }}" style="height: 200px; object-fit: cover;">
                        <div class="card-body d-flex flex-column">
                            <span class="badge bg-primary mb-2 align-self-start">{{ $category }}</span>
                            <h5 class="card-title">{{ $post->title }}</h5>
                            <p class="card-text flex-grow-1">{{ Str::limit($post->excerpt, 120) }}</p>
                            <div class="mt-auto">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ $post->author_name ?: 'Administrator' }}
                                    <i class="fas fa-calendar ms-3 me-1"></i>{{ $post->formatted_published_date }}
                                </small>
                                <div class="d-flex justify-content-between align-items-center mt-2">
                                    <a href="{{ route('blog.show', $post->slug) }}" class="btn btn-primary btn-sm">Read More</a>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>{{ $post->views_count }}
                                        <i class="fas fa-clock ms-2 me-1"></i>{{ $post->reading_time }}min
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="row mt-5">
                <div class="col-12 d-flex justify-content-center">
                    {{ $posts->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h4>No posts found in this category</h4>
                <p class="text-muted">This category doesn't have any published posts yet.</p>
                <a href="{{ route('blog.index') }}" class="btn btn-primary">Browse All Posts</a>
            </div>
        @endif
    </div>
</div>
@endsection
