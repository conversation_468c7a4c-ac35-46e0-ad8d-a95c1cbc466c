<?php

namespace App\Http\Controllers;

use App\Models\Sliders;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class OriginalSliderController extends Controller
{
    /**
     * Display a listing of sliders
     */
    public function index()
    {
        try {
            $sliders = Sliders::ordered()->get();
            return view('backend.pages.original-sliders.index', compact('sliders'));
        } catch (\Exception $e) {
            \Log::error('Error loading sliders: ' . $e->getMessage());
            return view('backend.pages.original-sliders.index', ['sliders' => collect()]);
        }
    }

    /**
     * Show the form for creating a new slider
     */
    public function create()
    {
        return view('backend.pages.original-sliders.create');
    }

    /**
     * Store a newly created slider
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_slogan_one' => 'required|string|max:255',
            'second_slogan_one' => 'required|string|max:255',
            'slider_one_img' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'first_slogan_two' => 'required|string|max:255',
            'second_slogan_two' => 'required|string|max:255',
            'slogan_two_img' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'first_slogan_one', 'second_slogan_one', 'first_slogan_two', 'second_slogan_two', 'status'
        ]);

        // Handle file uploads
        if ($request->hasFile('slider_one_img')) {
            $data['slider_one_img'] = $request->file('slider_one_img')->store('sliders/images', 'public');
        }
        
        if ($request->hasFile('slogan_two_img')) {
            $data['slogan_two_img'] = $request->file('slogan_two_img')->store('sliders/images', 'public');
        }

        Sliders::create($data);

        return redirect()->route('sliders.index')
            ->with('success', 'Slider created successfully!');
    }

    /**
     * Display the specified slider
     */
    public function show(Sliders $slider)
    {
        return view('backend.pages.original-sliders.show', compact('slider'));
    }

    /**
     * Show the form for editing the specified slider
     */
    public function edit(Sliders $slider)
    {
        return view('backend.pages.original-sliders.edit', compact('slider'));
    }

    /**
     * Update the specified slider
     */
    public function update(Request $request, Sliders $slider)
    {
        $validator = Validator::make($request->all(), [
            'first_slogan_one' => 'required|string|max:255',
            'second_slogan_one' => 'required|string|max:255',
            'slider_one_img' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'first_slogan_two' => 'required|string|max:255',
            'second_slogan_two' => 'required|string|max:255',
            'slogan_two_img' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'first_slogan_one', 'second_slogan_one', 'first_slogan_two', 'second_slogan_two', 'status'
        ]);

        // Handle file uploads and cleanup
        if ($request->hasFile('slider_one_img')) {
            // Delete old image
            if ($slider->slider_one_img && Storage::disk('public')->exists($slider->slider_one_img)) {
                Storage::disk('public')->delete($slider->slider_one_img);
            }
            $data['slider_one_img'] = $request->file('slider_one_img')->store('sliders/images', 'public');
        }
        
        if ($request->hasFile('slogan_two_img')) {
            // Delete old image
            if ($slider->slogan_two_img && Storage::disk('public')->exists($slider->slogan_two_img)) {
                Storage::disk('public')->delete($slider->slogan_two_img);
            }
            $data['slogan_two_img'] = $request->file('slogan_two_img')->store('sliders/images', 'public');
        }

        $slider->update($data);

        return redirect()->route('sliders.index')
            ->with('success', 'Slider updated successfully!');
    }

    /**
     * Remove the specified slider
     */
    public function destroy(Sliders $slider)
    {
        try {
            // Delete associated files
            if ($slider->slider_one_img && Storage::disk('public')->exists($slider->slider_one_img)) {
                Storage::disk('public')->delete($slider->slider_one_img);
            }
            if ($slider->slogan_two_img && Storage::disk('public')->exists($slider->slogan_two_img)) {
                Storage::disk('public')->delete($slider->slogan_two_img);
            }
            
            $slider->delete();

            return redirect()->route('sliders.index')
                ->with('success', 'Slider deleted successfully!');
        } catch (\Exception $e) {
            \Log::error('Error deleting slider: ' . $e->getMessage());
            return redirect()->route('sliders.index')
                ->with('error', 'Failed to delete slider');
        }
    }

    /**
     * Toggle slider status
     */
    public function toggleStatus(Sliders $slider)
    {
        try {
            $slider->toggleStatus();

            return response()->json([
                'success' => true,
                'status' => $slider->status,
                'message' => 'Slider status updated successfully!'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error toggling slider status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update slider status'
            ], 500);
        }
    }

    /**
     * Reorder sliders
     */
    public function reorder(Request $request)
    {
        try {
            $order = $request->input('order', []);
            
            if (empty($order)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No order data provided'
                ], 400);
            }
            
            Sliders::reorder($order);

            return response()->json([
                'success' => true,
                'message' => 'Sliders reordered successfully!'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error reordering sliders: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder sliders'
            ], 500);
        }
    }

    /**
     * Duplicate a slider
     */
    public function duplicate(Sliders $slider)
    {
        try {
            $newSlider = $slider->replicate();
            $newSlider->first_slogan_one = $slider->first_slogan_one . ' (Copy)';
            $newSlider->status = 'inactive';
            $newSlider->save();

            return redirect()->route('sliders.index')
                ->with('success', 'Slider duplicated successfully!');
        } catch (\Exception $e) {
            \Log::error('Error duplicating slider: ' . $e->getMessage());
            return redirect()->route('sliders.index')
                ->with('error', 'Failed to duplicate slider');
        }
    }

    /**
     * Get sliders for API
     */
    public function api()
    {
        try {
            $sliders = Sliders::active()->ordered()->get();
            
            $sliders = $sliders->map(function ($slider) {
                return [
                    'id' => $slider->id,
                    'first_slogan_one' => $slider->first_slogan_one,
                    'second_slogan_one' => $slider->second_slogan_one,
                    'slider_one_img_url' => $slider->slider_one_img_url,
                    'first_slogan_two' => $slider->first_slogan_two,
                    'second_slogan_two' => $slider->second_slogan_two,
                    'slogan_two_img_url' => $slider->slogan_two_img_url,
                    'status' => $slider->status,
                    'sort_order' => $slider->sort_order,
                ];
            });
            
            return response()->json($sliders);
        } catch (\Exception $e) {
            \Log::error('Error fetching sliders API: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
