@extends('backend.layouts.app')

@section('title', 'Edit Testimonial')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Edit Testimonial</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('testimonials.index') }}">Testimonials</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Testimonial Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Edit Testimonial: {{ $testimonial->name }}</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('testimonials.update', $testimonial) }}" method="POST" enctype="multipart/form-data" id="testimonialForm">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Client Information</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Name -->
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Client Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name', $testimonial->name) }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Position & Company -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="position" class="form-label">Position/Title</label>
                                                <input type="text" class="form-control @error('position') is-invalid @enderror" 
                                                       id="position" name="position" value="{{ old('position', $testimonial->position) }}" 
                                                       placeholder="e.g., CEO, Manager, Developer">
                                                @error('position')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="company" class="form-label">Company/Organization</label>
                                                <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                                       id="company" name="company" value="{{ old('company', $testimonial->company) }}" 
                                                       placeholder="e.g., ABC Corp, XYZ Ltd">
                                                @error('company')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Rating -->
                                    <div class="mb-3">
                                        <label for="rating" class="form-label">Rating <span class="text-danger">*</span></label>
                                        <select class="form-select @error('rating') is-invalid @enderror" id="rating" name="rating" required>
                                            <option value="">Select Rating</option>
                                            <option value="5" {{ old('rating', $testimonial->rating) == '5' ? 'selected' : '' }}>⭐⭐⭐⭐⭐ (5 Stars)</option>
                                            <option value="4" {{ old('rating', $testimonial->rating) == '4' ? 'selected' : '' }}>⭐⭐⭐⭐ (4 Stars)</option>
                                            <option value="3" {{ old('rating', $testimonial->rating) == '3' ? 'selected' : '' }}>⭐⭐⭐ (3 Stars)</option>
                                            <option value="2" {{ old('rating', $testimonial->rating) == '2' ? 'selected' : '' }}>⭐⭐ (2 Stars)</option>
                                            <option value="1" {{ old('rating', $testimonial->rating) == '1' ? 'selected' : '' }}>⭐ (1 Star)</option>
                                        </select>
                                        @error('rating')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Testimonial Comment -->
                                    <div class="mb-3">
                                        <label for="comment" class="form-label">Testimonial Comment <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('comment') is-invalid @enderror" 
                                                  id="comment" name="comment" rows="6" maxlength="2000" required>{{ old('comment', $testimonial->comment) }}</textarea>
                                        <div class="form-text">Write the client's testimonial or review (max 2000 characters)</div>
                                        @error('comment')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Sidebar -->
                        <div class="col-lg-4">
                            <!-- Client Image -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Client Photo</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Current Image -->
                                    @if($testimonial->client_image_url)
                                        <div class="mb-3">
                                            <label class="form-label">Current Photo</label>
                                            <div class="current-image">
                                                <img src="{{ $testimonial->client_image_url }}" class="img-fluid rounded-circle" 
                                                     style="width: 100px; height: 100px; object-fit: cover;" alt="{{ $testimonial->name }}">
                                            </div>
                                        </div>
                                    @endif
                                    
                                    <div class="mb-3">
                                        <label for="client_image" class="form-label">
                                            {{ $testimonial->client_image_url ? 'Change Photo' : 'Upload Photo' }}
                                        </label>
                                        <input type="file" class="form-control @error('client_image') is-invalid @enderror" 
                                               id="client_image" name="client_image" accept="image/*">
                                        <div class="form-text">Upload client photo (JPEG, PNG, JPG, GIF, WebP - Max: 5MB)</div>
                                        @error('client_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div id="image_preview" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Settings -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Settings</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                            <option value="active" {{ old('status', $testimonial->status) === 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="inactive" {{ old('status', $testimonial->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        </select>
                                        <div class="form-text">Active testimonials will be displayed on the website</div>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Featured -->
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                                                   value="1" {{ old('featured', $testimonial->featured) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="featured">
                                                Featured Testimonial
                                            </label>
                                        </div>
                                        <div class="form-text">Featured testimonials are prioritized on the homepage</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Added On</label>
                                        <p class="form-control-plaintext">{{ $testimonial->created_at->format('M d, Y') }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Current Preview -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Current Preview</h5>
                                </div>
                                <div class="card-body">
                                    <div class="text-center">
                                        <div class="mb-2">
                                            @if($testimonial->client_image_url)
                                                <img src="{{ $testimonial->client_image_url }}" class="rounded-circle" 
                                                     style="width: 60px; height: 60px; object-fit: cover;" alt="{{ $testimonial->name }}">
                                            @else
                                                <div class="rounded-circle bg-light mx-auto d-flex align-items-center justify-content-center" 
                                                     style="width: 60px; height: 60px;">
                                                    <i class="ri-user-line text-muted"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <h6 class="mb-1">{{ $testimonial->name }}</h6>
                                        <p class="text-muted small mb-2">
                                            @if($testimonial->position || $testimonial->company)
                                                @if($testimonial->position){{ $testimonial->position }}@endif
                                                @if($testimonial->position && $testimonial->company) at @endif
                                                @if($testimonial->company){{ $testimonial->company }}@endif
                                            @else
                                                Valued Client
                                            @endif
                                        </p>
                                        <div class="mb-2">{!! $testimonial->star_rating !!}</div>
                                        <blockquote class="blockquote small">
                                            <p class="mb-0">"{{ Str::limit($testimonial->comment, 100) }}"</p>
                                        </blockquote>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('testimonials.index') }}" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Testimonials
                                </a>
                                <div>
                                    <a href="{{ route('testimonials.show', $testimonial) }}" class="btn btn-outline-primary me-2">
                                        <i class="ri-eye-line me-1"></i>View Details
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Update Testimonial
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 50%;
    border: 1px solid #dee2e6;
}

.blockquote {
    border-left: 3px solid var(--vz-primary);
    padding-left: 1rem;
    font-style: italic;
}

.current-image img {
    border: 2px solid #dee2e6;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview functionality
    document.getElementById('client_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('image_preview');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `
                    <div class="mt-2">
                        <label class="form-label">New Photo Preview</label>
                        <img src="${e.target.result}" class="image-preview d-block" alt="Client Preview">
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        } else {
            preview.innerHTML = '';
        }
    });

    // Character counter for comment
    const commentTextarea = document.getElementById('comment');
    if (commentTextarea) {
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        commentTextarea.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = 2000 - commentTextarea.value.length;
            counter.textContent = `${commentTextarea.value.length}/2000 characters`;
            counter.className = `form-text text-end ${remaining < 200 ? 'text-warning' : remaining < 0 ? 'text-danger' : ''}`;
        }

        commentTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Form validation
    document.getElementById('testimonialForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const comment = document.getElementById('comment').value.trim();
        const rating = document.getElementById('rating').value;
        const status = document.getElementById('status').value;

        if (!name || !comment || !rating || !status) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ri-loader-4-line me-1 spinner-border spinner-border-sm"></i>Updating...';
    });
});
</script>
@endpush
