@extends('frontend.layouts.app')

@section('title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions')
@section('description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions. Expert Laravel, React, and database development services in Nairobi and across Kenya.')
@section('keywords', 'software development Kenya, custom software solutions, web development Nairobi, mobile app development, IT consulting Kenya, software company Kenya, Laravel development, React development, database solutions Kenya, business software Kenya')

@section('og_type', 'website')
@section('og_title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions')
@section('og_description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions. Expert Laravel, React, and database development services in Nairobi and across Kenya.')
@section('og_image', asset('assets/img/grandtek-homepage-og.jpg'))

@section('twitter_title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions')
@section('twitter_description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions.')

@push('structured_data')
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "GrandTek IT Solutions",
  "alternateName": "GrandTek Kenya",
  "url": "{{ url('/') }}",
  "description": "Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions.",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "{{ url('/') }}?search={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GrandTek IT Solutions Kenya Limited",
    "url": "{{ url('/') }}",
    "logo": {
      "@type": "ImageObject",
      "url": "{{ asset('assets/img/grandtek-logo.png') }}"
    }
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What software development services do you offer in Kenya?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We offer comprehensive software development services including custom web applications, mobile app development, database solutions, e-commerce platforms, and IT consulting services across Kenya."
      }
    },
    {
      "@type": "Question",
      "name": "Do you provide software development services in Nairobi?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, we are based in Nairobi and provide software development services throughout Kenya including Nairobi, Mombasa, Kisumu, and other major cities."
      }
    },
    {
      "@type": "Question",
      "name": "What technologies do you use for software development?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We specialize in modern technologies including Laravel, React, Vue.js, Node.js, MySQL, PostgreSQL, and mobile development frameworks for creating robust software solutions."
      }
    }
  ]
}
</script>
@endpush

@section('content')
    {{-- Hero Slider Section --}}
    @include('frontend.include.sliders')

    {{-- About Us Section --}}
    @include('frontend.include.aboutus')

    {{-- Services Section --}}
    @include('frontend.include.services')

    {{-- Projects Section --}}
    @include('frontend.include.projects')

    {{-- Team Section --}}
    @include('frontend.include.team')

    {{-- Testimonial Section --}}
    @include('frontend.include.testimonial')

    {{-- Our Systems Section --}}
    @include('frontend.include.systems')

    {{-- Blog Section --}}
    @include('frontend.include.blog')

    {{-- Contact Section --}}
    @include('frontend.include.contact')

@endsection
