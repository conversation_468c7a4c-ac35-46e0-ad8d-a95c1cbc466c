@if(isset($teams) && $teams && $teams->count() > 0)
<div class="container-fluid py-5 mb-5 team">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Team</h5>
            <h1>Meet our expert Team</h1>
        </div>
        <div class="owl-carousel team-carousel wow fadeIn" data-wow-delay=".5s">
            @foreach($teams as $index => $team)
                <div class="rounded team-item">
                    <div class="team-content">
                        <div class="team-img-icon">
                            <div class="team-img rounded-circle">
                                @if($team['team_image_url'])
                                    <img src="{{ $team['team_image_url'] }}" class="img-fluid w-100 rounded-circle" alt="{{ $team['name'] }}">
                                @else
                                    <div class="img-fluid w-100 rounded-circle bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="ri-user-line text-muted" style="font-size: 3rem;"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="team-name text-center py-3">
                                <h4 class="">{{ $team['name'] }}</h4>
                                <p class="m-0">{{ $team['designation'] }}</p>
                            </div>
                            <div class="team-icon d-flex justify-content-center pb-4">
                                @if($team['facebook'])
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="{{ $team['facebook'] }}" target="_blank">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                @endif
                                @if($team['twitter'])
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="{{ $team['twitter'] }}" target="_blank">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                @endif
                                @if($team['instagram'])
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="{{ $team['instagram'] }}" target="_blank">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                @endif
                                @if($team['linkedin'])
                                    <a class="btn btn-square btn-secondary text-white rounded-circle m-1" href="{{ $team['linkedin'] }}" target="_blank">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
@endif
