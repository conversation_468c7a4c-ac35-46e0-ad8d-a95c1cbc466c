<?php if(isset($testimonials) && $testimonials && $testimonials->count() > 0): ?>
<div class="container-fluid testimonial py-5 mb-5">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Testimonial</h5>
            <h1>Our Client Saying!</h1>
        </div>
        <div class="owl-carousel testimonial-carousel wow fadeIn" data-wow-delay=".5s">
            <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="testimonial-item border p-4">
                    <div class="d-flex align-items-center">
                        <div class="">
                            <?php if($testimonial['client_image_url']): ?>
                                <img src="<?php echo e($testimonial['client_image_url']); ?>" alt="<?php echo e($testimonial['name']); ?>"
                                     style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center rounded-circle"
                                     style="width: 80px; height: 80px;">
                                    <i class="ri-user-line text-muted" style="font-size: 2rem;"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="ms-4">
                            <h4 class="text-secondary"><?php echo e($testimonial['name']); ?></h4>
                            <?php if($testimonial['position'] || $testimonial['company']): ?>
                                <p class="m-0 pb-3">
                                    <?php if($testimonial['position']): ?><?php echo e($testimonial['position']); ?><?php endif; ?>
                                    <?php if($testimonial['position'] && $testimonial['company']): ?> at <?php endif; ?>
                                    <?php if($testimonial['company']): ?><?php echo e($testimonial['company']); ?><?php endif; ?>
                                </p>
                            <?php else: ?>
                                <p class="m-0 pb-3">Valued Client</p>
                            <?php endif; ?>
                            <div class="d-flex pe-5">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <?php if($i <= $testimonial['rating']): ?>
                                        <i class="fas fa-star me-1 text-primary"></i>
                                    <?php else: ?>
                                        <i class="far fa-star me-1 text-muted"></i>
                                    <?php endif; ?>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                    <div class="border-top mt-4 pt-3">
                        <p class="mb-0"><?php echo e($testimonial['comment']); ?></p>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/include/testimonial.blade.php ENDPATH**/ ?>