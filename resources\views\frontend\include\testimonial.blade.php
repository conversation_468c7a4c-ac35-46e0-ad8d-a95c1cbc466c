@if(isset($testimonials) && $testimonials && $testimonials->count() > 0)
<div class="container-fluid testimonial py-5 mb-5">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Testimonial</h5>
            <h1>Our Client Saying!</h1>
        </div>
        <div class="owl-carousel testimonial-carousel wow fadeIn" data-wow-delay=".5s">
            @foreach($testimonials as $index => $testimonial)
                <div class="testimonial-item border p-4">
                    <div class="d-flex align-items-center">
                        <div class="">
                            @if($testimonial['client_image_url'])
                                <img src="{{ $testimonial['client_image_url'] }}" alt="{{ $testimonial['name'] }}"
                                     style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%;">
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center rounded-circle"
                                     style="width: 80px; height: 80px;">
                                    <i class="ri-user-line text-muted" style="font-size: 2rem;"></i>
                                </div>
                            @endif
                        </div>
                        <div class="ms-4">
                            <h4 class="text-secondary">{{ $testimonial['name'] }}</h4>
                            @if($testimonial['position'] || $testimonial['company'])
                                <p class="m-0 pb-3">
                                    @if($testimonial['position']){{ $testimonial['position'] }}@endif
                                    @if($testimonial['position'] && $testimonial['company']) at @endif
                                    @if($testimonial['company']){{ $testimonial['company'] }}@endif
                                </p>
                            @else
                                <p class="m-0 pb-3">Valued Client</p>
                            @endif
                            <div class="d-flex pe-5">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $testimonial['rating'])
                                        <i class="fas fa-star me-1 text-primary"></i>
                                    @else
                                        <i class="far fa-star me-1 text-muted"></i>
                                    @endif
                                @endfor
                            </div>
                        </div>
                    </div>
                    <div class="border-top mt-4 pt-3">
                        <p class="mb-0">{{ $testimonial['comment'] }}</p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
@endif
