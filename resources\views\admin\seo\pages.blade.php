@extends('admin.layouts.app')

@section('title', 'Page SEO Management')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Page SEO Management</h1>
        <a href="{{ route('admin.seo.pages.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Page SEO
        </a>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.seo.pages') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="page_type" class="form-label">Page Type</label>
                    <select name="page_type" id="page_type" class="form-select">
                        <option value="">All Types</option>
                        @foreach($pageTypes as $key => $label)
                            <option value="{{ $key }}" {{ request('page_type') === $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="Search by page identifier, title, or description..."
                           value="{{ request('search') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Pages Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">SEO Pages ({{ $pages->total() }})</h6>
                @if($pages->count() > 0)
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="toggleBulkActions()">
                        <i class="fas fa-tasks me-2"></i>Bulk Actions
                    </button>
                @endif
            </div>
        </div>
        <div class="card-body">
            @if($pages->count() > 0)
                <form id="bulkForm" action="{{ route('admin.seo.pages.bulk') }}" method="POST" style="display: none;">
                    @csrf
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <select name="action" class="form-select" required>
                                <option value="">Select Action</option>
                                <option value="activate">Activate</option>
                                <option value="deactivate">Deactivate</option>
                                <option value="delete">Delete</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary me-2">Apply</button>
                            <button type="button" class="btn btn-secondary" onclick="toggleBulkActions()">Cancel</button>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th id="bulkCheckboxHeader" style="display: none;">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Page</th>
                                <th>Type</th>
                                <th>SEO Status</th>
                                <th>Meta Info</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($pages as $page)
                            <tr>
                                <td class="bulkCheckbox" style="display: none;">
                                    <input type="checkbox" name="pages[]" value="{{ $page->id }}" 
                                           form="bulkForm" class="form-check-input page-checkbox">
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $page->page_identifier }}</strong>
                                        @if($page->title)
                                            <br><small class="text-muted">{{ Str::limit($page->title, 50) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ $page->page_type }}</span>
                                </td>
                                <td>
                                    <div class="small">
                                        @if($page->title)
                                            <i class="fas fa-check text-success" title="Has Title"></i>
                                        @else
                                            <i class="fas fa-times text-danger" title="No Title"></i>
                                        @endif
                                        
                                        @if($page->description)
                                            <i class="fas fa-check text-success" title="Has Description"></i>
                                        @else
                                            <i class="fas fa-times text-danger" title="No Description"></i>
                                        @endif
                                        
                                        @if($page->keywords)
                                            <i class="fas fa-check text-success" title="Has Keywords"></i>
                                        @else
                                            <i class="fas fa-times text-danger" title="No Keywords"></i>
                                        @endif
                                        
                                        @if($page->og_title || $page->og_description)
                                            <i class="fab fa-facebook text-primary" title="Has OG Tags"></i>
                                        @endif
                                        
                                        @if($page->twitter_title || $page->twitter_description)
                                            <i class="fab fa-twitter text-info" title="Has Twitter Cards"></i>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($page->description)
                                        <small class="text-muted">
                                            {{ Str::limit($page->description, 60) }}
                                            <br>
                                            <span class="badge bg-light text-dark">{{ strlen($page->description) }} chars</span>
                                        </small>
                                    @else
                                        <small class="text-muted">No description</small>
                                    @endif
                                </td>
                                <td>
                                    @if($page->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.seo.pages.edit', $page->id) }}" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deletePage({{ $page->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Showing {{ $pages->firstItem() }} to {{ $pages->lastItem() }} of {{ $pages->total() }} results
                    </div>
                    {{ $pages->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No pages found</h5>
                    <p class="text-muted">Start by creating your first page SEO configuration.</p>
                    <a href="{{ route('admin.seo.pages.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Page SEO
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this page SEO configuration? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleBulkActions() {
    const bulkForm = document.getElementById('bulkForm');
    const bulkCheckboxes = document.querySelectorAll('.bulkCheckbox');
    const bulkCheckboxHeader = document.getElementById('bulkCheckboxHeader');
    
    if (bulkForm.style.display === 'none') {
        bulkForm.style.display = 'block';
        bulkCheckboxHeader.style.display = 'table-cell';
        bulkCheckboxes.forEach(cb => cb.style.display = 'table-cell');
    } else {
        bulkForm.style.display = 'none';
        bulkCheckboxHeader.style.display = 'none';
        bulkCheckboxes.forEach(cb => cb.style.display = 'none');
        document.getElementById('selectAll').checked = false;
        document.querySelectorAll('.page-checkbox').forEach(cb => cb.checked = false);
    }
}

document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.page-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

function deletePage(id) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/seo/pages/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
