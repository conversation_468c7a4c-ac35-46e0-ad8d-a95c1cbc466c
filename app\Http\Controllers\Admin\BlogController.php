<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\BlogPost;
use App\Models\PageSeo;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BlogController extends Controller
{
    /**
     * Display blog posts
     */
    public function index(Request $request)
    {
        $query = BlogPost::query();

        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $posts = $query->latest()->paginate(20);
        $statuses = ['draft', 'published', 'archived'];

        return view('admin.blog.index', compact('posts', 'statuses'));
    }

    /**
     * Show create form
     */
    public function create()
    {
        $post = new BlogPost();
        $categories = $this->getAvailableCategories();
        $tags = $this->getAvailableTags();

        return view('admin.blog.form', compact('post', 'categories', 'tags'));
    }

    /**
     * Store new blog post
     */
    public function store(Request $request)
    {
        $validator = $this->validatePost($request);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $data = $this->preparePostData($request);
        
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $this->uploadImage($request->file('featured_image'));
        }

        $post = BlogPost::create($data);

        // Create SEO entry for the blog post
        $this->createSeoEntry($post);

        return redirect()->route('admin.blog.index')->with('success', 'Blog post created successfully!');
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $post = BlogPost::findOrFail($id);
        $categories = $this->getAvailableCategories();
        $tags = $this->getAvailableTags();

        return view('admin.blog.form', compact('post', 'categories', 'tags'));
    }

    /**
     * Update blog post
     */
    public function update(Request $request, $id)
    {
        $post = BlogPost::findOrFail($id);
        $validator = $this->validatePost($request, $id);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $data = $this->preparePostData($request);
        
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($post->featured_image) {
                Storage::disk('public')->delete($post->featured_image);
            }
            $data['featured_image'] = $this->uploadImage($request->file('featured_image'));
        }

        $post->update($data);

        // Update SEO entry
        $this->updateSeoEntry($post);

        return redirect()->route('admin.blog.index')->with('success', 'Blog post updated successfully!');
    }

    /**
     * Delete blog post
     */
    public function destroy($id)
    {
        $post = BlogPost::findOrFail($id);
        
        // Delete featured image
        if ($post->featured_image) {
            Storage::disk('public')->delete($post->featured_image);
        }

        // Delete SEO entry
        PageSeo::where('page_type', 'blog')->where('page_identifier', $post->slug)->delete();

        $post->delete();

        return redirect()->back()->with('success', 'Blog post deleted successfully!');
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|string|in:publish,draft,archive,delete',
            'posts' => 'required|array',
            'posts.*' => 'exists:blog_posts,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator);
        }

        $posts = BlogPost::whereIn('id', $request->posts);

        switch ($request->action) {
            case 'publish':
                $posts->update(['status' => 'published', 'published_at' => now()]);
                $message = 'Selected posts published successfully!';
                break;
            case 'draft':
                $posts->update(['status' => 'draft']);
                $message = 'Selected posts moved to draft!';
                break;
            case 'archive':
                $posts->update(['status' => 'archived']);
                $message = 'Selected posts archived!';
                break;
            case 'delete':
                foreach ($posts->get() as $post) {
                    if ($post->featured_image) {
                        Storage::disk('public')->delete($post->featured_image);
                    }
                    PageSeo::where('page_type', 'blog')->where('page_identifier', $post->slug)->delete();
                }
                $posts->delete();
                $message = 'Selected posts deleted successfully!';
                break;
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * SEO optimization for blog post
     */
    public function seo($id)
    {
        $post = BlogPost::findOrFail($id);
        $seo = PageSeo::where('page_type', 'blog')->where('page_identifier', $post->slug)->first();
        
        if (!$seo) {
            $seo = new PageSeo([
                'page_type' => 'blog',
                'page_identifier' => $post->slug,
            ]);
        }

        return view('admin.blog.seo', compact('post', 'seo'));
    }

    /**
     * Update SEO for blog post
     */
    public function updateSeo(Request $request, $id)
    {
        $post = BlogPost::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:60',
            'description' => 'nullable|string|max:160',
            'keywords' => 'nullable|string',
            'og_title' => 'nullable|string|max:60',
            'og_description' => 'nullable|string|max:160',
            'og_image' => 'nullable|url',
            'twitter_title' => 'nullable|string|max:60',
            'twitter_description' => 'nullable|string|max:160',
            'twitter_image' => 'nullable|url',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $data['page_type'] = 'blog';
        $data['page_identifier'] = $post->slug;
        $data['index'] = $request->has('index');
        $data['follow'] = $request->has('follow');
        $data['is_active'] = true;

        PageSeo::updateOrCreate(
            ['page_type' => 'blog', 'page_identifier' => $post->slug],
            $data
        );

        return redirect()->back()->with('success', 'SEO settings updated successfully!');
    }

    /**
     * Validate blog post data
     */
    private function validatePost(Request $request, $id = null)
    {
        $rules = [
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'status' => 'required|in:draft,published,archived',
            'featured' => 'boolean',
            'categories' => 'nullable|array',
            'tags' => 'nullable|array',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'published_at' => 'nullable|date',
        ];

        if (!$id) {
            $rules['slug'] = 'nullable|string|unique:blog_posts,slug';
        } else {
            $rules['slug'] = 'nullable|string|unique:blog_posts,slug,' . $id;
        }

        return Validator::make($request->all(), $rules);
    }

    /**
     * Prepare post data for storage
     */
    private function preparePostData(Request $request)
    {
        $data = $request->all();
        
        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }
        
        // Set published_at if publishing
        if ($data['status'] === 'published' && empty($data['published_at'])) {
            $data['published_at'] = now();
        }
        
        $data['featured'] = $request->has('featured');
        
        return $data;
    }

    /**
     * Upload featured image
     */
    private function uploadImage($file)
    {
        return $file->store('blog', 'public');
    }

    /**
     * Create SEO entry for blog post
     */
    private function createSeoEntry(BlogPost $post)
    {
        PageSeo::create([
            'page_type' => 'blog',
            'page_identifier' => $post->slug,
            'title' => $post->meta_title,
            'description' => $post->meta_description,
            'keywords' => $post->meta_keywords,
            'og_title' => $post->og_title,
            'og_description' => $post->og_description,
            'og_image' => $post->og_image,
            'twitter_title' => $post->twitter_title,
            'twitter_description' => $post->twitter_description,
            'twitter_image' => $post->twitter_image,
            'sitemap_priority' => 0.7,
            'sitemap_changefreq' => 'weekly',
            'is_active' => true,
        ]);
    }

    /**
     * Update SEO entry for blog post
     */
    private function updateSeoEntry(BlogPost $post)
    {
        PageSeo::updateOrCreate(
            ['page_type' => 'blog', 'page_identifier' => $post->slug],
            [
                'title' => $post->meta_title,
                'description' => $post->meta_description,
                'keywords' => $post->meta_keywords,
                'og_title' => $post->og_title,
                'og_description' => $post->og_description,
                'og_image' => $post->og_image,
                'twitter_title' => $post->twitter_title,
                'twitter_description' => $post->twitter_description,
                'twitter_image' => $post->twitter_image,
                'is_active' => true,
            ]
        );
    }

    /**
     * Get available categories
     */
    private function getAvailableCategories()
    {
        return BlogPost::whereNotNull('categories')
                      ->get()
                      ->pluck('categories')
                      ->flatten()
                      ->unique()
                      ->filter()
                      ->values()
                      ->toArray();
    }

    /**
     * Get available tags
     */
    private function getAvailableTags()
    {
        return BlogPost::whereNotNull('tags')
                      ->get()
                      ->pluck('tags')
                      ->flatten()
                      ->unique()
                      ->filter()
                      ->values()
                      ->toArray();
    }
}
