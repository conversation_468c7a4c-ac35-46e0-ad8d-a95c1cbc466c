<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Services;
use App\Models\Project;
use App\Models\System;
use App\Models\BlogPost;
use Carbon\Carbon;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap
     */
    public function index()
    {
        $sitemap = $this->generateSitemap();

        return response($sitemap, 200)
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate sitemap XML content
     */
    private function generateSitemap(): string
    {
        $urls = [];

        // Add static pages
        $urls[] = $this->createUrl(url('/'), Carbon::now(), 'daily', '1.0');
        $urls[] = $this->createUrl(route('about'), Carbon::now()->subDays(7), 'weekly', '0.8');
        $urls[] = $this->createUrl(route('services.all'), Carbon::now()->subDays(1), 'daily', '0.9');
        $urls[] = $this->createUrl(route('projects.all'), Carbon::now()->subDays(1), 'daily', '0.9');
        $urls[] = $this->createUrl(route('systems.all'), Carbon::now()->subDays(1), 'daily', '0.9');

        // Add Kenya-focused SEO pages
        $urls[] = $this->createUrl(route('software-development-kenya'), Carbon::now(), 'weekly', '0.9');
        $urls[] = $this->createUrl(route('web-development-nairobi'), Carbon::now(), 'weekly', '0.9');
        $urls[] = $this->createUrl(route('mobile-app-development-kenya'), Carbon::now(), 'weekly', '0.9');
        $urls[] = $this->createUrl(route('contact-kenya'), Carbon::now(), 'weekly', '0.8');

        // Add blog pages
        $urls[] = $this->createUrl(route('blog.index'), Carbon::now(), 'daily', '0.9');
        $urls[] = $this->createUrl(route('blog.rss'), Carbon::now(), 'daily', '0.7');

        // Add dynamic service pages
        $services = Services::active()->get();
        foreach ($services as $service) {
            $urls[] = $this->createUrl(
                route('service.detail', $service->slug),
                $service->updated_at,
                'weekly',
                '0.8'
            );
        }

        // Add dynamic project pages
        $projects = Project::active()->get();
        foreach ($projects as $project) {
            $urls[] = $this->createUrl(
                route('project.detail', $project->slug),
                $project->updated_at,
                'monthly',
                '0.7'
            );
        }

        // Add dynamic system pages
        $systems = System::active()->get();
        foreach ($systems as $system) {
            $urls[] = $this->createUrl(
                route('system.detail', $system->slug),
                $system->updated_at,
                'weekly',
                '0.8'
            );
        }

        // Add blog posts
        $blogPosts = BlogPost::published()->get();
        foreach ($blogPosts as $post) {
            $urls[] = $this->createUrl(
                route('blog.show', $post->slug),
                $post->updated_at,
                'weekly',
                '0.7'
            );
        }

        return $this->buildSitemapXml($urls);
    }

    /**
     * Create URL entry for sitemap
     */
    private function createUrl(string $url, Carbon $lastmod, string $changefreq, string $priority): array
    {
        return [
            'loc' => $url,
            'lastmod' => $lastmod->toISOString(),
            'changefreq' => $changefreq,
            'priority' => $priority
        ];
    }

    /**
     * Build sitemap XML structure
     */
    private function buildSitemapXml(array $urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['loc']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Generate robots.txt
     */
    public function robots()
    {
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /dashboard/\n";
        $robots .= "Disallow: /login/\n";
        $robots .= "Disallow: /register/\n";
        $robots .= "Disallow: /password/\n";
        $robots .= "Disallow: /storage/\n";
        $robots .= "Disallow: /vendor/\n";
        $robots .= "\n";
        $robots .= "# Sitemap\n";
        $robots .= "Sitemap: " . url('/sitemap.xml') . "\n";
        $robots .= "\n";
        $robots .= "# Crawl-delay for respectful crawling\n";
        $robots .= "Crawl-delay: 1\n";

        return response($robots, 200)
            ->header('Content-Type', 'text/plain');
    }
}
