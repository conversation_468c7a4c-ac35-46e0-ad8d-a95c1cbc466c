<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class PageSeo extends Model
{
    use HasFactory;

    protected $table = 'page_seo';

    protected $fillable = [
        'page_type',
        'page_identifier',
        'title',
        'description',
        'keywords',
        'canonical_url',
        'index',
        'follow',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'twitter_card',
        'structured_data',
        'sitemap_priority',
        'sitemap_changefreq',
        'is_active',
    ];

    protected $casts = [
        'index' => 'boolean',
        'follow' => 'boolean',
        'structured_data' => 'array',
        'sitemap_priority' => 'decimal:1',
        'is_active' => 'boolean',
    ];

    /**
     * Get SEO data for a specific page
     */
    public static function getForPage($pageType, $pageIdentifier)
    {
        $cacheKey = "page_seo_{$pageType}_{$pageIdentifier}";
        
        return Cache::remember($cacheKey, 3600, function () use ($pageType, $pageIdentifier) {
            return static::where('page_type', $pageType)
                         ->where('page_identifier', $pageIdentifier)
                         ->where('is_active', true)
                         ->first();
        });
    }

    /**
     * Set SEO data for a page
     */
    public static function setForPage($pageType, $pageIdentifier, array $data)
    {
        $seo = static::updateOrCreate(
            [
                'page_type' => $pageType,
                'page_identifier' => $pageIdentifier,
            ],
            array_merge($data, ['is_active' => true])
        );

        static::clearCacheForPage($pageType, $pageIdentifier);
        
        return $seo;
    }

    /**
     * Get meta robots value
     */
    public function getMetaRobotsAttribute()
    {
        $robots = [];
        
        if ($this->index) {
            $robots[] = 'index';
        } else {
            $robots[] = 'noindex';
        }
        
        if ($this->follow) {
            $robots[] = 'follow';
        } else {
            $robots[] = 'nofollow';
        }
        
        return implode(', ', $robots);
    }

    /**
     * Get effective title (with fallback)
     */
    public function getEffectiveTitleAttribute()
    {
        return $this->title ?: $this->generateDefaultTitle();
    }

    /**
     * Get effective description (with fallback)
     */
    public function getEffectiveDescriptionAttribute()
    {
        return $this->description ?: $this->generateDefaultDescription();
    }

    /**
     * Get effective OG title
     */
    public function getEffectiveOgTitleAttribute()
    {
        return $this->og_title ?: $this->effective_title;
    }

    /**
     * Get effective OG description
     */
    public function getEffectiveOgDescriptionAttribute()
    {
        return $this->og_description ?: $this->effective_description;
    }

    /**
     * Get effective Twitter title
     */
    public function getEffectiveTwitterTitleAttribute()
    {
        return $this->twitter_title ?: $this->effective_og_title;
    }

    /**
     * Get effective Twitter description
     */
    public function getEffectiveTwitterDescriptionAttribute()
    {
        return $this->twitter_description ?: $this->effective_og_description;
    }

    /**
     * Generate default title based on page type
     */
    protected function generateDefaultTitle()
    {
        $siteName = SeoSetting::get('site_name', 'GrandTek IT Solutions');
        
        switch ($this->page_type) {
            case 'route':
                return $this->generateRouteTitle() . " | {$siteName}";
            case 'service':
                return $this->generateServiceTitle() . " | {$siteName}";
            case 'project':
                return $this->generateProjectTitle() . " | {$siteName}";
            case 'blog':
                return $this->generateBlogTitle() . " | {$siteName}";
            default:
                return $siteName;
        }
    }

    /**
     * Generate default description based on page type
     */
    protected function generateDefaultDescription()
    {
        $defaultDescription = SeoSetting::get('site_description', 'Leading software development company in Kenya');
        
        switch ($this->page_type) {
            case 'route':
                return $this->generateRouteDescription();
            case 'service':
                return $this->generateServiceDescription();
            case 'project':
                return $this->generateProjectDescription();
            case 'blog':
                return $this->generateBlogDescription();
            default:
                return $defaultDescription;
        }
    }

    /**
     * Generate route-specific title
     */
    protected function generateRouteTitle()
    {
        $routeTitles = [
            'home' => 'Professional Software Development Services in Kenya',
            'about' => 'About Us - Leading Software Development Company',
            'services' => 'Our Software Development Services',
            'projects' => 'Our Software Development Projects',
            'contact' => 'Contact Us - Get Your Software Solution',
            'blog' => 'Software Development Blog & Insights',
        ];
        
        return $routeTitles[$this->page_identifier] ?? ucfirst(str_replace('-', ' ', $this->page_identifier));
    }

    /**
     * Generate route-specific description
     */
    protected function generateRouteDescription()
    {
        $routeDescriptions = [
            'home' => 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions.',
            'about' => 'Learn about GrandTek IT Solutions, Kenya\'s trusted software development partner with expertise in Laravel, React, and mobile app development.',
            'services' => 'Comprehensive software development services including web development, mobile apps, database solutions, and IT consulting in Kenya.',
            'projects' => 'Explore our portfolio of successful software development projects for businesses across Kenya and East Africa.',
            'contact' => 'Contact GrandTek IT Solutions for professional software development services in Kenya. Get a free consultation today.',
            'blog' => 'Latest insights on software development, technology trends, and digital transformation in Kenya.',
        ];
        
        return $routeDescriptions[$this->page_identifier] ?? 'Professional software development services in Kenya.';
    }

    /**
     * Generate service-specific title and description
     */
    protected function generateServiceTitle()
    {
        // This would integrate with your Services model
        return ucfirst(str_replace('-', ' ', $this->page_identifier)) . ' Services';
    }

    protected function generateServiceDescription()
    {
        return "Professional {$this->page_identifier} services in Kenya by GrandTek IT Solutions.";
    }

    /**
     * Generate project-specific title and description
     */
    protected function generateProjectTitle()
    {
        return ucfirst(str_replace('-', ' ', $this->page_identifier)) . ' Project';
    }

    protected function generateProjectDescription()
    {
        return "Case study: {$this->page_identifier} software development project by GrandTek IT Solutions Kenya.";
    }

    /**
     * Generate blog-specific title and description
     */
    protected function generateBlogTitle()
    {
        $post = BlogPost::where('slug', $this->page_identifier)->first();
        return $post ? $post->title : ucfirst(str_replace('-', ' ', $this->page_identifier));
    }

    protected function generateBlogDescription()
    {
        $post = BlogPost::where('slug', $this->page_identifier)->first();
        return $post ? $post->excerpt : "Blog post about {$this->page_identifier}";
    }

    /**
     * Clear cache for specific page
     */
    public static function clearCacheForPage($pageType, $pageIdentifier)
    {
        $cacheKey = "page_seo_{$pageType}_{$pageIdentifier}";
        Cache::forget($cacheKey);
    }

    /**
     * Boot method to clear cache on model events
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($model) {
            static::clearCacheForPage($model->page_type, $model->page_identifier);
        });

        static::deleted(function ($model) {
            static::clearCacheForPage($model->page_type, $model->page_identifier);
        });
    }

    /**
     * Scope for active pages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific page type
     */
    public function scopePageType($query, $type)
    {
        return $query->where('page_type', $type);
    }

    /**
     * Get available page types
     */
    public static function getPageTypes()
    {
        return [
            'route' => 'Static Routes',
            'service' => 'Service Pages',
            'project' => 'Project Pages',
            'system' => 'System Pages',
            'blog' => 'Blog Posts',
            'custom' => 'Custom Pages',
        ];
    }

    /**
     * Get available change frequencies
     */
    public static function getChangeFrequencies()
    {
        return [
            'always' => 'Always',
            'hourly' => 'Hourly',
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            'monthly' => 'Monthly',
            'yearly' => 'Yearly',
            'never' => 'Never',
        ];
    }
}
