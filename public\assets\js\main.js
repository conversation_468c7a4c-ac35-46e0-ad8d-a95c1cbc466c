(function ($) {
    "use strict";

    // Spinner
    var spinner = function () {
        setTimeout(function () {
            if ($('#spinner').length > 0) {
                $('#spinner').removeClass('show');
            }
        }, 1);
    };
    spinner();


    // Initiate the wowjs
    new WOW().init();


    // Enhanced Navigation Functionality

    // Theme Toggle Functionality - Grand Style
    function initThemeToggle() {
        // Load saved theme or default to light
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);

        // Theme toggle buttons (desktop and mobile)
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const themeToggleBtnMobile = document.getElementById('theme-toggle-btn-mobile');

        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', function() {
                toggleTheme();
            });
        }

        if (themeToggleBtnMobile) {
            themeToggleBtnMobile.addEventListener('click', function() {
                toggleTheme();
            });
        }
    }

    function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        setTheme(newTheme);
    }

    function setTheme(theme) {
        document.documentElement.setAttribute('data-bs-theme', theme);
        localStorage.setItem('theme', theme);
        updateThemeIcon(theme);

        // Update navbar theme attribute
        const navbar = document.querySelector('[data-navbar-theme]');
        if (navbar) {
            navbar.setAttribute('data-navbar-theme', theme);
        }

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    function updateThemeIcon(theme) {
        const themeIcon = document.getElementById('theme-icon');
        const themeIconMobile = document.getElementById('theme-icon-mobile');

        const iconClass = theme === 'dark' ? 'ri-moon-line fs-20' : 'ri-sun-line fs-20';

        if (themeIcon) {
            themeIcon.className = iconClass;
        }

        if (themeIconMobile) {
            themeIconMobile.className = iconClass;
        }
    }

    // Initialize theme toggle
    initThemeToggle();

    // Smooth scrolling for anchor links
    $('.smooth-scroll').on('click', function(e) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000, 'easeInOutExpo');

            // Update active nav link
            $('.navbar-nav .nav-link').removeClass('active');
            $(this).addClass('active');

            // Close mobile menu if open
            $('.navbar-collapse').collapse('hide');
        }
    });

    // Update active nav link on scroll
    $(window).scroll(function() {
        var scrollPos = $(document).scrollTop() + 100;

        $('.smooth-scroll').each(function() {
            var currLink = $(this);
            var refElement = $(currLink.attr("href"));

            if (refElement.length && refElement.position().top <= scrollPos &&
                refElement.position().top + refElement.height() > scrollPos) {
                $('.navbar-nav .nav-link').removeClass("active");
                currLink.addClass("active");
            } else {
                currLink.removeClass("active");
            }
        });

        // Set home as active when at top
        if (scrollPos < 100) {
            $('.navbar-nav .nav-link').removeClass("active");
            $('.navbar-nav .nav-link[href="/"]').addClass("active");
        }
    });

    // Navbar background on scroll
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.navbar').addClass('scrolled');
        } else {
            $('.navbar').removeClass('scrolled');
        }
    });

    // Close dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.dropdown').length) {
            $('.dropdown-menu').removeClass('show');
        }
    });

    // Keyboard navigation for dropdowns
    $('.dropdown-toggle').on('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            $(this).dropdown('toggle');
        }
    });

    // Search form functionality
    $('.search-form').on('submit', function(e) {
        e.preventDefault();
        var searchTerm = $(this).find('input[type="search"]').val();
        if (searchTerm.trim()) {
            // Implement search functionality here
            console.log('Searching for:', searchTerm);
            // You can add actual search logic here
            // For now, we'll just show an alert
            alert('Search functionality will be implemented. Searching for: ' + searchTerm);
        }
    });

    // Search input focus enhancement
    $('.navbar-search-input input[type="search"]').on('focus', function() {
        $(this).closest('.navbar-search-input').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.navbar-search-input').removeClass('focused');
    });

    // Preload navigation hover states
    $('.nav-link, .dropdown-item').on('mouseenter', function() {
        $(this).addClass('preload-hover');
    }).on('mouseleave', function() {
        $(this).removeClass('preload-hover');
    });

    // Enhanced Navigation Animations
    // Ripple effect on navigation links
    $('.navbar-nav .nav-link').on('click', function(e) {
        var $this = $(this);
        var $ripple = $('<span class="nav-link-ripple"></span>');
        var offset = $this.offset();
        var x = e.pageX - offset.left;
        var y = e.pageY - offset.top;

        $ripple.css({
            left: x,
            top: y,
            width: 0,
            height: 0
        });

        $this.append($ripple);

        // Remove ripple after animation
        setTimeout(function() {
            $ripple.remove();
        }, 600);
    });

    // Enhanced navbar scroll effect
    $(window).scroll(function() {
        var scroll = $(window).scrollTop();
        var navbar = $('.grand-navbar');

        if (scroll >= 50) {
            navbar.addClass('scrolled');
        } else {
            navbar.removeClass('scrolled');
        }
    });

    // Smooth hover animations for mobile menu
    $('.navbar-toggler').on('click', function() {
        $(this).toggleClass('active');
        $('.hamburger-icon').toggleClass('active');
    });

    // Enhanced brand logo animation
    $('.navbar-brand').on('mouseenter', function() {
        $(this).find('.navbar-logo').addClass('logo-hover');
    }).on('mouseleave', function() {
        $(this).find('.navbar-logo').removeClass('logo-hover');
    });


   // Back to top button
   $(window).scroll(function () {
    if ($(this).scrollTop() > 300) {
        $('.back-to-top').fadeIn('slow');
    } else {
        $('.back-to-top').fadeOut('slow');
    }
    });
    $('.back-to-top').click(function () {
        $('html, body').animate({scrollTop: 0}, 1500, 'easeInOutExpo');
        return false;
    });


    // Team carousel
    $(".team-carousel").owlCarousel({
        autoplay: true,
        smartSpeed: 1000,
        center: false,
        dots: false,
        loop: true,
        margin: 50,
        nav : true,
        navText : [
            '<i class="bi bi-arrow-left"></i>',
            '<i class="bi bi-arrow-right"></i>'
        ],
        responsiveClass: true,
        responsive: {
            0:{
                items:1
            },
            768:{
                items:2
            },
            992:{
                items:3
            }
        }
    });


    // Testimonial carousel

    $(".testimonial-carousel").owlCarousel({
        autoplay: true,
        smartSpeed: 1500,
        center: true,
        dots: true,
        loop: true,
        margin: 0,
        nav : true,
        navText: false,
        responsiveClass: true,
        responsive: {
            0:{
                items:1
            },
            576:{
                items:1
            },
            768:{
                items:2
            },
            992:{
                items:3
            }
        }
    });


     // Fact Counter

     $(document).ready(function(){
        $('.counter-value').each(function(){
            $(this).prop('Counter',0).animate({
                Counter: $(this).text()
            },{
                duration: 2000,
                easing: 'easeInQuad',
                step: function (now){
                    $(this).text(Math.ceil(now));
                }
            });
        });
    });



})(jQuery);

