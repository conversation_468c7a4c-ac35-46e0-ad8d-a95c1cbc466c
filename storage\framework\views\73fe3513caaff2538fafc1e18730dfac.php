<?php if($blogs && $blogs->count() > 0): ?>
<div class="container-fluid blog py-5 mb-5">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Blog</h5>
            <h1>Latest Blog & News</h1>
        </div>
        <div class="row g-5 justify-content-center">
            <div class="col-lg-6 col-xl-4 wow fadeIn" data-wow-delay="<?php echo e(0.3 + ($index * 0.2)); ?>s">
                <div class="blog-item position-relative bg-light rounded">
                    <img src="<?php echo e($blog['featured_image_url']); ?>" class="img-fluid w-100 rounded-top" alt="<?php echo e($blog['title']); ?>" style="height: 250px; object-fit: cover;">
                    <?php if($blog['categories'] && count($blog['categories']) > 0): ?>
                        <span class="position-absolute px-4 py-3 bg-primary text-white rounded" style="top: -28px; right: 20px;"><?php echo e($blog['categories'][0]); ?></span>
                    <?php endif; ?>
                    <div class="blog-btn d-flex justify-content-between position-relative px-3" style="margin-top: -75px;">
                        <div class="blog-icon btn btn-secondary px-3 rounded-pill my-auto">
                            <a href="<?php echo e($blog['url']); ?>" class="btn text-white">Read More</a>
                        </div>
                        <div class="blog-btn-icon btn btn-secondary px-4 py-3 rounded-pill ">
                            <div class="blog-icon-1">
                                <p class="text-white px-2">Share<i class="fa fa-arrow-right ms-3"></i></p>
                            </div>
                            <div class="blog-icon-2">
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode($blog['url'])); ?>" target="_blank" class="btn me-1"><i class="fab fa-facebook-f text-white"></i></a>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode($blog['url'])); ?>&text=<?php echo e(urlencode($blog['title'])); ?>" target="_blank" class="btn me-1"><i class="fab fa-twitter text-white"></i></a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(urlencode($blog['url'])); ?>" target="_blank" class="btn me-1"><i class="fab fa-linkedin text-white"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="blog-content text-center position-relative px-3" style="margin-top: -25px;">
                        <img src="/assets/img/admin.jpg" class="img-fluid rounded-circle border border-4 border-white mb-3" alt="<?php echo e($blog['author_name']); ?>">
                        <h5 class=""><?php echo e($blog['author_name']); ?></h5>
                        <span class="text-secondary"><?php echo e($blog['formatted_published_date']); ?></span>
                        <p class="py-2"><?php echo e(Str::limit($blog['excerpt'], 120)); ?></p>
                    </div>
                    <div class="blog-coment d-flex justify-content-between px-4 py-2 border bg-primary rounded-bottom">
                        <a href="<?php echo e($blog['url']); ?>" class="text-white"><small><i class="fas fa-eye me-2 text-secondary"></i><?php echo e($blog['views_count']); ?> Views</small></a>
                        <a href="<?php echo e($blog['url']); ?>" class="text-white"><small><i class="fa fa-clock me-2 text-secondary"></i><?php echo e($blog['reading_time']); ?> min read</small></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- View All Blogs Button -->
        <div class="text-center mt-5">
            <a href="<?php echo e(route('blog.index')); ?>" class="btn btn-primary btn-lg rounded-pill px-5">
                <i class="fas fa-blog me-2"></i>View All Blog Posts
            </a>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/include/blog.blade.php ENDPATH**/ ?>