<?php if($services && $services->count() > 0): ?>
<div class="container-fluid services py-5 mb-5" id="services">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Services</h5>
            <h1>Services Built Specifically For Your Business</h1>
        </div>
        <div class="row g-5 services-inner">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6 col-lg-4 wow fadeIn" data-wow-delay="<?php echo e(0.3 + ($index * 0.2)); ?>s">
                    <div class="services-item bg-light">
                        <div class="p-4 text-center services-content">
                            <div class="services-content-icon">
                                <?php if($service['service_image_url']): ?>
                                    <img src="<?php echo e($service['service_image_url']); ?>" alt="<?php echo e($service['name']); ?>" 
                                         class="mb-4" style="width: 80px; height: 80px; object-fit: contain;">
                                <?php else: ?>
                                    <i class="<?php echo e($service['icon_class']); ?> fa-7x mb-4 text-primary"></i>
                                <?php endif; ?>
                                <h4 class="mb-3"><?php echo e($service['name']); ?></h4>
                                <p class="mb-4">
                                    <?php echo e($service['short_description'] ?: $service['description']); ?>

                                </p>
                                <a href="<?php echo e(route('service.detail', $service['slug'])); ?>" class="btn btn-secondary text-white px-5 py-3 rounded-pill">
                                    <i class="ri-eye-line me-2"></i>View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/include/services.blade.php ENDPATH**/ ?>