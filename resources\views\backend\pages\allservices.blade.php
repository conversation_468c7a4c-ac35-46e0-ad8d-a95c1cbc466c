@extends('backend.layouts.app')

@section('title', 'All Services - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">All Services</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">All Services</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">Services List</h4>
                <a href="{{ route('services') }}" class="btn btn-primary">
                    <i class="ri-add-line me-1"></i>Add New Service
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>#</th>
                                <th>Service Name</th>
                                <th>Description</th>
                                <th>Icon</th>
                                <th>Created Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{-- @forelse($services as $service)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>{{ $service->name }}</td>
                                <td>{{ Str::limit($service->description, 50) }}</td>
                                <td>
                                    @if($service->service_image)
                                        <img src="{{ Storage::url($service->service_image) }}" alt="Service Icon" class="rounded" style="width: 40px; height: 40px; object-fit: cover;">
                                    @else
                                        <span class="badge bg-secondary">No Icon</span>
                                    @endif
                                </td>
                                <td>{{ $service->created_at->format('M d, Y') }}</td>
                                <td>
                                    <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#serviceModal{{ $service->id }}">
                                        <i class="ri-eye-line"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-warning" onclick="editService({{ $service->id }})">
                                        <i class="ri-edit-line"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteService({{ $service->id }})">
                                        <i class="ri-delete-bin-line"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            @empty --}}
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="ri-customer-service-2-line fs-1 text-muted mb-3"></i>
                                        <h5 class="text-muted">No services added yet</h5>
                                        <p class="text-muted">Start by adding your first service to showcase your offerings.</p>
                                        <a href="{{ route('services') }}" class="btn btn-primary">
                                            <i class="ri-add-line me-1"></i>Add First Service
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {{-- @endforelse --}}
                        </tbody>
                    </table>
                </div>

                {{-- Pagination --}}
                {{-- @if($services->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $services->links() }}
                </div>
                @endif --}}
            </div>
        </div>
    </div>
</div>

{{-- Service View Modals --}}
{{-- @foreach($services ?? [] as $service)
<div class="modal fade" id="serviceModal{{ $service->id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $service->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        @if($service->service_image)
                            <img src="{{ Storage::url($service->service_image) }}" alt="Service Icon" class="img-fluid rounded">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                <span class="text-muted">No Icon</span>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-8">
                        <h6>Service Name:</h6>
                        <p>{{ $service->name }}</p>
                        
                        <h6>Description:</h6>
                        <p>{{ $service->description }}</p>
                        
                        <h6>Created:</h6>
                        <p>{{ $service->created_at->format('M d, Y H:i') }}</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-warning" onclick="editService({{ $service->id }})">
                    <i class="ri-edit-line me-1"></i>Edit Service
                </button>
            </div>
        </div>
    </div>
</div>
@endforeach --}}
@endsection

@push('scripts')
<script>
function editService(id) {
    // Add edit functionality here
    console.log('Edit service with ID:', id);
}

function deleteService(id) {
    if (confirm('Are you sure you want to delete this service?')) {
        // Add delete functionality here
        console.log('Delete service with ID:', id);
    }
}
</script>
@endpush
