@extends('backend.layouts.app')

@section('title', 'View Slider - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Slider Preview</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('sliders.index') }}">Sliders</a></li>
                    <li class="breadcrumb-item active">Preview</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Action Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex gap-2">
                <a href="{{ route('sliders.edit', $slider) }}" class="btn btn-primary">
                    <i class="ri-edit-line me-1"></i>Edit Slider
                </a>
                <button type="button" class="btn btn-outline-{{ $slider->status === 'active' ? 'warning' : 'success' }}" 
                        onclick="toggleStatus({{ $slider->id }})">
                    <i class="ri-{{ $slider->status === 'active' ? 'pause' : 'play' }}-line me-1"></i>
                    {{ $slider->status === 'active' ? 'Deactivate' : 'Activate' }}
                </button>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('sliders.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i>Back to Sliders
                </a>
                <button type="button" class="btn btn-outline-danger" onclick="deleteSlider({{ $slider->id }})">
                    <i class="ri-delete-bin-line me-1"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Slider Preview -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Slider Preview</h5>
            </div>
            <div class="card-body p-0">
                <div class="slider-preview-container" style="height: 500px; position: relative; overflow: hidden;">
                    @if($slider->media_type === 'video' && $slider->video_path)
                        <video autoplay muted loop style="width: 100%; height: 100%; object-fit: cover;">
                            <source src="{{ Storage::url($slider->video_path) }}" type="video/mp4">
                        </video>
                    @elseif($slider->image_path)
                        <img src="{{ Storage::url($slider->image_path) }}" 
                             style="width: 100%; height: 100%; object-fit: cover;" 
                             alt="{{ $slider->title }}">
                    @endif
                    
                    <!-- Overlay -->
                    <div class="slider-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; 
                         background: {{ $slider->background_color ? $slider->background_color.'80' : 'rgba(0,0,0,0.4)' }};">
                    </div>
                    
                    <!-- Content -->
                    <div class="slider-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                         text-align: {{ $slider->text_alignment }}; color: {{ $slider->text_color }}; width: 80%;">
                        @if($slider->subtitle)
                            <p class="slider-subtitle" style="font-size: 1.2rem; margin-bottom: 1rem; opacity: 0.9;">
                                {{ $slider->subtitle }}
                            </p>
                        @endif
                        
                        @if($slider->title)
                            <h1 class="slider-title" style="font-size: 3rem; font-weight: bold; margin-bottom: 1.5rem;">
                                {{ $slider->title }}
                            </h1>
                        @endif
                        
                        @if($slider->description)
                            <p class="slider-description" style="font-size: 1.1rem; margin-bottom: 2rem; line-height: 1.6;">
                                {{ $slider->description }}
                            </p>
                        @endif
                        
                        @if($slider->button_text)
                            <a href="{{ $slider->button_link ?: '#' }}" class="btn btn-primary btn-lg">
                                {{ $slider->button_text }}
                                <i class="ri-arrow-right-line ms-2"></i>
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Slider Details -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Slider Details</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td class="fw-semibold" style="width: 200px;">Title:</td>
                                <td>{{ $slider->title ?: 'Not set' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Subtitle:</td>
                                <td>{{ $slider->subtitle ?: 'Not set' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Description:</td>
                                <td>{{ $slider->description ?: 'Not set' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Button Text:</td>
                                <td>{{ $slider->button_text ?: 'Not set' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Button Link:</td>
                                <td>
                                    @if($slider->button_link)
                                        <a href="{{ $slider->button_link }}" target="_blank">{{ $slider->button_link }}</a>
                                    @else
                                        Not set
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Media Type:</td>
                                <td>
                                    <span class="badge bg-info">
                                        <i class="ri-{{ $slider->media_type === 'video' ? 'video' : 'image' }}-line me-1"></i>
                                        {{ ucfirst($slider->media_type) }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Status:</td>
                                <td>{!! $slider->status_badge !!}</td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Sort Order:</td>
                                <td><span class="badge bg-light text-dark">#{{ $slider->sort_order }}</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Design Settings</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-semibold">Text Alignment:</label>
                    <div>{{ ucfirst($slider->text_alignment) }}</div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-semibold">Display Duration:</label>
                    <div>
                        <span class="badge bg-primary fs-6">{{ $slider->display_duration ?? 12 }} seconds</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-semibold">Text Color:</label>
                    <div class="d-flex align-items-center gap-2">
                        <div class="color-preview" style="width: 20px; height: 20px; border-radius: 4px; 
                             background: {{ $slider->text_color }}; border: 1px solid #ddd;"></div>
                        <span>{{ $slider->text_color }}</span>
                    </div>
                </div>
                
                @if($slider->background_color)
                    <div class="mb-3">
                        <label class="form-label fw-semibold">Background Overlay:</label>
                        <div class="d-flex align-items-center gap-2">
                            <div class="color-preview" style="width: 20px; height: 20px; border-radius: 4px; 
                                 background: {{ $slider->background_color }}; border: 1px solid #ddd;"></div>
                            <span>{{ $slider->background_color }}</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Animation Settings</h5>
            </div>
            <div class="card-body">
                @php
                    $animations = $slider->animation_settings;
                @endphp
                <div class="mb-2">
                    <small class="text-muted">Title Animation:</small>
                    <div>{{ ucwords(str_replace('_', ' ', $animations['title_animation'] ?? 'Fade In Up')) }}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">Subtitle Animation:</small>
                    <div>{{ ucwords(str_replace('_', ' ', $animations['subtitle_animation'] ?? 'Fade In Left')) }}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">Duration:</small>
                    <div>{{ $animations['duration'] ?? 1000 }}ms</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">Delay:</small>
                    <div>{{ $animations['delay'] ?? 200 }}ms</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Timestamps</h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">Created:</small>
                    <div>{{ $slider->created_at->format('M d, Y \a\t g:i A') }}</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">Last Updated:</small>
                    <div>{{ $slider->updated_at->format('M d, Y \a\t g:i A') }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Toggle status
function toggleStatus(id) {
    fetch(`/admin/sliders/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to toggle status');
    });
}

// Delete slider
function deleteSlider(id) {
    if (confirm('Are you sure you want to delete this slider?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/sliders/${id}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').content}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
