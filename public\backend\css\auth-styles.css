/* 
 * GrandTek Authentication Styles
 * Modern, responsive authentication pages
 */

/* Additional animations and effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(64, 81, 137, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(64, 81, 137, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(64, 81, 137, 0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced form animations */
.auth-card {
    animation: fadeInUp 0.6s ease-out;
}

.form-group {
    animation: slideInLeft 0.4s ease-out;
    animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }

/* Enhanced input focus effects */
.form-control:focus {
    animation: pulse 2s infinite;
}

/* Custom checkbox styling */
.form-check-input {
    width: 1.2em;
    height: 1.2em;
    border-radius: 0.25em;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: var(--vz-primary);
    border-color: var(--vz-primary);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.form-check-input:focus {
    border-color: var(--vz-primary);
    box-shadow: 0 0 0 0.2rem rgba(64, 81, 137, 0.25);
}

/* Enhanced button effects */
.btn-auth {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-auth::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    z-index: -1;
}

.btn-auth:active::after {
    width: 300px;
    height: 300px;
}

/* Loading state enhancements */
.btn-auth.loading {
    color: transparent;
}

.btn-auth.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Password strength indicator */
.password-strength .progress {
    height: 4px;
    border-radius: 2px;
    background-color: #e9ecef;
    overflow: hidden;
}

.password-strength .progress-bar {
    transition: width 0.3s ease, background-color 0.3s ease;
}

/* Invalid feedback styling */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: var(--vz-danger);
    animation: fadeInUp 0.3s ease-out;
}

.form-control.is-invalid {
    border-color: var(--vz-danger);
    box-shadow: 0 0 0 0.2rem rgba(240, 101, 72, 0.25);
}

/* Success states */
.form-control.is-valid {
    border-color: var(--vz-success);
    box-shadow: 0 0 0 0.2rem rgba(10, 179, 156, 0.25);
}

/* Enhanced alert styling */
.alert {
    position: relative;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.5rem;
    animation: fadeInUp 0.4s ease-out;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem;
}

/* Social login buttons (if needed) */
.btn-social {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #fff;
    color: var(--vz-body-color);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-social:hover {
    border-color: var(--vz-primary);
    color: var(--vz-primary);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-social i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .auth-card-header {
        padding: 1.5rem;
    }
    
    .auth-logo {
        width: 60px;
        height: 60px;
    }
    
    .auth-logo img {
        width: 35px;
        height: 35px;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .auth-subtitle {
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .auth-page-wrapper {
        padding: 15px;
    }
    
    .auth-card-body {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    .btn-auth {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .auth-card {
        background: rgba(33, 37, 41, 0.95);
        color: #fff;
    }
    
    .form-control {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #fff;
    }
    
    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }
    
    .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--vz-primary);
    }
    
    .input-group-text {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
    }
}

/* Print styles */
@media print {
    .auth-page-wrapper {
        background: none !important;
    }
    
    .auth-card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .btn-auth {
        background: #000 !important;
        color: #fff !important;
    }
}

/* Accessibility improvements */
.form-control:focus,
.btn-auth:focus,
.form-check-input:focus {
    outline: 2px solid var(--vz-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .auth-card {
        border: 2px solid #000;
    }
    
    .form-control {
        border: 2px solid #000;
    }
    
    .btn-auth {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
