<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class HomepageSlider extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'secondary_description',
        'button_text',
        'button_link',
        'image_path',
        'secondary_image_path',
        'video_path',
        'media_type',
        'status',
        'sort_order',
        'background_color',
        'text_color',
        'text_alignment',
        'display_duration',
        'animation_settings',
    ];

    protected $casts = [
        // Remove array cast to avoid conflicts with accessor
    ];

    /**
     * Scope to get only active sliders
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get sliders ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }

    /**
     * Get the full URL for the image
     */
    public function getImageUrlAttribute()
    {
        if ($this->image_path) {
            return Storage::url($this->image_path);
        }
        return null;
    }

    /**
     * Get the full URL for the video
     */
    public function getVideoUrlAttribute()
    {
        if ($this->video_path) {
            return Storage::url($this->video_path);
        }
        return null;
    }

    /**
     * Get the media URL based on media type
     */
    public function getMediaUrlAttribute()
    {
        return $this->media_type === 'video' ? $this->video_url : $this->image_url;
    }

    /**
     * Check if slider has media
     */
    public function hasMedia()
    {
        return $this->media_type === 'video' ? !empty($this->video_path) : !empty($this->image_path);
    }

    /**
     * Get animation settings with defaults
     */
    public function getAnimationSettingsAttribute($value)
    {
        return $this->parseAnimationSettings($value);
    }

    /**
     * Parse animation settings safely
     */
    private function parseAnimationSettings($value)
    {
        $defaults = [
            'title_animation' => 'fadeInUp',
            'subtitle_animation' => 'fadeInLeft',
            'description_animation' => 'fadeInRight',
            'button_animation' => 'fadeInUp',
            'duration' => 1000,
            'delay' => 200,
        ];

        // Return defaults if value is null or empty
        if (empty($value)) {
            return $defaults;
        }

        // Handle different types of input
        $settings = [];

        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $settings = $decoded;
            }
        } elseif (is_array($value)) {
            $settings = $value;
        }

        // Ensure we always return an array
        return array_merge($defaults, is_array($settings) ? $settings : []);
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-assign sort order when creating
        static::creating(function ($slider) {
            if (is_null($slider->sort_order)) {
                $slider->sort_order = static::max('sort_order') + 1;
            }
        });

        // Clean up files when deleting
        static::deleting(function ($slider) {
            if ($slider->image_path && Storage::exists($slider->image_path)) {
                Storage::delete($slider->image_path);
            }
            if ($slider->video_path && Storage::exists($slider->video_path)) {
                Storage::delete($slider->video_path);
            }
        });
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $class = $this->status === 'active' ? 'bg-success' : 'bg-secondary';
        $text = ucfirst($this->status);
        return "<span class='badge {$class}'>{$text}</span>";
    }

    /**
     * Toggle slider status
     */
    public function toggleStatus()
    {
        $this->status = $this->status === 'active' ? 'inactive' : 'active';
        return $this->save();
    }

    /**
     * Reorder sliders
     */
    public static function reorder(array $order)
    {
        foreach ($order as $index => $id) {
            static::where('id', $id)->update(['sort_order' => $index + 1]);
        }
    }
}
