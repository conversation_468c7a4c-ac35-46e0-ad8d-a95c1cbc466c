/*
Template Name: Dashtreme Admin
Author: CODERVENT
Email: <EMAIL>
File: app-style
*/


/* Material Design Icons*/

/*!
 *  Material Design Iconic Font by <PERSON> (@zavoloklom) - http://zavoloklom.github.io/material-design-iconic-font/
 *  License - http://zavoloklom.github.io/material-design-iconic-font/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
@font-face {
  font-family: 'Material-Design-Iconic-Font';
  src: url('../fonts/Material-Design-Iconic-Font.woff2?v=2.2.0') format('woff2'), url('../fonts/Material-Design-Iconic-Font.woff?v=2.2.0') format('woff'), url('../fonts/Material-Design-Iconic-Font.ttf?v=2.2.0') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.zmdi {
  display: inline-block;
  font: normal normal normal 14px/1 'Material-Design-Iconic-Font';
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.zmdi-hc-lg {
  font-size: 1.33333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}
.zmdi-hc-2x {
  font-size: 2em;
}
.zmdi-hc-3x {
  font-size: 3em;
}
.zmdi-hc-4x {
  font-size: 4em;
}
.zmdi-hc-5x {
  font-size: 5em;
}
.zmdi-hc-fw {
  width: 1.28571429em;
  text-align: center;
}
.zmdi-hc-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}
.zmdi-hc-ul > li {
  position: relative;
}
.zmdi-hc-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: 0.14285714em;
  text-align: center;
}
.zmdi-hc-li.zmdi-hc-lg {
  left: -1.85714286em;
}
.zmdi-hc-border {
  padding: .1em .25em;
  border: solid 0.1em #9e9e9e;
  border-radius: 2px;
}
.zmdi-hc-border-circle {
  padding: .1em .25em;
  border: solid 0.1em #9e9e9e;
  border-radius: 50%;
}
.zmdi.pull-left {
  float: left;
  margin-right: .15em;
}
.zmdi.pull-right {
  float: right;
  margin-left: .15em;
}
.zmdi-hc-spin {
  -webkit-animation: zmdi-spin 1.5s infinite linear;
          animation: zmdi-spin 1.5s infinite linear;
}
.zmdi-hc-spin-reverse {
  -webkit-animation: zmdi-spin-reverse 1.5s infinite linear;
          animation: zmdi-spin-reverse 1.5s infinite linear;
}
@-webkit-keyframes zmdi-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}
@keyframes zmdi-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}
@-webkit-keyframes zmdi-spin-reverse {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(-359deg);
            transform: rotate(-359deg);
  }
}
@keyframes zmdi-spin-reverse {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(-359deg);
            transform: rotate(-359deg);
  }
}
.zmdi-hc-rotate-90 {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
.zmdi-hc-rotate-180 {
  -webkit-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
          transform: rotate(180deg);
}
.zmdi-hc-rotate-270 {
  -webkit-transform: rotate(270deg);
      -ms-transform: rotate(270deg);
          transform: rotate(270deg);
}
.zmdi-hc-flip-horizontal {
  -webkit-transform: scale(-1, 1);
      -ms-transform: scale(-1, 1);
          transform: scale(-1, 1);
}
.zmdi-hc-flip-vertical {
  -webkit-transform: scale(1, -1);
      -ms-transform: scale(1, -1);
          transform: scale(1, -1);
}
.zmdi-hc-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}
.zmdi-hc-stack-1x,
.zmdi-hc-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}
.zmdi-hc-stack-1x {
  line-height: inherit;
}
.zmdi-hc-stack-2x {
  font-size: 2em;
}
.zmdi-hc-inverse {
  color: #ffffff;
}
/* Material Design Iconic Font uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.zmdi-3d-rotation:before {
  content: '\f101';
}
.zmdi-airplane-off:before {
  content: '\f102';
}
.zmdi-airplane:before {
  content: '\f103';
}
.zmdi-album:before {
  content: '\f104';
}
.zmdi-archive:before {
  content: '\f105';
}
.zmdi-assignment-account:before {
  content: '\f106';
}
.zmdi-assignment-alert:before {
  content: '\f107';
}
.zmdi-assignment-check:before {
  content: '\f108';
}
.zmdi-assignment-o:before {
  content: '\f109';
}
.zmdi-assignment-return:before {
  content: '\f10a';
}
.zmdi-assignment-returned:before {
  content: '\f10b';
}
.zmdi-assignment:before {
  content: '\f10c';
}
.zmdi-attachment-alt:before {
  content: '\f10d';
}
.zmdi-attachment:before {
  content: '\f10e';
}
.zmdi-audio:before {
  content: '\f10f';
}
.zmdi-badge-check:before {
  content: '\f110';
}
.zmdi-balance-wallet:before {
  content: '\f111';
}
.zmdi-balance:before {
  content: '\f112';
}
.zmdi-battery-alert:before {
  content: '\f113';
}
.zmdi-battery-flash:before {
  content: '\f114';
}
.zmdi-battery-unknown:before {
  content: '\f115';
}
.zmdi-battery:before {
  content: '\f116';
}
.zmdi-bike:before {
  content: '\f117';
}
.zmdi-block-alt:before {
  content: '\f118';
}
.zmdi-block:before {
  content: '\f119';
}
.zmdi-boat:before {
  content: '\f11a';
}
.zmdi-book-image:before {
  content: '\f11b';
}
.zmdi-book:before {
  content: '\f11c';
}
.zmdi-bookmark-outline:before {
  content: '\f11d';
}
.zmdi-bookmark:before {
  content: '\f11e';
}
.zmdi-brush:before {
  content: '\f11f';
}
.zmdi-bug:before {
  content: '\f120';
}
.zmdi-bus:before {
  content: '\f121';
}
.zmdi-cake:before {
  content: '\f122';
}
.zmdi-car-taxi:before {
  content: '\f123';
}
.zmdi-car-wash:before {
  content: '\f124';
}
.zmdi-car:before {
  content: '\f125';
}
.zmdi-card-giftcard:before {
  content: '\f126';
}
.zmdi-card-membership:before {
  content: '\f127';
}
.zmdi-card-travel:before {
  content: '\f128';
}
.zmdi-card:before {
  content: '\f129';
}
.zmdi-case-check:before {
  content: '\f12a';
}
.zmdi-case-download:before {
  content: '\f12b';
}
.zmdi-case-play:before {
  content: '\f12c';
}
.zmdi-case:before {
  content: '\f12d';
}
.zmdi-cast-connected:before {
  content: '\f12e';
}
.zmdi-cast:before {
  content: '\f12f';
}
.zmdi-chart-donut:before {
  content: '\f130';
}
.zmdi-chart:before {
  content: '\f131';
}
.zmdi-city-alt:before {
  content: '\f132';
}
.zmdi-city:before {
  content: '\f133';
}
.zmdi-close-circle-o:before {
  content: '\f134';
}
.zmdi-close-circle:before {
  content: '\f135';
}
.zmdi-close:before {
  content: '\f136';
}
.zmdi-cocktail:before {
  content: '\f137';
}
.zmdi-code-setting:before {
  content: '\f138';
}
.zmdi-code-smartphone:before {
  content: '\f139';
}
.zmdi-code:before {
  content: '\f13a';
}
.zmdi-coffee:before {
  content: '\f13b';
}
.zmdi-collection-bookmark:before {
  content: '\f13c';
}
.zmdi-collection-case-play:before {
  content: '\f13d';
}
.zmdi-collection-folder-image:before {
  content: '\f13e';
}
.zmdi-collection-image-o:before {
  content: '\f13f';
}
.zmdi-collection-image:before {
  content: '\f140';
}
.zmdi-collection-item-1:before {
  content: '\f141';
}
.zmdi-collection-item-2:before {
  content: '\f142';
}
.zmdi-collection-item-3:before {
  content: '\f143';
}
.zmdi-collection-item-4:before {
  content: '\f144';
}
.zmdi-collection-item-5:before {
  content: '\f145';
}
.zmdi-collection-item-6:before {
  content: '\f146';
}
.zmdi-collection-item-7:before {
  content: '\f147';
}
.zmdi-collection-item-8:before {
  content: '\f148';
}
.zmdi-collection-item-9-plus:before {
  content: '\f149';
}
.zmdi-collection-item-9:before {
  content: '\f14a';
}
.zmdi-collection-item:before {
  content: '\f14b';
}
.zmdi-collection-music:before {
  content: '\f14c';
}
.zmdi-collection-pdf:before {
  content: '\f14d';
}
.zmdi-collection-plus:before {
  content: '\f14e';
}
.zmdi-collection-speaker:before {
  content: '\f14f';
}
.zmdi-collection-text:before {
  content: '\f150';
}
.zmdi-collection-video:before {
  content: '\f151';
}
.zmdi-compass:before {
  content: '\f152';
}
.zmdi-cutlery:before {
  content: '\f153';
}
.zmdi-delete:before {
  content: '\f154';
}
.zmdi-dialpad:before {
  content: '\f155';
}
.zmdi-dns:before {
  content: '\f156';
}
.zmdi-drink:before {
  content: '\f157';
}
.zmdi-edit:before {
  content: '\f158';
}
.zmdi-email-open:before {
  content: '\f159';
}
.zmdi-email:before {
  content: '\f15a';
}
.zmdi-eye-off:before {
  content: '\f15b';
}
.zmdi-eye:before {
  content: '\f15c';
}
.zmdi-eyedropper:before {
  content: '\f15d';
}
.zmdi-favorite-outline:before {
  content: '\f15e';
}
.zmdi-favorite:before {
  content: '\f15f';
}
.zmdi-filter-list:before {
  content: '\f160';
}
.zmdi-fire:before {
  content: '\f161';
}
.zmdi-flag:before {
  content: '\f162';
}
.zmdi-flare:before {
  content: '\f163';
}
.zmdi-flash-auto:before {
  content: '\f164';
}
.zmdi-flash-off:before {
  content: '\f165';
}
.zmdi-flash:before {
  content: '\f166';
}
.zmdi-flip:before {
  content: '\f167';
}
.zmdi-flower-alt:before {
  content: '\f168';
}
.zmdi-flower:before {
  content: '\f169';
}
.zmdi-font:before {
  content: '\f16a';
}
.zmdi-fullscreen-alt:before {
  content: '\f16b';
}
.zmdi-fullscreen-exit:before {
  content: '\f16c';
}
.zmdi-fullscreen:before {
  content: '\f16d';
}
.zmdi-functions:before {
  content: '\f16e';
}
.zmdi-gas-station:before {
  content: '\f16f';
}
.zmdi-gesture:before {
  content: '\f170';
}
.zmdi-globe-alt:before {
  content: '\f171';
}
.zmdi-globe-lock:before {
  content: '\f172';
}
.zmdi-globe:before {
  content: '\f173';
}
.zmdi-graduation-cap:before {
  content: '\f174';
}
.zmdi-home:before {
  content: '\f175';
}
.zmdi-hospital-alt:before {
  content: '\f176';
}
.zmdi-hospital:before {
  content: '\f177';
}
.zmdi-hotel:before {
  content: '\f178';
}
.zmdi-hourglass-alt:before {
  content: '\f179';
}
.zmdi-hourglass-outline:before {
  content: '\f17a';
}
.zmdi-hourglass:before {
  content: '\f17b';
}
.zmdi-http:before {
  content: '\f17c';
}
.zmdi-image-alt:before {
  content: '\f17d';
}
.zmdi-image-o:before {
  content: '\f17e';
}
.zmdi-image:before {
  content: '\f17f';
}
.zmdi-inbox:before {
  content: '\f180';
}
.zmdi-invert-colors-off:before {
  content: '\f181';
}
.zmdi-invert-colors:before {
  content: '\f182';
}
.zmdi-key:before {
  content: '\f183';
}
.zmdi-label-alt-outline:before {
  content: '\f184';
}
.zmdi-label-alt:before {
  content: '\f185';
}
.zmdi-label-heart:before {
  content: '\f186';
}
.zmdi-label:before {
  content: '\f187';
}
.zmdi-labels:before {
  content: '\f188';
}
.zmdi-lamp:before {
  content: '\f189';
}
.zmdi-landscape:before {
  content: '\f18a';
}
.zmdi-layers-off:before {
  content: '\f18b';
}
.zmdi-layers:before {
  content: '\f18c';
}
.zmdi-library:before {
  content: '\f18d';
}
.zmdi-link:before {
  content: '\f18e';
}
.zmdi-lock-open:before {
  content: '\f18f';
}
.zmdi-lock-outline:before {
  content: '\f190';
}
.zmdi-lock:before {
  content: '\f191';
}
.zmdi-mail-reply-all:before {
  content: '\f192';
}
.zmdi-mail-reply:before {
  content: '\f193';
}
.zmdi-mail-send:before {
  content: '\f194';
}
.zmdi-mall:before {
  content: '\f195';
}
.zmdi-map:before {
  content: '\f196';
}
.zmdi-menu:before {
  content: '\f197';
}
.zmdi-money-box:before {
  content: '\f198';
}
.zmdi-money-off:before {
  content: '\f199';
}
.zmdi-money:before {
  content: '\f19a';
}
.zmdi-more-vert:before {
  content: '\f19b';
}
.zmdi-more:before {
  content: '\f19c';
}
.zmdi-movie-alt:before {
  content: '\f19d';
}
.zmdi-movie:before {
  content: '\f19e';
}
.zmdi-nature-people:before {
  content: '\f19f';
}
.zmdi-nature:before {
  content: '\f1a0';
}
.zmdi-navigation:before {
  content: '\f1a1';
}
.zmdi-open-in-browser:before {
  content: '\f1a2';
}
.zmdi-open-in-new:before {
  content: '\f1a3';
}
.zmdi-palette:before {
  content: '\f1a4';
}
.zmdi-parking:before {
  content: '\f1a5';
}
.zmdi-pin-account:before {
  content: '\f1a6';
}
.zmdi-pin-assistant:before {
  content: '\f1a7';
}
.zmdi-pin-drop:before {
  content: '\f1a8';
}
.zmdi-pin-help:before {
  content: '\f1a9';
}
.zmdi-pin-off:before {
  content: '\f1aa';
}
.zmdi-pin:before {
  content: '\f1ab';
}
.zmdi-pizza:before {
  content: '\f1ac';
}
.zmdi-plaster:before {
  content: '\f1ad';
}
.zmdi-power-setting:before {
  content: '\f1ae';
}
.zmdi-power:before {
  content: '\f1af';
}
.zmdi-print:before {
  content: '\f1b0';
}
.zmdi-puzzle-piece:before {
  content: '\f1b1';
}
.zmdi-quote:before {
  content: '\f1b2';
}
.zmdi-railway:before {
  content: '\f1b3';
}
.zmdi-receipt:before {
  content: '\f1b4';
}
.zmdi-refresh-alt:before {
  content: '\f1b5';
}
.zmdi-refresh-sync-alert:before {
  content: '\f1b6';
}
.zmdi-refresh-sync-off:before {
  content: '\f1b7';
}
.zmdi-refresh-sync:before {
  content: '\f1b8';
}
.zmdi-refresh:before {
  content: '\f1b9';
}
.zmdi-roller:before {
  content: '\f1ba';
}
.zmdi-ruler:before {
  content: '\f1bb';
}
.zmdi-scissors:before {
  content: '\f1bc';
}
.zmdi-screen-rotation-lock:before {
  content: '\f1bd';
}
.zmdi-screen-rotation:before {
  content: '\f1be';
}
.zmdi-search-for:before {
  content: '\f1bf';
}
.zmdi-search-in-file:before {
  content: '\f1c0';
}
.zmdi-search-in-page:before {
  content: '\f1c1';
}
.zmdi-search-replace:before {
  content: '\f1c2';
}
.zmdi-search:before {
  content: '\f1c3';
}
.zmdi-seat:before {
  content: '\f1c4';
}
.zmdi-settings-square:before {
  content: '\f1c5';
}
.zmdi-settings:before {
  content: '\f1c6';
}
.zmdi-shield-check:before {
  content: '\f1c7';
}
.zmdi-shield-security:before {
  content: '\f1c8';
}
.zmdi-shopping-basket:before {
  content: '\f1c9';
}
.zmdi-shopping-cart-plus:before {
  content: '\f1ca';
}
.zmdi-shopping-cart:before {
  content: '\f1cb';
}
.zmdi-sign-in:before {
  content: '\f1cc';
}
.zmdi-sort-amount-asc:before {
  content: '\f1cd';
}
.zmdi-sort-amount-desc:before {
  content: '\f1ce';
}
.zmdi-sort-asc:before {
  content: '\f1cf';
}
.zmdi-sort-desc:before {
  content: '\f1d0';
}
.zmdi-spellcheck:before {
  content: '\f1d1';
}
.zmdi-storage:before {
  content: '\f1d2';
}
.zmdi-store-24:before {
  content: '\f1d3';
}
.zmdi-store:before {
  content: '\f1d4';
}
.zmdi-subway:before {
  content: '\f1d5';
}
.zmdi-sun:before {
  content: '\f1d6';
}
.zmdi-tab-unselected:before {
  content: '\f1d7';
}
.zmdi-tab:before {
  content: '\f1d8';
}
.zmdi-tag-close:before {
  content: '\f1d9';
}
.zmdi-tag-more:before {
  content: '\f1da';
}
.zmdi-tag:before {
  content: '\f1db';
}
.zmdi-thumb-down:before {
  content: '\f1dc';
}
.zmdi-thumb-up-down:before {
  content: '\f1dd';
}
.zmdi-thumb-up:before {
  content: '\f1de';
}
.zmdi-ticket-star:before {
  content: '\f1df';
}
.zmdi-toll:before {
  content: '\f1e0';
}
.zmdi-toys:before {
  content: '\f1e1';
}
.zmdi-traffic:before {
  content: '\f1e2';
}
.zmdi-translate:before {
  content: '\f1e3';
}
.zmdi-triangle-down:before {
  content: '\f1e4';
}
.zmdi-triangle-up:before {
  content: '\f1e5';
}
.zmdi-truck:before {
  content: '\f1e6';
}
.zmdi-turning-sign:before {
  content: '\f1e7';
}
.zmdi-wallpaper:before {
  content: '\f1e8';
}
.zmdi-washing-machine:before {
  content: '\f1e9';
}
.zmdi-window-maximize:before {
  content: '\f1ea';
}
.zmdi-window-minimize:before {
  content: '\f1eb';
}
.zmdi-window-restore:before {
  content: '\f1ec';
}
.zmdi-wrench:before {
  content: '\f1ed';
}
.zmdi-zoom-in:before {
  content: '\f1ee';
}
.zmdi-zoom-out:before {
  content: '\f1ef';
}
.zmdi-alert-circle-o:before {
  content: '\f1f0';
}
.zmdi-alert-circle:before {
  content: '\f1f1';
}
.zmdi-alert-octagon:before {
  content: '\f1f2';
}
.zmdi-alert-polygon:before {
  content: '\f1f3';
}
.zmdi-alert-triangle:before {
  content: '\f1f4';
}
.zmdi-help-outline:before {
  content: '\f1f5';
}
.zmdi-help:before {
  content: '\f1f6';
}
.zmdi-info-outline:before {
  content: '\f1f7';
}
.zmdi-info:before {
  content: '\f1f8';
}
.zmdi-notifications-active:before {
  content: '\f1f9';
}
.zmdi-notifications-add:before {
  content: '\f1fa';
}
.zmdi-notifications-none:before {
  content: '\f1fb';
}
.zmdi-notifications-off:before {
  content: '\f1fc';
}
.zmdi-notifications-paused:before {
  content: '\f1fd';
}
.zmdi-notifications:before {
  content: '\f1fe';
}
.zmdi-account-add:before {
  content: '\f1ff';
}
.zmdi-account-box-mail:before {
  content: '\f200';
}
.zmdi-account-box-o:before {
  content: '\f201';
}
.zmdi-account-box-phone:before {
  content: '\f202';
}
.zmdi-account-box:before {
  content: '\f203';
}
.zmdi-account-calendar:before {
  content: '\f204';
}
.zmdi-account-circle:before {
  content: '\f205';
}
.zmdi-account-o:before {
  content: '\f206';
}
.zmdi-account:before {
  content: '\f207';
}
.zmdi-accounts-add:before {
  content: '\f208';
}
.zmdi-accounts-alt:before {
  content: '\f209';
}
.zmdi-accounts-list-alt:before {
  content: '\f20a';
}
.zmdi-accounts-list:before {
  content: '\f20b';
}
.zmdi-accounts-outline:before {
  content: '\f20c';
}
.zmdi-accounts:before {
  content: '\f20d';
}
.zmdi-face:before {
  content: '\f20e';
}
.zmdi-female:before {
  content: '\f20f';
}
.zmdi-male-alt:before {
  content: '\f210';
}
.zmdi-male-female:before {
  content: '\f211';
}
.zmdi-male:before {
  content: '\f212';
}
.zmdi-mood-bad:before {
  content: '\f213';
}
.zmdi-mood:before {
  content: '\f214';
}
.zmdi-run:before {
  content: '\f215';
}
.zmdi-walk:before {
  content: '\f216';
}
.zmdi-cloud-box:before {
  content: '\f217';
}
.zmdi-cloud-circle:before {
  content: '\f218';
}
.zmdi-cloud-done:before {
  content: '\f219';
}
.zmdi-cloud-download:before {
  content: '\f21a';
}
.zmdi-cloud-off:before {
  content: '\f21b';
}
.zmdi-cloud-outline-alt:before {
  content: '\f21c';
}
.zmdi-cloud-outline:before {
  content: '\f21d';
}
.zmdi-cloud-upload:before {
  content: '\f21e';
}
.zmdi-cloud:before {
  content: '\f21f';
}
.zmdi-download:before {
  content: '\f220';
}
.zmdi-file-plus:before {
  content: '\f221';
}
.zmdi-file-text:before {
  content: '\f222';
}
.zmdi-file:before {
  content: '\f223';
}
.zmdi-folder-outline:before {
  content: '\f224';
}
.zmdi-folder-person:before {
  content: '\f225';
}
.zmdi-folder-star-alt:before {
  content: '\f226';
}
.zmdi-folder-star:before {
  content: '\f227';
}
.zmdi-folder:before {
  content: '\f228';
}
.zmdi-gif:before {
  content: '\f229';
}
.zmdi-upload:before {
  content: '\f22a';
}
.zmdi-border-all:before {
  content: '\f22b';
}
.zmdi-border-bottom:before {
  content: '\f22c';
}
.zmdi-border-clear:before {
  content: '\f22d';
}
.zmdi-border-color:before {
  content: '\f22e';
}
.zmdi-border-horizontal:before {
  content: '\f22f';
}
.zmdi-border-inner:before {
  content: '\f230';
}
.zmdi-border-left:before {
  content: '\f231';
}
.zmdi-border-outer:before {
  content: '\f232';
}
.zmdi-border-right:before {
  content: '\f233';
}
.zmdi-border-style:before {
  content: '\f234';
}
.zmdi-border-top:before {
  content: '\f235';
}
.zmdi-border-vertical:before {
  content: '\f236';
}
.zmdi-copy:before {
  content: '\f237';
}
.zmdi-crop:before {
  content: '\f238';
}
.zmdi-format-align-center:before {
  content: '\f239';
}
.zmdi-format-align-justify:before {
  content: '\f23a';
}
.zmdi-format-align-left:before {
  content: '\f23b';
}
.zmdi-format-align-right:before {
  content: '\f23c';
}
.zmdi-format-bold:before {
  content: '\f23d';
}
.zmdi-format-clear-all:before {
  content: '\f23e';
}
.zmdi-format-clear:before {
  content: '\f23f';
}
.zmdi-format-color-fill:before {
  content: '\f240';
}
.zmdi-format-color-reset:before {
  content: '\f241';
}
.zmdi-format-color-text:before {
  content: '\f242';
}
.zmdi-format-indent-decrease:before {
  content: '\f243';
}
.zmdi-format-indent-increase:before {
  content: '\f244';
}
.zmdi-format-italic:before {
  content: '\f245';
}
.zmdi-format-line-spacing:before {
  content: '\f246';
}
.zmdi-format-list-bulleted:before {
  content: '\f247';
}
.zmdi-format-list-numbered:before {
  content: '\f248';
}
.zmdi-format-ltr:before {
  content: '\f249';
}
.zmdi-format-rtl:before {
  content: '\f24a';
}
.zmdi-format-size:before {
  content: '\f24b';
}
.zmdi-format-strikethrough-s:before {
  content: '\f24c';
}
.zmdi-format-strikethrough:before {
  content: '\f24d';
}
.zmdi-format-subject:before {
  content: '\f24e';
}
.zmdi-format-underlined:before {
  content: '\f24f';
}
.zmdi-format-valign-bottom:before {
  content: '\f250';
}
.zmdi-format-valign-center:before {
  content: '\f251';
}
.zmdi-format-valign-top:before {
  content: '\f252';
}
.zmdi-redo:before {
  content: '\f253';
}
.zmdi-select-all:before {
  content: '\f254';
}
.zmdi-space-bar:before {
  content: '\f255';
}
.zmdi-text-format:before {
  content: '\f256';
}
.zmdi-transform:before {
  content: '\f257';
}
.zmdi-undo:before {
  content: '\f258';
}
.zmdi-wrap-text:before {
  content: '\f259';
}
.zmdi-comment-alert:before {
  content: '\f25a';
}
.zmdi-comment-alt-text:before {
  content: '\f25b';
}
.zmdi-comment-alt:before {
  content: '\f25c';
}
.zmdi-comment-edit:before {
  content: '\f25d';
}
.zmdi-comment-image:before {
  content: '\f25e';
}
.zmdi-comment-list:before {
  content: '\f25f';
}
.zmdi-comment-more:before {
  content: '\f260';
}
.zmdi-comment-outline:before {
  content: '\f261';
}
.zmdi-comment-text-alt:before {
  content: '\f262';
}
.zmdi-comment-text:before {
  content: '\f263';
}
.zmdi-comment-video:before {
  content: '\f264';
}
.zmdi-comment:before {
  content: '\f265';
}
.zmdi-comments:before {
  content: '\f266';
}
.zmdi-check-all:before {
  content: '\f267';
}
.zmdi-check-circle-u:before {
  content: '\f268';
}
.zmdi-check-circle:before {
  content: '\f269';
}
.zmdi-check-square:before {
  content: '\f26a';
}
.zmdi-check:before {
  content: '\f26b';
}
.zmdi-circle-o:before {
  content: '\f26c';
}
.zmdi-circle:before {
  content: '\f26d';
}
.zmdi-dot-circle-alt:before {
  content: '\f26e';
}
.zmdi-dot-circle:before {
  content: '\f26f';
}
.zmdi-minus-circle-outline:before {
  content: '\f270';
}
.zmdi-minus-circle:before {
  content: '\f271';
}
.zmdi-minus-square:before {
  content: '\f272';
}
.zmdi-minus:before {
  content: '\f273';
}
.zmdi-plus-circle-o-duplicate:before {
  content: '\f274';
}
.zmdi-plus-circle-o:before {
  content: '\f275';
}
.zmdi-plus-circle:before {
  content: '\f276';
}
.zmdi-plus-square:before {
  content: '\f277';
}
.zmdi-plus:before {
  content: '\f278';
}
.zmdi-square-o:before {
  content: '\f279';
}
.zmdi-star-circle:before {
  content: '\f27a';
}
.zmdi-star-half:before {
  content: '\f27b';
}
.zmdi-star-outline:before {
  content: '\f27c';
}
.zmdi-star:before {
  content: '\f27d';
}
.zmdi-bluetooth-connected:before {
  content: '\f27e';
}
.zmdi-bluetooth-off:before {
  content: '\f27f';
}
.zmdi-bluetooth-search:before {
  content: '\f280';
}
.zmdi-bluetooth-setting:before {
  content: '\f281';
}
.zmdi-bluetooth:before {
  content: '\f282';
}
.zmdi-camera-add:before {
  content: '\f283';
}
.zmdi-camera-alt:before {
  content: '\f284';
}
.zmdi-camera-bw:before {
  content: '\f285';
}
.zmdi-camera-front:before {
  content: '\f286';
}
.zmdi-camera-mic:before {
  content: '\f287';
}
.zmdi-camera-party-mode:before {
  content: '\f288';
}
.zmdi-camera-rear:before {
  content: '\f289';
}
.zmdi-camera-roll:before {
  content: '\f28a';
}
.zmdi-camera-switch:before {
  content: '\f28b';
}
.zmdi-camera:before {
  content: '\f28c';
}
.zmdi-card-alert:before {
  content: '\f28d';
}
.zmdi-card-off:before {
  content: '\f28e';
}
.zmdi-card-sd:before {
  content: '\f28f';
}
.zmdi-card-sim:before {
  content: '\f290';
}
.zmdi-desktop-mac:before {
  content: '\f291';
}
.zmdi-desktop-windows:before {
  content: '\f292';
}
.zmdi-device-hub:before {
  content: '\f293';
}
.zmdi-devices-off:before {
  content: '\f294';
}
.zmdi-devices:before {
  content: '\f295';
}
.zmdi-dock:before {
  content: '\f296';
}
.zmdi-floppy:before {
  content: '\f297';
}
.zmdi-gamepad:before {
  content: '\f298';
}
.zmdi-gps-dot:before {
  content: '\f299';
}
.zmdi-gps-off:before {
  content: '\f29a';
}
.zmdi-gps:before {
  content: '\f29b';
}
.zmdi-headset-mic:before {
  content: '\f29c';
}
.zmdi-headset:before {
  content: '\f29d';
}
.zmdi-input-antenna:before {
  content: '\f29e';
}
.zmdi-input-composite:before {
  content: '\f29f';
}
.zmdi-input-hdmi:before {
  content: '\f2a0';
}
.zmdi-input-power:before {
  content: '\f2a1';
}
.zmdi-input-svideo:before {
  content: '\f2a2';
}
.zmdi-keyboard-hide:before {
  content: '\f2a3';
}
.zmdi-keyboard:before {
  content: '\f2a4';
}
.zmdi-laptop-chromebook:before {
  content: '\f2a5';
}
.zmdi-laptop-mac:before {
  content: '\f2a6';
}
.zmdi-laptop:before {
  content: '\f2a7';
}
.zmdi-mic-off:before {
  content: '\f2a8';
}
.zmdi-mic-outline:before {
  content: '\f2a9';
}
.zmdi-mic-setting:before {
  content: '\f2aa';
}
.zmdi-mic:before {
  content: '\f2ab';
}
.zmdi-mouse:before {
  content: '\f2ac';
}
.zmdi-network-alert:before {
  content: '\f2ad';
}
.zmdi-network-locked:before {
  content: '\f2ae';
}
.zmdi-network-off:before {
  content: '\f2af';
}
.zmdi-network-outline:before {
  content: '\f2b0';
}
.zmdi-network-setting:before {
  content: '\f2b1';
}
.zmdi-network:before {
  content: '\f2b2';
}
.zmdi-phone-bluetooth:before {
  content: '\f2b3';
}
.zmdi-phone-end:before {
  content: '\f2b4';
}
.zmdi-phone-forwarded:before {
  content: '\f2b5';
}
.zmdi-phone-in-talk:before {
  content: '\f2b6';
}
.zmdi-phone-locked:before {
  content: '\f2b7';
}
.zmdi-phone-missed:before {
  content: '\f2b8';
}
.zmdi-phone-msg:before {
  content: '\f2b9';
}
.zmdi-phone-paused:before {
  content: '\f2ba';
}
.zmdi-phone-ring:before {
  content: '\f2bb';
}
.zmdi-phone-setting:before {
  content: '\f2bc';
}
.zmdi-phone-sip:before {
  content: '\f2bd';
}
.zmdi-phone:before {
  content: '\f2be';
}
.zmdi-portable-wifi-changes:before {
  content: '\f2bf';
}
.zmdi-portable-wifi-off:before {
  content: '\f2c0';
}
.zmdi-portable-wifi:before {
  content: '\f2c1';
}
.zmdi-radio:before {
  content: '\f2c2';
}
.zmdi-reader:before {
  content: '\f2c3';
}
.zmdi-remote-control-alt:before {
  content: '\f2c4';
}
.zmdi-remote-control:before {
  content: '\f2c5';
}
.zmdi-router:before {
  content: '\f2c6';
}
.zmdi-scanner:before {
  content: '\f2c7';
}
.zmdi-smartphone-android:before {
  content: '\f2c8';
}
.zmdi-smartphone-download:before {
  content: '\f2c9';
}
.zmdi-smartphone-erase:before {
  content: '\f2ca';
}
.zmdi-smartphone-info:before {
  content: '\f2cb';
}
.zmdi-smartphone-iphone:before {
  content: '\f2cc';
}
.zmdi-smartphone-landscape-lock:before {
  content: '\f2cd';
}
.zmdi-smartphone-landscape:before {
  content: '\f2ce';
}
.zmdi-smartphone-lock:before {
  content: '\f2cf';
}
.zmdi-smartphone-portrait-lock:before {
  content: '\f2d0';
}
.zmdi-smartphone-ring:before {
  content: '\f2d1';
}
.zmdi-smartphone-setting:before {
  content: '\f2d2';
}
.zmdi-smartphone-setup:before {
  content: '\f2d3';
}
.zmdi-smartphone:before {
  content: '\f2d4';
}
.zmdi-speaker:before {
  content: '\f2d5';
}
.zmdi-tablet-android:before {
  content: '\f2d6';
}
.zmdi-tablet-mac:before {
  content: '\f2d7';
}
.zmdi-tablet:before {
  content: '\f2d8';
}
.zmdi-tv-alt-play:before {
  content: '\f2d9';
}
.zmdi-tv-list:before {
  content: '\f2da';
}
.zmdi-tv-play:before {
  content: '\f2db';
}
.zmdi-tv:before {
  content: '\f2dc';
}
.zmdi-usb:before {
  content: '\f2dd';
}
.zmdi-videocam-off:before {
  content: '\f2de';
}
.zmdi-videocam-switch:before {
  content: '\f2df';
}
.zmdi-videocam:before {
  content: '\f2e0';
}
.zmdi-watch:before {
  content: '\f2e1';
}
.zmdi-wifi-alt-2:before {
  content: '\f2e2';
}
.zmdi-wifi-alt:before {
  content: '\f2e3';
}
.zmdi-wifi-info:before {
  content: '\f2e4';
}
.zmdi-wifi-lock:before {
  content: '\f2e5';
}
.zmdi-wifi-off:before {
  content: '\f2e6';
}
.zmdi-wifi-outline:before {
  content: '\f2e7';
}
.zmdi-wifi:before {
  content: '\f2e8';
}
.zmdi-arrow-left-bottom:before {
  content: '\f2e9';
}
.zmdi-arrow-left:before {
  content: '\f2ea';
}
.zmdi-arrow-merge:before {
  content: '\f2eb';
}
.zmdi-arrow-missed:before {
  content: '\f2ec';
}
.zmdi-arrow-right-top:before {
  content: '\f2ed';
}
.zmdi-arrow-right:before {
  content: '\f2ee';
}
.zmdi-arrow-split:before {
  content: '\f2ef';
}
.zmdi-arrows:before {
  content: '\f2f0';
}
.zmdi-caret-down-circle:before {
  content: '\f2f1';
}
.zmdi-caret-down:before {
  content: '\f2f2';
}
.zmdi-caret-left-circle:before {
  content: '\f2f3';
}
.zmdi-caret-left:before {
  content: '\f2f4';
}
.zmdi-caret-right-circle:before {
  content: '\f2f5';
}
.zmdi-caret-right:before {
  content: '\f2f6';
}
.zmdi-caret-up-circle:before {
  content: '\f2f7';
}
.zmdi-caret-up:before {
  content: '\f2f8';
}
.zmdi-chevron-down:before {
  content: '\f2f9';
}
.zmdi-chevron-left:before {
  content: '\f2fa';
}
.zmdi-chevron-right:before {
  content: '\f2fb';
}
.zmdi-chevron-up:before {
  content: '\f2fc';
}
.zmdi-forward:before {
  content: '\f2fd';
}
.zmdi-long-arrow-down:before {
  content: '\f2fe';
}
.zmdi-long-arrow-left:before {
  content: '\f2ff';
}
.zmdi-long-arrow-return:before {
  content: '\f300';
}
.zmdi-long-arrow-right:before {
  content: '\f301';
}
.zmdi-long-arrow-tab:before {
  content: '\f302';
}
.zmdi-long-arrow-up:before {
  content: '\f303';
}
.zmdi-rotate-ccw:before {
  content: '\f304';
}
.zmdi-rotate-cw:before {
  content: '\f305';
}
.zmdi-rotate-left:before {
  content: '\f306';
}
.zmdi-rotate-right:before {
  content: '\f307';
}
.zmdi-square-down:before {
  content: '\f308';
}
.zmdi-square-right:before {
  content: '\f309';
}
.zmdi-swap-alt:before {
  content: '\f30a';
}
.zmdi-swap-vertical-circle:before {
  content: '\f30b';
}
.zmdi-swap-vertical:before {
  content: '\f30c';
}
.zmdi-swap:before {
  content: '\f30d';
}
.zmdi-trending-down:before {
  content: '\f30e';
}
.zmdi-trending-flat:before {
  content: '\f30f';
}
.zmdi-trending-up:before {
  content: '\f310';
}
.zmdi-unfold-less:before {
  content: '\f311';
}
.zmdi-unfold-more:before {
  content: '\f312';
}
.zmdi-apps:before {
  content: '\f313';
}
.zmdi-grid-off:before {
  content: '\f314';
}
.zmdi-grid:before {
  content: '\f315';
}
.zmdi-view-agenda:before {
  content: '\f316';
}
.zmdi-view-array:before {
  content: '\f317';
}
.zmdi-view-carousel:before {
  content: '\f318';
}
.zmdi-view-column:before {
  content: '\f319';
}
.zmdi-view-comfy:before {
  content: '\f31a';
}
.zmdi-view-compact:before {
  content: '\f31b';
}
.zmdi-view-dashboard:before {
  content: '\f31c';
}
.zmdi-view-day:before {
  content: '\f31d';
}
.zmdi-view-headline:before {
  content: '\f31e';
}
.zmdi-view-list-alt:before {
  content: '\f31f';
}
.zmdi-view-list:before {
  content: '\f320';
}
.zmdi-view-module:before {
  content: '\f321';
}
.zmdi-view-quilt:before {
  content: '\f322';
}
.zmdi-view-stream:before {
  content: '\f323';
}
.zmdi-view-subtitles:before {
  content: '\f324';
}
.zmdi-view-toc:before {
  content: '\f325';
}
.zmdi-view-web:before {
  content: '\f326';
}
.zmdi-view-week:before {
  content: '\f327';
}
.zmdi-widgets:before {
  content: '\f328';
}
.zmdi-alarm-check:before {
  content: '\f329';
}
.zmdi-alarm-off:before {
  content: '\f32a';
}
.zmdi-alarm-plus:before {
  content: '\f32b';
}
.zmdi-alarm-snooze:before {
  content: '\f32c';
}
.zmdi-alarm:before {
  content: '\f32d';
}
.zmdi-calendar-alt:before {
  content: '\f32e';
}
.zmdi-calendar-check:before {
  content: '\f32f';
}
.zmdi-calendar-close:before {
  content: '\f330';
}
.zmdi-calendar-note:before {
  content: '\f331';
}
.zmdi-calendar:before {
  content: '\f332';
}
.zmdi-time-countdown:before {
  content: '\f333';
}
.zmdi-time-interval:before {
  content: '\f334';
}
.zmdi-time-restore-setting:before {
  content: '\f335';
}
.zmdi-time-restore:before {
  content: '\f336';
}
.zmdi-time:before {
  content: '\f337';
}
.zmdi-timer-off:before {
  content: '\f338';
}
.zmdi-timer:before {
  content: '\f339';
}
.zmdi-android-alt:before {
  content: '\f33a';
}
.zmdi-android:before {
  content: '\f33b';
}
.zmdi-apple:before {
  content: '\f33c';
}
.zmdi-behance:before {
  content: '\f33d';
}
.zmdi-codepen:before {
  content: '\f33e';
}
.zmdi-dribbble:before {
  content: '\f33f';
}
.zmdi-dropbox:before {
  content: '\f340';
}
.zmdi-evernote:before {
  content: '\f341';
}
.zmdi-facebook-box:before {
  content: '\f342';
}
.zmdi-facebook:before {
  content: '\f343';
}
.zmdi-github-box:before {
  content: '\f344';
}
.zmdi-github:before {
  content: '\f345';
}
.zmdi-google-drive:before {
  content: '\f346';
}
.zmdi-google-earth:before {
  content: '\f347';
}
.zmdi-google-glass:before {
  content: '\f348';
}
.zmdi-google-maps:before {
  content: '\f349';
}
.zmdi-google-pages:before {
  content: '\f34a';
}
.zmdi-google-play:before {
  content: '\f34b';
}
.zmdi-google-plus-box:before {
  content: '\f34c';
}
.zmdi-google-plus:before {
  content: '\f34d';
}
.zmdi-google:before {
  content: '\f34e';
}
.zmdi-instagram:before {
  content: '\f34f';
}
.zmdi-language-css3:before {
  content: '\f350';
}
.zmdi-language-html5:before {
  content: '\f351';
}
.zmdi-language-javascript:before {
  content: '\f352';
}
.zmdi-language-python-alt:before {
  content: '\f353';
}
.zmdi-language-python:before {
  content: '\f354';
}
.zmdi-lastfm:before {
  content: '\f355';
}
.zmdi-linkedin-box:before {
  content: '\f356';
}
.zmdi-paypal:before {
  content: '\f357';
}
.zmdi-pinterest-box:before {
  content: '\f358';
}
.zmdi-pocket:before {
  content: '\f359';
}
.zmdi-polymer:before {
  content: '\f35a';
}
.zmdi-share:before {
  content: '\f35b';
}
.zmdi-stackoverflow:before {
  content: '\f35c';
}
.zmdi-steam-square:before {
  content: '\f35d';
}
.zmdi-steam:before {
  content: '\f35e';
}
.zmdi-twitter-box:before {
  content: '\f35f';
}
.zmdi-twitter:before {
  content: '\f360';
}
.zmdi-vk:before {
  content: '\f361';
}
.zmdi-wikipedia:before {
  content: '\f362';
}
.zmdi-windows:before {
  content: '\f363';
}
.zmdi-aspect-ratio-alt:before {
  content: '\f364';
}
.zmdi-aspect-ratio:before {
  content: '\f365';
}
.zmdi-blur-circular:before {
  content: '\f366';
}
.zmdi-blur-linear:before {
  content: '\f367';
}
.zmdi-blur-off:before {
  content: '\f368';
}
.zmdi-blur:before {
  content: '\f369';
}
.zmdi-brightness-2:before {
  content: '\f36a';
}
.zmdi-brightness-3:before {
  content: '\f36b';
}
.zmdi-brightness-4:before {
  content: '\f36c';
}
.zmdi-brightness-5:before {
  content: '\f36d';
}
.zmdi-brightness-6:before {
  content: '\f36e';
}
.zmdi-brightness-7:before {
  content: '\f36f';
}
.zmdi-brightness-auto:before {
  content: '\f370';
}
.zmdi-brightness-setting:before {
  content: '\f371';
}
.zmdi-broken-image:before {
  content: '\f372';
}
.zmdi-center-focus-strong:before {
  content: '\f373';
}
.zmdi-center-focus-weak:before {
  content: '\f374';
}
.zmdi-compare:before {
  content: '\f375';
}
.zmdi-crop-16-9:before {
  content: '\f376';
}
.zmdi-crop-3-2:before {
  content: '\f377';
}
.zmdi-crop-5-4:before {
  content: '\f378';
}
.zmdi-crop-7-5:before {
  content: '\f379';
}
.zmdi-crop-din:before {
  content: '\f37a';
}
.zmdi-crop-free:before {
  content: '\f37b';
}
.zmdi-crop-landscape:before {
  content: '\f37c';
}
.zmdi-crop-portrait:before {
  content: '\f37d';
}
.zmdi-crop-square:before {
  content: '\f37e';
}
.zmdi-exposure-alt:before {
  content: '\f37f';
}
.zmdi-exposure:before {
  content: '\f380';
}
.zmdi-filter-b-and-w:before {
  content: '\f381';
}
.zmdi-filter-center-focus:before {
  content: '\f382';
}
.zmdi-filter-frames:before {
  content: '\f383';
}
.zmdi-filter-tilt-shift:before {
  content: '\f384';
}
.zmdi-gradient:before {
  content: '\f385';
}
.zmdi-grain:before {
  content: '\f386';
}
.zmdi-graphic-eq:before {
  content: '\f387';
}
.zmdi-hdr-off:before {
  content: '\f388';
}
.zmdi-hdr-strong:before {
  content: '\f389';
}
.zmdi-hdr-weak:before {
  content: '\f38a';
}
.zmdi-hdr:before {
  content: '\f38b';
}
.zmdi-iridescent:before {
  content: '\f38c';
}
.zmdi-leak-off:before {
  content: '\f38d';
}
.zmdi-leak:before {
  content: '\f38e';
}
.zmdi-looks:before {
  content: '\f38f';
}
.zmdi-loupe:before {
  content: '\f390';
}
.zmdi-panorama-horizontal:before {
  content: '\f391';
}
.zmdi-panorama-vertical:before {
  content: '\f392';
}
.zmdi-panorama-wide-angle:before {
  content: '\f393';
}
.zmdi-photo-size-select-large:before {
  content: '\f394';
}
.zmdi-photo-size-select-small:before {
  content: '\f395';
}
.zmdi-picture-in-picture:before {
  content: '\f396';
}
.zmdi-slideshow:before {
  content: '\f397';
}
.zmdi-texture:before {
  content: '\f398';
}
.zmdi-tonality:before {
  content: '\f399';
}
.zmdi-vignette:before {
  content: '\f39a';
}
.zmdi-wb-auto:before {
  content: '\f39b';
}
.zmdi-eject-alt:before {
  content: '\f39c';
}
.zmdi-eject:before {
  content: '\f39d';
}
.zmdi-equalizer:before {
  content: '\f39e';
}
.zmdi-fast-forward:before {
  content: '\f39f';
}
.zmdi-fast-rewind:before {
  content: '\f3a0';
}
.zmdi-forward-10:before {
  content: '\f3a1';
}
.zmdi-forward-30:before {
  content: '\f3a2';
}
.zmdi-forward-5:before {
  content: '\f3a3';
}
.zmdi-hearing:before {
  content: '\f3a4';
}
.zmdi-pause-circle-outline:before {
  content: '\f3a5';
}
.zmdi-pause-circle:before {
  content: '\f3a6';
}
.zmdi-pause:before {
  content: '\f3a7';
}
.zmdi-play-circle-outline:before {
  content: '\f3a8';
}
.zmdi-play-circle:before {
  content: '\f3a9';
}
.zmdi-play:before {
  content: '\f3aa';
}
.zmdi-playlist-audio:before {
  content: '\f3ab';
}
.zmdi-playlist-plus:before {
  content: '\f3ac';
}
.zmdi-repeat-one:before {
  content: '\f3ad';
}
.zmdi-repeat:before {
  content: '\f3ae';
}
.zmdi-replay-10:before {
  content: '\f3af';
}
.zmdi-replay-30:before {
  content: '\f3b0';
}
.zmdi-replay-5:before {
  content: '\f3b1';
}
.zmdi-replay:before {
  content: '\f3b2';
}
.zmdi-shuffle:before {
  content: '\f3b3';
}
.zmdi-skip-next:before {
  content: '\f3b4';
}
.zmdi-skip-previous:before {
  content: '\f3b5';
}
.zmdi-stop:before {
  content: '\f3b6';
}
.zmdi-surround-sound:before {
  content: '\f3b7';
}
.zmdi-tune:before {
  content: '\f3b8';
}
.zmdi-volume-down:before {
  content: '\f3b9';
}
.zmdi-volume-mute:before {
  content: '\f3ba';
}
.zmdi-volume-off:before {
  content: '\f3bb';
}
.zmdi-volume-up:before {
  content: '\f3bc';
}
.zmdi-n-1-square:before {
  content: '\f3bd';
}
.zmdi-n-2-square:before {
  content: '\f3be';
}
.zmdi-n-3-square:before {
  content: '\f3bf';
}
.zmdi-n-4-square:before {
  content: '\f3c0';
}
.zmdi-n-5-square:before {
  content: '\f3c1';
}
.zmdi-n-6-square:before {
  content: '\f3c2';
}
.zmdi-neg-1:before {
  content: '\f3c3';
}
.zmdi-neg-2:before {
  content: '\f3c4';
}
.zmdi-plus-1:before {
  content: '\f3c5';
}
.zmdi-plus-2:before {
  content: '\f3c6';
}
.zmdi-sec-10:before {
  content: '\f3c7';
}
.zmdi-sec-3:before {
  content: '\f3c8';
}
.zmdi-zero:before {
  content: '\f3c9';
}
.zmdi-airline-seat-flat-angled:before {
  content: '\f3ca';
}
.zmdi-airline-seat-flat:before {
  content: '\f3cb';
}
.zmdi-airline-seat-individual-suite:before {
  content: '\f3cc';
}
.zmdi-airline-seat-legroom-extra:before {
  content: '\f3cd';
}
.zmdi-airline-seat-legroom-normal:before {
  content: '\f3ce';
}
.zmdi-airline-seat-legroom-reduced:before {
  content: '\f3cf';
}
.zmdi-airline-seat-recline-extra:before {
  content: '\f3d0';
}
.zmdi-airline-seat-recline-normal:before {
  content: '\f3d1';
}
.zmdi-airplay:before {
  content: '\f3d2';
}
.zmdi-closed-caption:before {
  content: '\f3d3';
}
.zmdi-confirmation-number:before {
  content: '\f3d4';
}
.zmdi-developer-board:before {
  content: '\f3d5';
}
.zmdi-disc-full:before {
  content: '\f3d6';
}
.zmdi-explicit:before {
  content: '\f3d7';
}
.zmdi-flight-land:before {
  content: '\f3d8';
}
.zmdi-flight-takeoff:before {
  content: '\f3d9';
}
.zmdi-flip-to-back:before {
  content: '\f3da';
}
.zmdi-flip-to-front:before {
  content: '\f3db';
}
.zmdi-group-work:before {
  content: '\f3dc';
}
.zmdi-hd:before {
  content: '\f3dd';
}
.zmdi-hq:before {
  content: '\f3de';
}
.zmdi-markunread-mailbox:before {
  content: '\f3df';
}
.zmdi-memory:before {
  content: '\f3e0';
}
.zmdi-nfc:before {
  content: '\f3e1';
}
.zmdi-play-for-work:before {
  content: '\f3e2';
}
.zmdi-power-input:before {
  content: '\f3e3';
}
.zmdi-present-to-all:before {
  content: '\f3e4';
}
.zmdi-satellite:before {
  content: '\f3e5';
}
.zmdi-tap-and-play:before {
  content: '\f3e6';
}
.zmdi-vibration:before {
  content: '\f3e7';
}
.zmdi-voicemail:before {
  content: '\f3e8';
}
.zmdi-group:before {
  content: '\f3e9';
}
.zmdi-rss:before {
  content: '\f3ea';
}
.zmdi-shape:before {
  content: '\f3eb';
}
.zmdi-spinner:before {
  content: '\f3ec';
}
.zmdi-ungroup:before {
  content: '\f3ed';
}
.zmdi-500px:before {
  content: '\f3ee';
}
.zmdi-8tracks:before {
  content: '\f3ef';
}
.zmdi-amazon:before {
  content: '\f3f0';
}
.zmdi-blogger:before {
  content: '\f3f1';
}
.zmdi-delicious:before {
  content: '\f3f2';
}
.zmdi-disqus:before {
  content: '\f3f3';
}
.zmdi-flattr:before {
  content: '\f3f4';
}
.zmdi-flickr:before {
  content: '\f3f5';
}
.zmdi-github-alt:before {
  content: '\f3f6';
}
.zmdi-google-old:before {
  content: '\f3f7';
}
.zmdi-linkedin:before {
  content: '\f3f8';
}
.zmdi-odnoklassniki:before {
  content: '\f3f9';
}
.zmdi-outlook:before {
  content: '\f3fa';
}
.zmdi-paypal-alt:before {
  content: '\f3fb';
}
.zmdi-pinterest:before {
  content: '\f3fc';
}
.zmdi-playstation:before {
  content: '\f3fd';
}
.zmdi-reddit:before {
  content: '\f3fe';
}
.zmdi-skype:before {
  content: '\f3ff';
}
.zmdi-slideshare:before {
  content: '\f400';
}
.zmdi-soundcloud:before {
  content: '\f401';
}
.zmdi-tumblr:before {
  content: '\f402';
}
.zmdi-twitch:before {
  content: '\f403';
}
.zmdi-vimeo:before {
  content: '\f404';
}
.zmdi-whatsapp:before {
  content: '\f405';
}
.zmdi-xbox:before {
  content: '\f406';
}
.zmdi-yahoo:before {
  content: '\f407';
}
.zmdi-youtube-play:before {
  content: '\f408';
}
.zmdi-youtube:before {
  content: '\f409';
}
.zmdi-3d-rotation:before {
  content: '\f101';
}
.zmdi-airplane-off:before {
  content: '\f102';
}
.zmdi-airplane:before {
  content: '\f103';
}
.zmdi-album:before {
  content: '\f104';
}
.zmdi-archive:before {
  content: '\f105';
}
.zmdi-assignment-account:before {
  content: '\f106';
}
.zmdi-assignment-alert:before {
  content: '\f107';
}
.zmdi-assignment-check:before {
  content: '\f108';
}
.zmdi-assignment-o:before {
  content: '\f109';
}
.zmdi-assignment-return:before {
  content: '\f10a';
}
.zmdi-assignment-returned:before {
  content: '\f10b';
}
.zmdi-assignment:before {
  content: '\f10c';
}
.zmdi-attachment-alt:before {
  content: '\f10d';
}
.zmdi-attachment:before {
  content: '\f10e';
}
.zmdi-audio:before {
  content: '\f10f';
}
.zmdi-badge-check:before {
  content: '\f110';
}
.zmdi-balance-wallet:before {
  content: '\f111';
}
.zmdi-balance:before {
  content: '\f112';
}
.zmdi-battery-alert:before {
  content: '\f113';
}
.zmdi-battery-flash:before {
  content: '\f114';
}
.zmdi-battery-unknown:before {
  content: '\f115';
}
.zmdi-battery:before {
  content: '\f116';
}
.zmdi-bike:before {
  content: '\f117';
}
.zmdi-block-alt:before {
  content: '\f118';
}
.zmdi-block:before {
  content: '\f119';
}
.zmdi-boat:before {
  content: '\f11a';
}
.zmdi-book-image:before {
  content: '\f11b';
}
.zmdi-book:before {
  content: '\f11c';
}
.zmdi-bookmark-outline:before {
  content: '\f11d';
}
.zmdi-bookmark:before {
  content: '\f11e';
}
.zmdi-brush:before {
  content: '\f11f';
}
.zmdi-bug:before {
  content: '\f120';
}
.zmdi-bus:before {
  content: '\f121';
}
.zmdi-cake:before {
  content: '\f122';
}
.zmdi-car-taxi:before {
  content: '\f123';
}
.zmdi-car-wash:before {
  content: '\f124';
}
.zmdi-car:before {
  content: '\f125';
}
.zmdi-card-giftcard:before {
  content: '\f126';
}
.zmdi-card-membership:before {
  content: '\f127';
}
.zmdi-card-travel:before {
  content: '\f128';
}
.zmdi-card:before {
  content: '\f129';
}
.zmdi-case-check:before {
  content: '\f12a';
}
.zmdi-case-download:before {
  content: '\f12b';
}
.zmdi-case-play:before {
  content: '\f12c';
}
.zmdi-case:before {
  content: '\f12d';
}
.zmdi-cast-connected:before {
  content: '\f12e';
}
.zmdi-cast:before {
  content: '\f12f';
}
.zmdi-chart-donut:before {
  content: '\f130';
}
.zmdi-chart:before {
  content: '\f131';
}
.zmdi-city-alt:before {
  content: '\f132';
}
.zmdi-city:before {
  content: '\f133';
}
.zmdi-close-circle-o:before {
  content: '\f134';
}
.zmdi-close-circle:before {
  content: '\f135';
}
.zmdi-close:before {
  content: '\f136';
}
.zmdi-cocktail:before {
  content: '\f137';
}
.zmdi-code-setting:before {
  content: '\f138';
}
.zmdi-code-smartphone:before {
  content: '\f139';
}
.zmdi-code:before {
  content: '\f13a';
}
.zmdi-coffee:before {
  content: '\f13b';
}
.zmdi-collection-bookmark:before {
  content: '\f13c';
}
.zmdi-collection-case-play:before {
  content: '\f13d';
}
.zmdi-collection-folder-image:before {
  content: '\f13e';
}
.zmdi-collection-image-o:before {
  content: '\f13f';
}
.zmdi-collection-image:before {
  content: '\f140';
}
.zmdi-collection-item-1:before {
  content: '\f141';
}
.zmdi-collection-item-2:before {
  content: '\f142';
}
.zmdi-collection-item-3:before {
  content: '\f143';
}
.zmdi-collection-item-4:before {
  content: '\f144';
}
.zmdi-collection-item-5:before {
  content: '\f145';
}
.zmdi-collection-item-6:before {
  content: '\f146';
}
.zmdi-collection-item-7:before {
  content: '\f147';
}
.zmdi-collection-item-8:before {
  content: '\f148';
}
.zmdi-collection-item-9-plus:before {
  content: '\f149';
}
.zmdi-collection-item-9:before {
  content: '\f14a';
}
.zmdi-collection-item:before {
  content: '\f14b';
}
.zmdi-collection-music:before {
  content: '\f14c';
}
.zmdi-collection-pdf:before {
  content: '\f14d';
}
.zmdi-collection-plus:before {
  content: '\f14e';
}
.zmdi-collection-speaker:before {
  content: '\f14f';
}
.zmdi-collection-text:before {
  content: '\f150';
}
.zmdi-collection-video:before {
  content: '\f151';
}
.zmdi-compass:before {
  content: '\f152';
}
.zmdi-cutlery:before {
  content: '\f153';
}
.zmdi-delete:before {
  content: '\f154';
}
.zmdi-dialpad:before {
  content: '\f155';
}
.zmdi-dns:before {
  content: '\f156';
}
.zmdi-drink:before {
  content: '\f157';
}
.zmdi-edit:before {
  content: '\f158';
}
.zmdi-email-open:before {
  content: '\f159';
}
.zmdi-email:before {
  content: '\f15a';
}
.zmdi-eye-off:before {
  content: '\f15b';
}
.zmdi-eye:before {
  content: '\f15c';
}
.zmdi-eyedropper:before {
  content: '\f15d';
}
.zmdi-favorite-outline:before {
  content: '\f15e';
}
.zmdi-favorite:before {
  content: '\f15f';
}
.zmdi-filter-list:before {
  content: '\f160';
}
.zmdi-fire:before {
  content: '\f161';
}
.zmdi-flag:before {
  content: '\f162';
}
.zmdi-flare:before {
  content: '\f163';
}
.zmdi-flash-auto:before {
  content: '\f164';
}
.zmdi-flash-off:before {
  content: '\f165';
}
.zmdi-flash:before {
  content: '\f166';
}
.zmdi-flip:before {
  content: '\f167';
}
.zmdi-flower-alt:before {
  content: '\f168';
}
.zmdi-flower:before {
  content: '\f169';
}
.zmdi-font:before {
  content: '\f16a';
}
.zmdi-fullscreen-alt:before {
  content: '\f16b';
}
.zmdi-fullscreen-exit:before {
  content: '\f16c';
}
.zmdi-fullscreen:before {
  content: '\f16d';
}
.zmdi-functions:before {
  content: '\f16e';
}
.zmdi-gas-station:before {
  content: '\f16f';
}
.zmdi-gesture:before {
  content: '\f170';
}
.zmdi-globe-alt:before {
  content: '\f171';
}
.zmdi-globe-lock:before {
  content: '\f172';
}
.zmdi-globe:before {
  content: '\f173';
}
.zmdi-graduation-cap:before {
  content: '\f174';
}
.zmdi-home:before {
  content: '\f175';
}
.zmdi-hospital-alt:before {
  content: '\f176';
}
.zmdi-hospital:before {
  content: '\f177';
}
.zmdi-hotel:before {
  content: '\f178';
}
.zmdi-hourglass-alt:before {
  content: '\f179';
}
.zmdi-hourglass-outline:before {
  content: '\f17a';
}
.zmdi-hourglass:before {
  content: '\f17b';
}
.zmdi-http:before {
  content: '\f17c';
}
.zmdi-image-alt:before {
  content: '\f17d';
}
.zmdi-image-o:before {
  content: '\f17e';
}
.zmdi-image:before {
  content: '\f17f';
}
.zmdi-inbox:before {
  content: '\f180';
}
.zmdi-invert-colors-off:before {
  content: '\f181';
}
.zmdi-invert-colors:before {
  content: '\f182';
}
.zmdi-key:before {
  content: '\f183';
}
.zmdi-label-alt-outline:before {
  content: '\f184';
}
.zmdi-label-alt:before {
  content: '\f185';
}
.zmdi-label-heart:before {
  content: '\f186';
}
.zmdi-label:before {
  content: '\f187';
}
.zmdi-labels:before {
  content: '\f188';
}
.zmdi-lamp:before {
  content: '\f189';
}
.zmdi-landscape:before {
  content: '\f18a';
}
.zmdi-layers-off:before {
  content: '\f18b';
}
.zmdi-layers:before {
  content: '\f18c';
}
.zmdi-library:before {
  content: '\f18d';
}
.zmdi-link:before {
  content: '\f18e';
}
.zmdi-lock-open:before {
  content: '\f18f';
}
.zmdi-lock-outline:before {
  content: '\f190';
}
.zmdi-lock:before {
  content: '\f191';
}
.zmdi-mail-reply-all:before {
  content: '\f192';
}
.zmdi-mail-reply:before {
  content: '\f193';
}
.zmdi-mail-send:before {
  content: '\f194';
}
.zmdi-mall:before {
  content: '\f195';
}
.zmdi-map:before {
  content: '\f196';
}
.zmdi-menu:before {
  content: '\f197';
}
.zmdi-money-box:before {
  content: '\f198';
}
.zmdi-money-off:before {
  content: '\f199';
}
.zmdi-money:before {
  content: '\f19a';
}
.zmdi-more-vert:before {
  content: '\f19b';
}
.zmdi-more:before {
  content: '\f19c';
}
.zmdi-movie-alt:before {
  content: '\f19d';
}
.zmdi-movie:before {
  content: '\f19e';
}
.zmdi-nature-people:before {
  content: '\f19f';
}
.zmdi-nature:before {
  content: '\f1a0';
}
.zmdi-navigation:before {
  content: '\f1a1';
}
.zmdi-open-in-browser:before {
  content: '\f1a2';
}
.zmdi-open-in-new:before {
  content: '\f1a3';
}
.zmdi-palette:before {
  content: '\f1a4';
}
.zmdi-parking:before {
  content: '\f1a5';
}
.zmdi-pin-account:before {
  content: '\f1a6';
}
.zmdi-pin-assistant:before {
  content: '\f1a7';
}
.zmdi-pin-drop:before {
  content: '\f1a8';
}
.zmdi-pin-help:before {
  content: '\f1a9';
}
.zmdi-pin-off:before {
  content: '\f1aa';
}
.zmdi-pin:before {
  content: '\f1ab';
}
.zmdi-pizza:before {
  content: '\f1ac';
}
.zmdi-plaster:before {
  content: '\f1ad';
}
.zmdi-power-setting:before {
  content: '\f1ae';
}
.zmdi-power:before {
  content: '\f1af';
}
.zmdi-print:before {
  content: '\f1b0';
}
.zmdi-puzzle-piece:before {
  content: '\f1b1';
}
.zmdi-quote:before {
  content: '\f1b2';
}
.zmdi-railway:before {
  content: '\f1b3';
}
.zmdi-receipt:before {
  content: '\f1b4';
}
.zmdi-refresh-alt:before {
  content: '\f1b5';
}
.zmdi-refresh-sync-alert:before {
  content: '\f1b6';
}
.zmdi-refresh-sync-off:before {
  content: '\f1b7';
}
.zmdi-refresh-sync:before {
  content: '\f1b8';
}
.zmdi-refresh:before {
  content: '\f1b9';
}
.zmdi-roller:before {
  content: '\f1ba';
}
.zmdi-ruler:before {
  content: '\f1bb';
}
.zmdi-scissors:before {
  content: '\f1bc';
}
.zmdi-screen-rotation-lock:before {
  content: '\f1bd';
}
.zmdi-screen-rotation:before {
  content: '\f1be';
}
.zmdi-search-for:before {
  content: '\f1bf';
}
.zmdi-search-in-file:before {
  content: '\f1c0';
}
.zmdi-search-in-page:before {
  content: '\f1c1';
}
.zmdi-search-replace:before {
  content: '\f1c2';
}
.zmdi-search:before {
  content: '\f1c3';
}
.zmdi-seat:before {
  content: '\f1c4';
}
.zmdi-settings-square:before {
  content: '\f1c5';
}
.zmdi-settings:before {
  content: '\f1c6';
}
.zmdi-shield-check:before {
  content: '\f1c7';
}
.zmdi-shield-security:before {
  content: '\f1c8';
}
.zmdi-shopping-basket:before {
  content: '\f1c9';
}
.zmdi-shopping-cart-plus:before {
  content: '\f1ca';
}
.zmdi-shopping-cart:before {
  content: '\f1cb';
}
.zmdi-sign-in:before {
  content: '\f1cc';
}
.zmdi-sort-amount-asc:before {
  content: '\f1cd';
}
.zmdi-sort-amount-desc:before {
  content: '\f1ce';
}
.zmdi-sort-asc:before {
  content: '\f1cf';
}
.zmdi-sort-desc:before {
  content: '\f1d0';
}
.zmdi-spellcheck:before {
  content: '\f1d1';
}
.zmdi-storage:before {
  content: '\f1d2';
}
.zmdi-store-24:before {
  content: '\f1d3';
}
.zmdi-store:before {
  content: '\f1d4';
}
.zmdi-subway:before {
  content: '\f1d5';
}
.zmdi-sun:before {
  content: '\f1d6';
}
.zmdi-tab-unselected:before {
  content: '\f1d7';
}
.zmdi-tab:before {
  content: '\f1d8';
}
.zmdi-tag-close:before {
  content: '\f1d9';
}
.zmdi-tag-more:before {
  content: '\f1da';
}
.zmdi-tag:before {
  content: '\f1db';
}
.zmdi-thumb-down:before {
  content: '\f1dc';
}
.zmdi-thumb-up-down:before {
  content: '\f1dd';
}
.zmdi-thumb-up:before {
  content: '\f1de';
}
.zmdi-ticket-star:before {
  content: '\f1df';
}
.zmdi-toll:before {
  content: '\f1e0';
}
.zmdi-toys:before {
  content: '\f1e1';
}
.zmdi-traffic:before {
  content: '\f1e2';
}
.zmdi-translate:before {
  content: '\f1e3';
}
.zmdi-triangle-down:before {
  content: '\f1e4';
}
.zmdi-triangle-up:before {
  content: '\f1e5';
}
.zmdi-truck:before {
  content: '\f1e6';
}
.zmdi-turning-sign:before {
  content: '\f1e7';
}
.zmdi-wallpaper:before {
  content: '\f1e8';
}
.zmdi-washing-machine:before {
  content: '\f1e9';
}
.zmdi-window-maximize:before {
  content: '\f1ea';
}
.zmdi-window-minimize:before {
  content: '\f1eb';
}
.zmdi-window-restore:before {
  content: '\f1ec';
}
.zmdi-wrench:before {
  content: '\f1ed';
}
.zmdi-zoom-in:before {
  content: '\f1ee';
}
.zmdi-zoom-out:before {
  content: '\f1ef';
}
.zmdi-alert-circle-o:before {
  content: '\f1f0';
}
.zmdi-alert-circle:before {
  content: '\f1f1';
}
.zmdi-alert-octagon:before {
  content: '\f1f2';
}
.zmdi-alert-polygon:before {
  content: '\f1f3';
}
.zmdi-alert-triangle:before {
  content: '\f1f4';
}
.zmdi-help-outline:before {
  content: '\f1f5';
}
.zmdi-help:before {
  content: '\f1f6';
}
.zmdi-info-outline:before {
  content: '\f1f7';
}
.zmdi-info:before {
  content: '\f1f8';
}
.zmdi-notifications-active:before {
  content: '\f1f9';
}
.zmdi-notifications-add:before {
  content: '\f1fa';
}
.zmdi-notifications-none:before {
  content: '\f1fb';
}
.zmdi-notifications-off:before {
  content: '\f1fc';
}
.zmdi-notifications-paused:before {
  content: '\f1fd';
}
.zmdi-notifications:before {
  content: '\f1fe';
}
.zmdi-account-add:before {
  content: '\f1ff';
}
.zmdi-account-box-mail:before {
  content: '\f200';
}
.zmdi-account-box-o:before {
  content: '\f201';
}
.zmdi-account-box-phone:before {
  content: '\f202';
}
.zmdi-account-box:before {
  content: '\f203';
}
.zmdi-account-calendar:before {
  content: '\f204';
}
.zmdi-account-circle:before {
  content: '\f205';
}
.zmdi-account-o:before {
  content: '\f206';
}
.zmdi-account:before {
  content: '\f207';
}
.zmdi-accounts-add:before {
  content: '\f208';
}
.zmdi-accounts-alt:before {
  content: '\f209';
}
.zmdi-accounts-list-alt:before {
  content: '\f20a';
}
.zmdi-accounts-list:before {
  content: '\f20b';
}
.zmdi-accounts-outline:before {
  content: '\f20c';
}
.zmdi-accounts:before {
  content: '\f20d';
}
.zmdi-face:before {
  content: '\f20e';
}
.zmdi-female:before {
  content: '\f20f';
}
.zmdi-male-alt:before {
  content: '\f210';
}
.zmdi-male-female:before {
  content: '\f211';
}
.zmdi-male:before {
  content: '\f212';
}
.zmdi-mood-bad:before {
  content: '\f213';
}
.zmdi-mood:before {
  content: '\f214';
}
.zmdi-run:before {
  content: '\f215';
}
.zmdi-walk:before {
  content: '\f216';
}
.zmdi-cloud-box:before {
  content: '\f217';
}
.zmdi-cloud-circle:before {
  content: '\f218';
}
.zmdi-cloud-done:before {
  content: '\f219';
}
.zmdi-cloud-download:before {
  content: '\f21a';
}
.zmdi-cloud-off:before {
  content: '\f21b';
}
.zmdi-cloud-outline-alt:before {
  content: '\f21c';
}
.zmdi-cloud-outline:before {
  content: '\f21d';
}
.zmdi-cloud-upload:before {
  content: '\f21e';
}
.zmdi-cloud:before {
  content: '\f21f';
}
.zmdi-download:before {
  content: '\f220';
}
.zmdi-file-plus:before {
  content: '\f221';
}
.zmdi-file-text:before {
  content: '\f222';
}
.zmdi-file:before {
  content: '\f223';
}
.zmdi-folder-outline:before {
  content: '\f224';
}
.zmdi-folder-person:before {
  content: '\f225';
}
.zmdi-folder-star-alt:before {
  content: '\f226';
}
.zmdi-folder-star:before {
  content: '\f227';
}
.zmdi-folder:before {
  content: '\f228';
}
.zmdi-gif:before {
  content: '\f229';
}
.zmdi-upload:before {
  content: '\f22a';
}
.zmdi-border-all:before {
  content: '\f22b';
}
.zmdi-border-bottom:before {
  content: '\f22c';
}
.zmdi-border-clear:before {
  content: '\f22d';
}
.zmdi-border-color:before {
  content: '\f22e';
}
.zmdi-border-horizontal:before {
  content: '\f22f';
}
.zmdi-border-inner:before {
  content: '\f230';
}
.zmdi-border-left:before {
  content: '\f231';
}
.zmdi-border-outer:before {
  content: '\f232';
}
.zmdi-border-right:before {
  content: '\f233';
}
.zmdi-border-style:before {
  content: '\f234';
}
.zmdi-border-top:before {
  content: '\f235';
}
.zmdi-border-vertical:before {
  content: '\f236';
}
.zmdi-copy:before {
  content: '\f237';
}
.zmdi-crop:before {
  content: '\f238';
}
.zmdi-format-align-center:before {
  content: '\f239';
}
.zmdi-format-align-justify:before {
  content: '\f23a';
}
.zmdi-format-align-left:before {
  content: '\f23b';
}
.zmdi-format-align-right:before {
  content: '\f23c';
}
.zmdi-format-bold:before {
  content: '\f23d';
}
.zmdi-format-clear-all:before {
  content: '\f23e';
}
.zmdi-format-clear:before {
  content: '\f23f';
}
.zmdi-format-color-fill:before {
  content: '\f240';
}
.zmdi-format-color-reset:before {
  content: '\f241';
}
.zmdi-format-color-text:before {
  content: '\f242';
}
.zmdi-format-indent-decrease:before {
  content: '\f243';
}
.zmdi-format-indent-increase:before {
  content: '\f244';
}
.zmdi-format-italic:before {
  content: '\f245';
}
.zmdi-format-line-spacing:before {
  content: '\f246';
}
.zmdi-format-list-bulleted:before {
  content: '\f247';
}
.zmdi-format-list-numbered:before {
  content: '\f248';
}
.zmdi-format-ltr:before {
  content: '\f249';
}
.zmdi-format-rtl:before {
  content: '\f24a';
}
.zmdi-format-size:before {
  content: '\f24b';
}
.zmdi-format-strikethrough-s:before {
  content: '\f24c';
}
.zmdi-format-strikethrough:before {
  content: '\f24d';
}
.zmdi-format-subject:before {
  content: '\f24e';
}
.zmdi-format-underlined:before {
  content: '\f24f';
}
.zmdi-format-valign-bottom:before {
  content: '\f250';
}
.zmdi-format-valign-center:before {
  content: '\f251';
}
.zmdi-format-valign-top:before {
  content: '\f252';
}
.zmdi-redo:before {
  content: '\f253';
}
.zmdi-select-all:before {
  content: '\f254';
}
.zmdi-space-bar:before {
  content: '\f255';
}
.zmdi-text-format:before {
  content: '\f256';
}
.zmdi-transform:before {
  content: '\f257';
}
.zmdi-undo:before {
  content: '\f258';
}
.zmdi-wrap-text:before {
  content: '\f259';
}
.zmdi-comment-alert:before {
  content: '\f25a';
}
.zmdi-comment-alt-text:before {
  content: '\f25b';
}
.zmdi-comment-alt:before {
  content: '\f25c';
}
.zmdi-comment-edit:before {
  content: '\f25d';
}
.zmdi-comment-image:before {
  content: '\f25e';
}
.zmdi-comment-list:before {
  content: '\f25f';
}
.zmdi-comment-more:before {
  content: '\f260';
}
.zmdi-comment-outline:before {
  content: '\f261';
}
.zmdi-comment-text-alt:before {
  content: '\f262';
}
.zmdi-comment-text:before {
  content: '\f263';
}
.zmdi-comment-video:before {
  content: '\f264';
}
.zmdi-comment:before {
  content: '\f265';
}
.zmdi-comments:before {
  content: '\f266';
}
.zmdi-check-all:before {
  content: '\f267';
}
.zmdi-check-circle-u:before {
  content: '\f268';
}
.zmdi-check-circle:before {
  content: '\f269';
}
.zmdi-check-square:before {
  content: '\f26a';
}
.zmdi-check:before {
  content: '\f26b';
}
.zmdi-circle-o:before {
  content: '\f26c';
}
.zmdi-circle:before {
  content: '\f26d';
}
.zmdi-dot-circle-alt:before {
  content: '\f26e';
}
.zmdi-dot-circle:before {
  content: '\f26f';
}
.zmdi-minus-circle-outline:before {
  content: '\f270';
}
.zmdi-minus-circle:before {
  content: '\f271';
}
.zmdi-minus-square:before {
  content: '\f272';
}
.zmdi-minus:before {
  content: '\f273';
}
.zmdi-plus-circle-o-duplicate:before {
  content: '\f274';
}
.zmdi-plus-circle-o:before {
  content: '\f275';
}
.zmdi-plus-circle:before {
  content: '\f276';
}
.zmdi-plus-square:before {
  content: '\f277';
}
.zmdi-plus:before {
  content: '\f278';
}
.zmdi-square-o:before {
  content: '\f279';
}
.zmdi-star-circle:before {
  content: '\f27a';
}
.zmdi-star-half:before {
  content: '\f27b';
}
.zmdi-star-outline:before {
  content: '\f27c';
}
.zmdi-star:before {
  content: '\f27d';
}
.zmdi-bluetooth-connected:before {
  content: '\f27e';
}
.zmdi-bluetooth-off:before {
  content: '\f27f';
}
.zmdi-bluetooth-search:before {
  content: '\f280';
}
.zmdi-bluetooth-setting:before {
  content: '\f281';
}
.zmdi-bluetooth:before {
  content: '\f282';
}
.zmdi-camera-add:before {
  content: '\f283';
}
.zmdi-camera-alt:before {
  content: '\f284';
}
.zmdi-camera-bw:before {
  content: '\f285';
}
.zmdi-camera-front:before {
  content: '\f286';
}
.zmdi-camera-mic:before {
  content: '\f287';
}
.zmdi-camera-party-mode:before {
  content: '\f288';
}
.zmdi-camera-rear:before {
  content: '\f289';
}
.zmdi-camera-roll:before {
  content: '\f28a';
}
.zmdi-camera-switch:before {
  content: '\f28b';
}
.zmdi-camera:before {
  content: '\f28c';
}
.zmdi-card-alert:before {
  content: '\f28d';
}
.zmdi-card-off:before {
  content: '\f28e';
}
.zmdi-card-sd:before {
  content: '\f28f';
}
.zmdi-card-sim:before {
  content: '\f290';
}
.zmdi-desktop-mac:before {
  content: '\f291';
}
.zmdi-desktop-windows:before {
  content: '\f292';
}
.zmdi-device-hub:before {
  content: '\f293';
}
.zmdi-devices-off:before {
  content: '\f294';
}
.zmdi-devices:before {
  content: '\f295';
}
.zmdi-dock:before {
  content: '\f296';
}
.zmdi-floppy:before {
  content: '\f297';
}
.zmdi-gamepad:before {
  content: '\f298';
}
.zmdi-gps-dot:before {
  content: '\f299';
}
.zmdi-gps-off:before {
  content: '\f29a';
}
.zmdi-gps:before {
  content: '\f29b';
}
.zmdi-headset-mic:before {
  content: '\f29c';
}
.zmdi-headset:before {
  content: '\f29d';
}
.zmdi-input-antenna:before {
  content: '\f29e';
}
.zmdi-input-composite:before {
  content: '\f29f';
}
.zmdi-input-hdmi:before {
  content: '\f2a0';
}
.zmdi-input-power:before {
  content: '\f2a1';
}
.zmdi-input-svideo:before {
  content: '\f2a2';
}
.zmdi-keyboard-hide:before {
  content: '\f2a3';
}
.zmdi-keyboard:before {
  content: '\f2a4';
}
.zmdi-laptop-chromebook:before {
  content: '\f2a5';
}
.zmdi-laptop-mac:before {
  content: '\f2a6';
}
.zmdi-laptop:before {
  content: '\f2a7';
}
.zmdi-mic-off:before {
  content: '\f2a8';
}
.zmdi-mic-outline:before {
  content: '\f2a9';
}
.zmdi-mic-setting:before {
  content: '\f2aa';
}
.zmdi-mic:before {
  content: '\f2ab';
}
.zmdi-mouse:before {
  content: '\f2ac';
}
.zmdi-network-alert:before {
  content: '\f2ad';
}
.zmdi-network-locked:before {
  content: '\f2ae';
}
.zmdi-network-off:before {
  content: '\f2af';
}
.zmdi-network-outline:before {
  content: '\f2b0';
}
.zmdi-network-setting:before {
  content: '\f2b1';
}
.zmdi-network:before {
  content: '\f2b2';
}
.zmdi-phone-bluetooth:before {
  content: '\f2b3';
}
.zmdi-phone-end:before {
  content: '\f2b4';
}
.zmdi-phone-forwarded:before {
  content: '\f2b5';
}
.zmdi-phone-in-talk:before {
  content: '\f2b6';
}
.zmdi-phone-locked:before {
  content: '\f2b7';
}
.zmdi-phone-missed:before {
  content: '\f2b8';
}
.zmdi-phone-msg:before {
  content: '\f2b9';
}
.zmdi-phone-paused:before {
  content: '\f2ba';
}
.zmdi-phone-ring:before {
  content: '\f2bb';
}
.zmdi-phone-setting:before {
  content: '\f2bc';
}
.zmdi-phone-sip:before {
  content: '\f2bd';
}
.zmdi-phone:before {
  content: '\f2be';
}
.zmdi-portable-wifi-changes:before {
  content: '\f2bf';
}
.zmdi-portable-wifi-off:before {
  content: '\f2c0';
}
.zmdi-portable-wifi:before {
  content: '\f2c1';
}
.zmdi-radio:before {
  content: '\f2c2';
}
.zmdi-reader:before {
  content: '\f2c3';
}
.zmdi-remote-control-alt:before {
  content: '\f2c4';
}
.zmdi-remote-control:before {
  content: '\f2c5';
}
.zmdi-router:before {
  content: '\f2c6';
}
.zmdi-scanner:before {
  content: '\f2c7';
}
.zmdi-smartphone-android:before {
  content: '\f2c8';
}
.zmdi-smartphone-download:before {
  content: '\f2c9';
}
.zmdi-smartphone-erase:before {
  content: '\f2ca';
}
.zmdi-smartphone-info:before {
  content: '\f2cb';
}
.zmdi-smartphone-iphone:before {
  content: '\f2cc';
}
.zmdi-smartphone-landscape-lock:before {
  content: '\f2cd';
}
.zmdi-smartphone-landscape:before {
  content: '\f2ce';
}
.zmdi-smartphone-lock:before {
  content: '\f2cf';
}
.zmdi-smartphone-portrait-lock:before {
  content: '\f2d0';
}
.zmdi-smartphone-ring:before {
  content: '\f2d1';
}
.zmdi-smartphone-setting:before {
  content: '\f2d2';
}
.zmdi-smartphone-setup:before {
  content: '\f2d3';
}
.zmdi-smartphone:before {
  content: '\f2d4';
}
.zmdi-speaker:before {
  content: '\f2d5';
}
.zmdi-tablet-android:before {
  content: '\f2d6';
}
.zmdi-tablet-mac:before {
  content: '\f2d7';
}
.zmdi-tablet:before {
  content: '\f2d8';
}
.zmdi-tv-alt-play:before {
  content: '\f2d9';
}
.zmdi-tv-list:before {
  content: '\f2da';
}
.zmdi-tv-play:before {
  content: '\f2db';
}
.zmdi-tv:before {
  content: '\f2dc';
}
.zmdi-usb:before {
  content: '\f2dd';
}
.zmdi-videocam-off:before {
  content: '\f2de';
}
.zmdi-videocam-switch:before {
  content: '\f2df';
}
.zmdi-videocam:before {
  content: '\f2e0';
}
.zmdi-watch:before {
  content: '\f2e1';
}
.zmdi-wifi-alt-2:before {
  content: '\f2e2';
}
.zmdi-wifi-alt:before {
  content: '\f2e3';
}
.zmdi-wifi-info:before {
  content: '\f2e4';
}
.zmdi-wifi-lock:before {
  content: '\f2e5';
}
.zmdi-wifi-off:before {
  content: '\f2e6';
}
.zmdi-wifi-outline:before {
  content: '\f2e7';
}
.zmdi-wifi:before {
  content: '\f2e8';
}
.zmdi-arrow-left-bottom:before {
  content: '\f2e9';
}
.zmdi-arrow-left:before {
  content: '\f2ea';
}
.zmdi-arrow-merge:before {
  content: '\f2eb';
}
.zmdi-arrow-missed:before {
  content: '\f2ec';
}
.zmdi-arrow-right-top:before {
  content: '\f2ed';
}
.zmdi-arrow-right:before {
  content: '\f2ee';
}
.zmdi-arrow-split:before {
  content: '\f2ef';
}
.zmdi-arrows:before {
  content: '\f2f0';
}
.zmdi-caret-down-circle:before {
  content: '\f2f1';
}
.zmdi-caret-down:before {
  content: '\f2f2';
}
.zmdi-caret-left-circle:before {
  content: '\f2f3';
}
.zmdi-caret-left:before {
  content: '\f2f4';
}
.zmdi-caret-right-circle:before {
  content: '\f2f5';
}
.zmdi-caret-right:before {
  content: '\f2f6';
}
.zmdi-caret-up-circle:before {
  content: '\f2f7';
}
.zmdi-caret-up:before {
  content: '\f2f8';
}
.zmdi-chevron-down:before {
  content: '\f2f9';
}
.zmdi-chevron-left:before {
  content: '\f2fa';
}
.zmdi-chevron-right:before {
  content: '\f2fb';
}
.zmdi-chevron-up:before {
  content: '\f2fc';
}
.zmdi-forward:before {
  content: '\f2fd';
}
.zmdi-long-arrow-down:before {
  content: '\f2fe';
}
.zmdi-long-arrow-left:before {
  content: '\f2ff';
}
.zmdi-long-arrow-return:before {
  content: '\f300';
}
.zmdi-long-arrow-right:before {
  content: '\f301';
}
.zmdi-long-arrow-tab:before {
  content: '\f302';
}
.zmdi-long-arrow-up:before {
  content: '\f303';
}
.zmdi-rotate-ccw:before {
  content: '\f304';
}
.zmdi-rotate-cw:before {
  content: '\f305';
}
.zmdi-rotate-left:before {
  content: '\f306';
}
.zmdi-rotate-right:before {
  content: '\f307';
}
.zmdi-square-down:before {
  content: '\f308';
}
.zmdi-square-right:before {
  content: '\f309';
}
.zmdi-swap-alt:before {
  content: '\f30a';
}
.zmdi-swap-vertical-circle:before {
  content: '\f30b';
}
.zmdi-swap-vertical:before {
  content: '\f30c';
}
.zmdi-swap:before {
  content: '\f30d';
}
.zmdi-trending-down:before {
  content: '\f30e';
}
.zmdi-trending-flat:before {
  content: '\f30f';
}
.zmdi-trending-up:before {
  content: '\f310';
}
.zmdi-unfold-less:before {
  content: '\f311';
}
.zmdi-unfold-more:before {
  content: '\f312';
}
.zmdi-apps:before {
  content: '\f313';
}
.zmdi-grid-off:before {
  content: '\f314';
}
.zmdi-grid:before {
  content: '\f315';
}
.zmdi-view-agenda:before {
  content: '\f316';
}
.zmdi-view-array:before {
  content: '\f317';
}
.zmdi-view-carousel:before {
  content: '\f318';
}
.zmdi-view-column:before {
  content: '\f319';
}
.zmdi-view-comfy:before {
  content: '\f31a';
}
.zmdi-view-compact:before {
  content: '\f31b';
}
.zmdi-view-dashboard:before {
  content: '\f31c';
}
.zmdi-view-day:before {
  content: '\f31d';
}
.zmdi-view-headline:before {
  content: '\f31e';
}
.zmdi-view-list-alt:before {
  content: '\f31f';
}
.zmdi-view-list:before {
  content: '\f320';
}
.zmdi-view-module:before {
  content: '\f321';
}
.zmdi-view-quilt:before {
  content: '\f322';
}
.zmdi-view-stream:before {
  content: '\f323';
}
.zmdi-view-subtitles:before {
  content: '\f324';
}
.zmdi-view-toc:before {
  content: '\f325';
}
.zmdi-view-web:before {
  content: '\f326';
}
.zmdi-view-week:before {
  content: '\f327';
}
.zmdi-widgets:before {
  content: '\f328';
}
.zmdi-alarm-check:before {
  content: '\f329';
}
.zmdi-alarm-off:before {
  content: '\f32a';
}
.zmdi-alarm-plus:before {
  content: '\f32b';
}
.zmdi-alarm-snooze:before {
  content: '\f32c';
}
.zmdi-alarm:before {
  content: '\f32d';
}
.zmdi-calendar-alt:before {
  content: '\f32e';
}
.zmdi-calendar-check:before {
  content: '\f32f';
}
.zmdi-calendar-close:before {
  content: '\f330';
}
.zmdi-calendar-note:before {
  content: '\f331';
}
.zmdi-calendar:before {
  content: '\f332';
}
.zmdi-time-countdown:before {
  content: '\f333';
}
.zmdi-time-interval:before {
  content: '\f334';
}
.zmdi-time-restore-setting:before {
  content: '\f335';
}
.zmdi-time-restore:before {
  content: '\f336';
}
.zmdi-time:before {
  content: '\f337';
}
.zmdi-timer-off:before {
  content: '\f338';
}
.zmdi-timer:before {
  content: '\f339';
}
.zmdi-android-alt:before {
  content: '\f33a';
}
.zmdi-android:before {
  content: '\f33b';
}
.zmdi-apple:before {
  content: '\f33c';
}
.zmdi-behance:before {
  content: '\f33d';
}
.zmdi-codepen:before {
  content: '\f33e';
}
.zmdi-dribbble:before {
  content: '\f33f';
}
.zmdi-dropbox:before {
  content: '\f340';
}
.zmdi-evernote:before {
  content: '\f341';
}
.zmdi-facebook-box:before {
  content: '\f342';
}
.zmdi-facebook:before {
  content: '\f343';
}
.zmdi-github-box:before {
  content: '\f344';
}
.zmdi-github:before {
  content: '\f345';
}
.zmdi-google-drive:before {
  content: '\f346';
}
.zmdi-google-earth:before {
  content: '\f347';
}
.zmdi-google-glass:before {
  content: '\f348';
}
.zmdi-google-maps:before {
  content: '\f349';
}
.zmdi-google-pages:before {
  content: '\f34a';
}
.zmdi-google-play:before {
  content: '\f34b';
}
.zmdi-google-plus-box:before {
  content: '\f34c';
}
.zmdi-google-plus:before {
  content: '\f34d';
}
.zmdi-google:before {
  content: '\f34e';
}
.zmdi-instagram:before {
  content: '\f34f';
}
.zmdi-language-css3:before {
  content: '\f350';
}
.zmdi-language-html5:before {
  content: '\f351';
}
.zmdi-language-javascript:before {
  content: '\f352';
}
.zmdi-language-python-alt:before {
  content: '\f353';
}
.zmdi-language-python:before {
  content: '\f354';
}
.zmdi-lastfm:before {
  content: '\f355';
}
.zmdi-linkedin-box:before {
  content: '\f356';
}
.zmdi-paypal:before {
  content: '\f357';
}
.zmdi-pinterest-box:before {
  content: '\f358';
}
.zmdi-pocket:before {
  content: '\f359';
}
.zmdi-polymer:before {
  content: '\f35a';
}
.zmdi-share:before {
  content: '\f35b';
}
.zmdi-stackoverflow:before {
  content: '\f35c';
}
.zmdi-steam-square:before {
  content: '\f35d';
}
.zmdi-steam:before {
  content: '\f35e';
}
.zmdi-twitter-box:before {
  content: '\f35f';
}
.zmdi-twitter:before {
  content: '\f360';
}
.zmdi-vk:before {
  content: '\f361';
}
.zmdi-wikipedia:before {
  content: '\f362';
}
.zmdi-windows:before {
  content: '\f363';
}
.zmdi-aspect-ratio-alt:before {
  content: '\f364';
}
.zmdi-aspect-ratio:before {
  content: '\f365';
}
.zmdi-blur-circular:before {
  content: '\f366';
}
.zmdi-blur-linear:before {
  content: '\f367';
}
.zmdi-blur-off:before {
  content: '\f368';
}
.zmdi-blur:before {
  content: '\f369';
}
.zmdi-brightness-2:before {
  content: '\f36a';
}
.zmdi-brightness-3:before {
  content: '\f36b';
}
.zmdi-brightness-4:before {
  content: '\f36c';
}
.zmdi-brightness-5:before {
  content: '\f36d';
}
.zmdi-brightness-6:before {
  content: '\f36e';
}
.zmdi-brightness-7:before {
  content: '\f36f';
}
.zmdi-brightness-auto:before {
  content: '\f370';
}
.zmdi-brightness-setting:before {
  content: '\f371';
}
.zmdi-broken-image:before {
  content: '\f372';
}
.zmdi-center-focus-strong:before {
  content: '\f373';
}
.zmdi-center-focus-weak:before {
  content: '\f374';
}
.zmdi-compare:before {
  content: '\f375';
}
.zmdi-crop-16-9:before {
  content: '\f376';
}
.zmdi-crop-3-2:before {
  content: '\f377';
}
.zmdi-crop-5-4:before {
  content: '\f378';
}
.zmdi-crop-7-5:before {
  content: '\f379';
}
.zmdi-crop-din:before {
  content: '\f37a';
}
.zmdi-crop-free:before {
  content: '\f37b';
}
.zmdi-crop-landscape:before {
  content: '\f37c';
}
.zmdi-crop-portrait:before {
  content: '\f37d';
}
.zmdi-crop-square:before {
  content: '\f37e';
}
.zmdi-exposure-alt:before {
  content: '\f37f';
}
.zmdi-exposure:before {
  content: '\f380';
}
.zmdi-filter-b-and-w:before {
  content: '\f381';
}
.zmdi-filter-center-focus:before {
  content: '\f382';
}
.zmdi-filter-frames:before {
  content: '\f383';
}
.zmdi-filter-tilt-shift:before {
  content: '\f384';
}
.zmdi-gradient:before {
  content: '\f385';
}
.zmdi-grain:before {
  content: '\f386';
}
.zmdi-graphic-eq:before {
  content: '\f387';
}
.zmdi-hdr-off:before {
  content: '\f388';
}
.zmdi-hdr-strong:before {
  content: '\f389';
}
.zmdi-hdr-weak:before {
  content: '\f38a';
}
.zmdi-hdr:before {
  content: '\f38b';
}
.zmdi-iridescent:before {
  content: '\f38c';
}
.zmdi-leak-off:before {
  content: '\f38d';
}
.zmdi-leak:before {
  content: '\f38e';
}
.zmdi-looks:before {
  content: '\f38f';
}
.zmdi-loupe:before {
  content: '\f390';
}
.zmdi-panorama-horizontal:before {
  content: '\f391';
}
.zmdi-panorama-vertical:before {
  content: '\f392';
}
.zmdi-panorama-wide-angle:before {
  content: '\f393';
}
.zmdi-photo-size-select-large:before {
  content: '\f394';
}
.zmdi-photo-size-select-small:before {
  content: '\f395';
}
.zmdi-picture-in-picture:before {
  content: '\f396';
}
.zmdi-slideshow:before {
  content: '\f397';
}
.zmdi-texture:before {
  content: '\f398';
}
.zmdi-tonality:before {
  content: '\f399';
}
.zmdi-vignette:before {
  content: '\f39a';
}
.zmdi-wb-auto:before {
  content: '\f39b';
}
.zmdi-eject-alt:before {
  content: '\f39c';
}
.zmdi-eject:before {
  content: '\f39d';
}
.zmdi-equalizer:before {
  content: '\f39e';
}
.zmdi-fast-forward:before {
  content: '\f39f';
}
.zmdi-fast-rewind:before {
  content: '\f3a0';
}
.zmdi-forward-10:before {
  content: '\f3a1';
}
.zmdi-forward-30:before {
  content: '\f3a2';
}
.zmdi-forward-5:before {
  content: '\f3a3';
}
.zmdi-hearing:before {
  content: '\f3a4';
}
.zmdi-pause-circle-outline:before {
  content: '\f3a5';
}
.zmdi-pause-circle:before {
  content: '\f3a6';
}
.zmdi-pause:before {
  content: '\f3a7';
}
.zmdi-play-circle-outline:before {
  content: '\f3a8';
}
.zmdi-play-circle:before {
  content: '\f3a9';
}
.zmdi-play:before {
  content: '\f3aa';
}
.zmdi-playlist-audio:before {
  content: '\f3ab';
}
.zmdi-playlist-plus:before {
  content: '\f3ac';
}
.zmdi-repeat-one:before {
  content: '\f3ad';
}
.zmdi-repeat:before {
  content: '\f3ae';
}
.zmdi-replay-10:before {
  content: '\f3af';
}
.zmdi-replay-30:before {
  content: '\f3b0';
}
.zmdi-replay-5:before {
  content: '\f3b1';
}
.zmdi-replay:before {
  content: '\f3b2';
}
.zmdi-shuffle:before {
  content: '\f3b3';
}
.zmdi-skip-next:before {
  content: '\f3b4';
}
.zmdi-skip-previous:before {
  content: '\f3b5';
}
.zmdi-stop:before {
  content: '\f3b6';
}
.zmdi-surround-sound:before {
  content: '\f3b7';
}
.zmdi-tune:before {
  content: '\f3b8';
}
.zmdi-volume-down:before {
  content: '\f3b9';
}
.zmdi-volume-mute:before {
  content: '\f3ba';
}
.zmdi-volume-off:before {
  content: '\f3bb';
}
.zmdi-volume-up:before {
  content: '\f3bc';
}
.zmdi-n-1-square:before {
  content: '\f3bd';
}
.zmdi-n-2-square:before {
  content: '\f3be';
}
.zmdi-n-3-square:before {
  content: '\f3bf';
}
.zmdi-n-4-square:before {
  content: '\f3c0';
}
.zmdi-n-5-square:before {
  content: '\f3c1';
}
.zmdi-n-6-square:before {
  content: '\f3c2';
}
.zmdi-neg-1:before {
  content: '\f3c3';
}
.zmdi-neg-2:before {
  content: '\f3c4';
}
.zmdi-plus-1:before {
  content: '\f3c5';
}
.zmdi-plus-2:before {
  content: '\f3c6';
}
.zmdi-sec-10:before {
  content: '\f3c7';
}
.zmdi-sec-3:before {
  content: '\f3c8';
}
.zmdi-zero:before {
  content: '\f3c9';
}
.zmdi-airline-seat-flat-angled:before {
  content: '\f3ca';
}
.zmdi-airline-seat-flat:before {
  content: '\f3cb';
}
.zmdi-airline-seat-individual-suite:before {
  content: '\f3cc';
}
.zmdi-airline-seat-legroom-extra:before {
  content: '\f3cd';
}
.zmdi-airline-seat-legroom-normal:before {
  content: '\f3ce';
}
.zmdi-airline-seat-legroom-reduced:before {
  content: '\f3cf';
}
.zmdi-airline-seat-recline-extra:before {
  content: '\f3d0';
}
.zmdi-airline-seat-recline-normal:before {
  content: '\f3d1';
}
.zmdi-airplay:before {
  content: '\f3d2';
}
.zmdi-closed-caption:before {
  content: '\f3d3';
}
.zmdi-confirmation-number:before {
  content: '\f3d4';
}
.zmdi-developer-board:before {
  content: '\f3d5';
}
.zmdi-disc-full:before {
  content: '\f3d6';
}
.zmdi-explicit:before {
  content: '\f3d7';
}
.zmdi-flight-land:before {
  content: '\f3d8';
}
.zmdi-flight-takeoff:before {
  content: '\f3d9';
}
.zmdi-flip-to-back:before {
  content: '\f3da';
}
.zmdi-flip-to-front:before {
  content: '\f3db';
}
.zmdi-group-work:before {
  content: '\f3dc';
}
.zmdi-hd:before {
  content: '\f3dd';
}
.zmdi-hq:before {
  content: '\f3de';
}
.zmdi-markunread-mailbox:before {
  content: '\f3df';
}
.zmdi-memory:before {
  content: '\f3e0';
}
.zmdi-nfc:before {
  content: '\f3e1';
}
.zmdi-play-for-work:before {
  content: '\f3e2';
}
.zmdi-power-input:before {
  content: '\f3e3';
}
.zmdi-present-to-all:before {
  content: '\f3e4';
}
.zmdi-satellite:before {
  content: '\f3e5';
}
.zmdi-tap-and-play:before {
  content: '\f3e6';
}
.zmdi-vibration:before {
  content: '\f3e7';
}
.zmdi-voicemail:before {
  content: '\f3e8';
}
.zmdi-group:before {
  content: '\f3e9';
}
.zmdi-rss:before {
  content: '\f3ea';
}
.zmdi-shape:before {
  content: '\f3eb';
}
.zmdi-spinner:before {
  content: '\f3ec';
}
.zmdi-ungroup:before {
  content: '\f3ed';
}
.zmdi-500px:before {
  content: '\f3ee';
}
.zmdi-8tracks:before {
  content: '\f3ef';
}
.zmdi-amazon:before {
  content: '\f3f0';
}
.zmdi-blogger:before {
  content: '\f3f1';
}
.zmdi-delicious:before {
  content: '\f3f2';
}
.zmdi-disqus:before {
  content: '\f3f3';
}
.zmdi-flattr:before {
  content: '\f3f4';
}
.zmdi-flickr:before {
  content: '\f3f5';
}
.zmdi-github-alt:before {
  content: '\f3f6';
}
.zmdi-google-old:before {
  content: '\f3f7';
}
.zmdi-linkedin:before {
  content: '\f3f8';
}
.zmdi-odnoklassniki:before {
  content: '\f3f9';
}
.zmdi-outlook:before {
  content: '\f3fa';
}
.zmdi-paypal-alt:before {
  content: '\f3fb';
}
.zmdi-pinterest:before {
  content: '\f3fc';
}
.zmdi-playstation:before {
  content: '\f3fd';
}
.zmdi-reddit:before {
  content: '\f3fe';
}
.zmdi-skype:before {
  content: '\f3ff';
}
.zmdi-slideshare:before {
  content: '\f400';
}
.zmdi-soundcloud:before {
  content: '\f401';
}
.zmdi-tumblr:before {
  content: '\f402';
}
.zmdi-twitch:before {
  content: '\f403';
}
.zmdi-vimeo:before {
  content: '\f404';
}
.zmdi-whatsapp:before {
  content: '\f405';
}
.zmdi-xbox:before {
  content: '\f406';
}
.zmdi-yahoo:before {
  content: '\f407';
}
.zmdi-youtube-play:before {
  content: '\f408';
}
.zmdi-youtube:before {
  content: '\f409';
}
.zmdi-import-export:before {
  content: '\f30c';
}
.zmdi-swap-vertical-:before {
  content: '\f30c';
}
.zmdi-airplanemode-inactive:before {
  content: '\f102';
}
.zmdi-airplanemode-active:before {
  content: '\f103';
}
.zmdi-rate-review:before {
  content: '\f103';
}
.zmdi-comment-sign:before {
  content: '\f25a';
}
.zmdi-network-warning:before {
  content: '\f2ad';
}
.zmdi-shopping-cart-add:before {
  content: '\f1ca';
}
.zmdi-file-add:before {
  content: '\f221';
}
.zmdi-network-wifi-scan:before {
  content: '\f2e4';
}
.zmdi-collection-add:before {
  content: '\f14e';
}
.zmdi-format-playlist-add:before {
  content: '\f3ac';
}
.zmdi-format-queue-music:before {
  content: '\f3ab';
}
.zmdi-plus-box:before {
  content: '\f277';
}
.zmdi-tag-backspace:before {
  content: '\f1d9';
}
.zmdi-alarm-add:before {
  content: '\f32b';
}
.zmdi-battery-charging:before {
  content: '\f114';
}
.zmdi-daydream-setting:before {
  content: '\f217';
}
.zmdi-more-horiz:before {
  content: '\f19c';
}
.zmdi-book-photo:before {
  content: '\f11b';
}
.zmdi-incandescent:before {
  content: '\f189';
}
.zmdi-wb-iridescent:before {
  content: '\f38c';
}
.zmdi-calendar-remove:before {
  content: '\f330';
}
.zmdi-refresh-sync-disabled:before {
  content: '\f1b7';
}
.zmdi-refresh-sync-problem:before {
  content: '\f1b6';
}
.zmdi-crop-original:before {
  content: '\f17e';
}
.zmdi-power-off:before {
  content: '\f1af';
}
.zmdi-power-off-setting:before {
  content: '\f1ae';
}
.zmdi-leak-remove:before {
  content: '\f38d';
}
.zmdi-star-border:before {
  content: '\f27c';
}
.zmdi-brightness-low:before {
  content: '\f36d';
}
.zmdi-brightness-medium:before {
  content: '\f36e';
}
.zmdi-brightness-high:before {
  content: '\f36f';
}
.zmdi-smartphone-portrait:before {
  content: '\f2d4';
}
.zmdi-live-tv:before {
  content: '\f2d9';
}
.zmdi-format-textdirection-l-to-r:before {
  content: '\f249';
}
.zmdi-format-textdirection-r-to-l:before {
  content: '\f24a';
}
.zmdi-arrow-back:before {
  content: '\f2ea';
}
.zmdi-arrow-forward:before {
  content: '\f2ee';
}
.zmdi-arrow-in:before {
  content: '\f2e9';
}
.zmdi-arrow-out:before {
  content: '\f2ed';
}
.zmdi-rotate-90-degrees-ccw:before {
  content: '\f304';
}
.zmdi-adb:before {
  content: '\f33a';
}
.zmdi-network-wifi:before {
  content: '\f2e8';
}
.zmdi-network-wifi-alt:before {
  content: '\f2e3';
}
.zmdi-network-wifi-lock:before {
  content: '\f2e5';
}
.zmdi-network-wifi-off:before {
  content: '\f2e6';
}
.zmdi-network-wifi-outline:before {
  content: '\f2e7';
}
.zmdi-network-wifi-info:before {
  content: '\f2e4';
}
.zmdi-layers-clear:before {
  content: '\f18b';
}
.zmdi-colorize:before {
  content: '\f15d';
}
.zmdi-format-paint:before {
  content: '\f1ba';
}
.zmdi-format-quote:before {
  content: '\f1b2';
}
.zmdi-camera-monochrome-photos:before {
  content: '\f285';
}
.zmdi-sort-by-alpha:before {
  content: '\f1cf';
}
.zmdi-folder-shared:before {
  content: '\f225';
}
.zmdi-folder-special:before {
  content: '\f226';
}
.zmdi-comment-dots:before {
  content: '\f260';
}
.zmdi-reorder:before {
  content: '\f31e';
}
.zmdi-dehaze:before {
  content: '\f197';
}
.zmdi-sort:before {
  content: '\f1ce';
}
.zmdi-pages:before {
  content: '\f34a';
}
.zmdi-stack-overflow:before {
  content: '\f35c';
}
.zmdi-calendar-account:before {
  content: '\f204';
}
.zmdi-paste:before {
  content: '\f109';
}
.zmdi-cut:before {
  content: '\f1bc';
}
.zmdi-save:before {
  content: '\f297';
}
.zmdi-smartphone-code:before {
  content: '\f139';
}
.zmdi-directions-bike:before {
  content: '\f117';
}
.zmdi-directions-boat:before {
  content: '\f11a';
}
.zmdi-directions-bus:before {
  content: '\f121';
}
.zmdi-directions-car:before {
  content: '\f125';
}
.zmdi-directions-railway:before {
  content: '\f1b3';
}
.zmdi-directions-run:before {
  content: '\f215';
}
.zmdi-directions-subway:before {
  content: '\f1d5';
}
.zmdi-directions-walk:before {
  content: '\f216';
}
.zmdi-local-hotel:before {
  content: '\f178';
}
.zmdi-local-activity:before {
  content: '\f1df';
}
.zmdi-local-play:before {
  content: '\f1df';
}
.zmdi-local-airport:before {
  content: '\f103';
}
.zmdi-local-atm:before {
  content: '\f198';
}
.zmdi-local-bar:before {
  content: '\f137';
}
.zmdi-local-cafe:before {
  content: '\f13b';
}
.zmdi-local-car-wash:before {
  content: '\f124';
}
.zmdi-local-convenience-store:before {
  content: '\f1d3';
}
.zmdi-local-dining:before {
  content: '\f153';
}
.zmdi-local-drink:before {
  content: '\f157';
}
.zmdi-local-florist:before {
  content: '\f168';
}
.zmdi-local-gas-station:before {
  content: '\f16f';
}
.zmdi-local-grocery-store:before {
  content: '\f1cb';
}
.zmdi-local-hospital:before {
  content: '\f177';
}
.zmdi-local-laundry-service:before {
  content: '\f1e9';
}
.zmdi-local-library:before {
  content: '\f18d';
}
.zmdi-local-mall:before {
  content: '\f195';
}
.zmdi-local-movies:before {
  content: '\f19d';
}
.zmdi-local-offer:before {
  content: '\f187';
}
.zmdi-local-parking:before {
  content: '\f1a5';
}
.zmdi-local-parking:before {
  content: '\f1a5';
}
.zmdi-local-pharmacy:before {
  content: '\f176';
}
.zmdi-local-phone:before {
  content: '\f2be';
}
.zmdi-local-pizza:before {
  content: '\f1ac';
}
.zmdi-local-post-office:before {
  content: '\f15a';
}
.zmdi-local-printshop:before {
  content: '\f1b0';
}
.zmdi-local-see:before {
  content: '\f28c';
}
.zmdi-local-shipping:before {
  content: '\f1e6';
}
.zmdi-local-store:before {
  content: '\f1d4';
}
.zmdi-local-taxi:before {
  content: '\f123';
}
.zmdi-local-wc:before {
  content: '\f211';
}
.zmdi-my-location:before {
  content: '\f299';
}
.zmdi-directions:before {
  content: '\f1e7';
}


/* Font Awesome */


/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
/* FONT PATH
 * -------------------------- */
@font-face {
  font-family: 'FontAwesome';
  src: url('../fonts/fontawesome-webfont.eot?v=4.7.0');
  src: url('../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'), url('../fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'), url('../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* makes the font 33% larger relative to the icon container */
.fa-lg {
  font-size: 1.33333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}
.fa-2x {
  font-size: 2em;
}
.fa-3x {
  font-size: 3em;
}
.fa-4x {
  font-size: 4em;
}
.fa-5x {
  font-size: 5em;
}
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}
.fa-ul > li {
  position: relative;
}
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: 0.14285714em;
  text-align: center;
}
.fa-li.fa-lg {
  left: -1.85714286em;
}
.fa-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eeeeee;
  border-radius: .1em;
}
.fa-pull-left {
  float: left;
}
.fa-pull-right {
  float: right;
}
.fa.fa-pull-left {
  margin-right: .3em;
}
.fa.fa-pull-right {
  margin-left: .3em;
}
/* Deprecated as of 4.4.0 */
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.fa.pull-left {
  margin-right: .3em;
}
.fa.pull-right {
  margin-left: .3em;
}
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}
@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}
:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  filter: none;
}
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}
.fa-stack-1x,
.fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}
.fa-stack-1x {
  line-height: inherit;
}
.fa-stack-2x {
  font-size: 2em;
}
.fa-inverse {
  color: #ffffff;
}
/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
  content: "\f000";
}
.fa-music:before {
  content: "\f001";
}
.fa-search:before {
  content: "\f002";
}
.fa-envelope-o:before {
  content: "\f003";
}
.fa-heart:before {
  content: "\f004";
}
.fa-star:before {
  content: "\f005";
}
.fa-star-o:before {
  content: "\f006";
}
.fa-user:before {
  content: "\f007";
}
.fa-film:before {
  content: "\f008";
}
.fa-th-large:before {
  content: "\f009";
}
.fa-th:before {
  content: "\f00a";
}
.fa-th-list:before {
  content: "\f00b";
}
.fa-check:before {
  content: "\f00c";
}
.fa-remove:before,
.fa-close:before,
.fa-times:before {
  content: "\f00d";
}
.fa-search-plus:before {
  content: "\f00e";
}
.fa-search-minus:before {
  content: "\f010";
}
.fa-power-off:before {
  content: "\f011";
}
.fa-signal:before {
  content: "\f012";
}
.fa-gear:before,
.fa-cog:before {
  content: "\f013";
}
.fa-trash-o:before {
  content: "\f014";
}
.fa-home:before {
  content: "\f015";
}
.fa-file-o:before {
  content: "\f016";
}
.fa-clock-o:before {
  content: "\f017";
}
.fa-road:before {
  content: "\f018";
}
.fa-download:before {
  content: "\f019";
}
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}
.fa-inbox:before {
  content: "\f01c";
}
.fa-play-circle-o:before {
  content: "\f01d";
}
.fa-rotate-right:before,
.fa-repeat:before {
  content: "\f01e";
}
.fa-refresh:before {
  content: "\f021";
}
.fa-list-alt:before {
  content: "\f022";
}
.fa-lock:before {
  content: "\f023";
}
.fa-flag:before {
  content: "\f024";
}
.fa-headphones:before {
  content: "\f025";
}
.fa-volume-off:before {
  content: "\f026";
}
.fa-volume-down:before {
  content: "\f027";
}
.fa-volume-up:before {
  content: "\f028";
}
.fa-qrcode:before {
  content: "\f029";
}
.fa-barcode:before {
  content: "\f02a";
}
.fa-tag:before {
  content: "\f02b";
}
.fa-tags:before {
  content: "\f02c";
}
.fa-book:before {
  content: "\f02d";
}
.fa-bookmark:before {
  content: "\f02e";
}
.fa-print:before {
  content: "\f02f";
}
.fa-camera:before {
  content: "\f030";
}
.fa-font:before {
  content: "\f031";
}
.fa-bold:before {
  content: "\f032";
}
.fa-italic:before {
  content: "\f033";
}
.fa-text-height:before {
  content: "\f034";
}
.fa-text-width:before {
  content: "\f035";
}
.fa-align-left:before {
  content: "\f036";
}
.fa-align-center:before {
  content: "\f037";
}
.fa-align-right:before {
  content: "\f038";
}
.fa-align-justify:before {
  content: "\f039";
}
.fa-list:before {
  content: "\f03a";
}
.fa-dedent:before,
.fa-outdent:before {
  content: "\f03b";
}
.fa-indent:before {
  content: "\f03c";
}
.fa-video-camera:before {
  content: "\f03d";
}
.fa-photo:before,
.fa-image:before,
.fa-picture-o:before {
  content: "\f03e";
}
.fa-pencil:before {
  content: "\f040";
}
.fa-map-marker:before {
  content: "\f041";
}
.fa-adjust:before {
  content: "\f042";
}
.fa-tint:before {
  content: "\f043";
}
.fa-edit:before,
.fa-pencil-square-o:before {
  content: "\f044";
}
.fa-share-square-o:before {
  content: "\f045";
}
.fa-check-square-o:before {
  content: "\f046";
}
.fa-arrows:before {
  content: "\f047";
}
.fa-step-backward:before {
  content: "\f048";
}
.fa-fast-backward:before {
  content: "\f049";
}
.fa-backward:before {
  content: "\f04a";
}
.fa-play:before {
  content: "\f04b";
}
.fa-pause:before {
  content: "\f04c";
}
.fa-stop:before {
  content: "\f04d";
}
.fa-forward:before {
  content: "\f04e";
}
.fa-fast-forward:before {
  content: "\f050";
}
.fa-step-forward:before {
  content: "\f051";
}
.fa-eject:before {
  content: "\f052";
}
.fa-chevron-left:before {
  content: "\f053";
}
.fa-chevron-right:before {
  content: "\f054";
}
.fa-plus-circle:before {
  content: "\f055";
}
.fa-minus-circle:before {
  content: "\f056";
}
.fa-times-circle:before {
  content: "\f057";
}
.fa-check-circle:before {
  content: "\f058";
}
.fa-question-circle:before {
  content: "\f059";
}
.fa-info-circle:before {
  content: "\f05a";
}
.fa-crosshairs:before {
  content: "\f05b";
}
.fa-times-circle-o:before {
  content: "\f05c";
}
.fa-check-circle-o:before {
  content: "\f05d";
}
.fa-ban:before {
  content: "\f05e";
}
.fa-arrow-left:before {
  content: "\f060";
}
.fa-arrow-right:before {
  content: "\f061";
}
.fa-arrow-up:before {
  content: "\f062";
}
.fa-arrow-down:before {
  content: "\f063";
}
.fa-mail-forward:before,
.fa-share:before {
  content: "\f064";
}
.fa-expand:before {
  content: "\f065";
}
.fa-compress:before {
  content: "\f066";
}
.fa-plus:before {
  content: "\f067";
}
.fa-minus:before {
  content: "\f068";
}
.fa-asterisk:before {
  content: "\f069";
}
.fa-exclamation-circle:before {
  content: "\f06a";
}
.fa-gift:before {
  content: "\f06b";
}
.fa-leaf:before {
  content: "\f06c";
}
.fa-fire:before {
  content: "\f06d";
}
.fa-eye:before {
  content: "\f06e";
}
.fa-eye-slash:before {
  content: "\f070";
}
.fa-warning:before,
.fa-exclamation-triangle:before {
  content: "\f071";
}
.fa-plane:before {
  content: "\f072";
}
.fa-calendar:before {
  content: "\f073";
}
.fa-random:before {
  content: "\f074";
}
.fa-comment:before {
  content: "\f075";
}
.fa-magnet:before {
  content: "\f076";
}
.fa-chevron-up:before {
  content: "\f077";
}
.fa-chevron-down:before {
  content: "\f078";
}
.fa-retweet:before {
  content: "\f079";
}
.fa-shopping-cart:before {
  content: "\f07a";
}
.fa-folder:before {
  content: "\f07b";
}
.fa-folder-open:before {
  content: "\f07c";
}
.fa-arrows-v:before {
  content: "\f07d";
}
.fa-arrows-h:before {
  content: "\f07e";
}
.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: "\f080";
}
.fa-twitter-square:before {
  content: "\f081";
}
.fa-facebook-square:before {
  content: "\f082";
}
.fa-camera-retro:before {
  content: "\f083";
}
.fa-key:before {
  content: "\f084";
}
.fa-gears:before,
.fa-cogs:before {
  content: "\f085";
}
.fa-comments:before {
  content: "\f086";
}
.fa-thumbs-o-up:before {
  content: "\f087";
}
.fa-thumbs-o-down:before {
  content: "\f088";
}
.fa-star-half:before {
  content: "\f089";
}
.fa-heart-o:before {
  content: "\f08a";
}
.fa-sign-out:before {
  content: "\f08b";
}
.fa-linkedin-square:before {
  content: "\f08c";
}
.fa-thumb-tack:before {
  content: "\f08d";
}
.fa-external-link:before {
  content: "\f08e";
}
.fa-sign-in:before {
  content: "\f090";
}
.fa-trophy:before {
  content: "\f091";
}
.fa-github-square:before {
  content: "\f092";
}
.fa-upload:before {
  content: "\f093";
}
.fa-lemon-o:before {
  content: "\f094";
}
.fa-phone:before {
  content: "\f095";
}
.fa-square-o:before {
  content: "\f096";
}
.fa-bookmark-o:before {
  content: "\f097";
}
.fa-phone-square:before {
  content: "\f098";
}
.fa-twitter:before {
  content: "\f099";
}
.fa-facebook-f:before,
.fa-facebook:before {
  content: "\f09a";
}
.fa-github:before {
  content: "\f09b";
}
.fa-unlock:before {
  content: "\f09c";
}
.fa-credit-card:before {
  content: "\f09d";
}
.fa-feed:before,
.fa-rss:before {
  content: "\f09e";
}
.fa-hdd-o:before {
  content: "\f0a0";
}
.fa-bullhorn:before {
  content: "\f0a1";
}
.fa-bell:before {
  content: "\f0f3";
}
.fa-certificate:before {
  content: "\f0a3";
}
.fa-hand-o-right:before {
  content: "\f0a4";
}
.fa-hand-o-left:before {
  content: "\f0a5";
}
.fa-hand-o-up:before {
  content: "\f0a6";
}
.fa-hand-o-down:before {
  content: "\f0a7";
}
.fa-arrow-circle-left:before {
  content: "\f0a8";
}
.fa-arrow-circle-right:before {
  content: "\f0a9";
}
.fa-arrow-circle-up:before {
  content: "\f0aa";
}
.fa-arrow-circle-down:before {
  content: "\f0ab";
}
.fa-globe:before {
  content: "\f0ac";
}
.fa-wrench:before {
  content: "\f0ad";
}
.fa-tasks:before {
  content: "\f0ae";
}
.fa-filter:before {
  content: "\f0b0";
}
.fa-briefcase:before {
  content: "\f0b1";
}
.fa-arrows-alt:before {
  content: "\f0b2";
}
.fa-group:before,
.fa-users:before {
  content: "\f0c0";
}
.fa-chain:before,
.fa-link:before {
  content: "\f0c1";
}
.fa-cloud:before {
  content: "\f0c2";
}
.fa-flask:before {
  content: "\f0c3";
}
.fa-cut:before,
.fa-scissors:before {
  content: "\f0c4";
}
.fa-copy:before,
.fa-files-o:before {
  content: "\f0c5";
}
.fa-paperclip:before {
  content: "\f0c6";
}
.fa-save:before,
.fa-floppy-o:before {
  content: "\f0c7";
}
.fa-square:before {
  content: "\f0c8";
}
.fa-navicon:before,
.fa-reorder:before,
.fa-bars:before {
  content: "\f0c9";
}
.fa-list-ul:before {
  content: "\f0ca";
}
.fa-list-ol:before {
  content: "\f0cb";
}
.fa-strikethrough:before {
  content: "\f0cc";
}
.fa-underline:before {
  content: "\f0cd";
}
.fa-table:before {
  content: "\f0ce";
}
.fa-magic:before {
  content: "\f0d0";
}
.fa-truck:before {
  content: "\f0d1";
}
.fa-pinterest:before {
  content: "\f0d2";
}
.fa-pinterest-square:before {
  content: "\f0d3";
}
.fa-google-plus-square:before {
  content: "\f0d4";
}
.fa-google-plus:before {
  content: "\f0d5";
}
.fa-money:before {
  content: "\f0d6";
}
.fa-caret-down:before {
  content: "\f0d7";
}
.fa-caret-up:before {
  content: "\f0d8";
}
.fa-caret-left:before {
  content: "\f0d9";
}
.fa-caret-right:before {
  content: "\f0da";
}
.fa-columns:before {
  content: "\f0db";
}
.fa-unsorted:before,
.fa-sort:before {
  content: "\f0dc";
}
.fa-sort-down:before,
.fa-sort-desc:before {
  content: "\f0dd";
}
.fa-sort-up:before,
.fa-sort-asc:before {
  content: "\f0de";
}
.fa-envelope:before {
  content: "\f0e0";
}
.fa-linkedin:before {
  content: "\f0e1";
}
.fa-rotate-left:before,
.fa-undo:before {
  content: "\f0e2";
}
.fa-legal:before,
.fa-gavel:before {
  content: "\f0e3";
}
.fa-dashboard:before,
.fa-tachometer:before {
  content: "\f0e4";
}
.fa-comment-o:before {
  content: "\f0e5";
}
.fa-comments-o:before {
  content: "\f0e6";
}
.fa-flash:before,
.fa-bolt:before {
  content: "\f0e7";
}
.fa-sitemap:before {
  content: "\f0e8";
}
.fa-umbrella:before {
  content: "\f0e9";
}
.fa-paste:before,
.fa-clipboard:before {
  content: "\f0ea";
}
.fa-lightbulb-o:before {
  content: "\f0eb";
}
.fa-exchange:before {
  content: "\f0ec";
}
.fa-cloud-download:before {
  content: "\f0ed";
}
.fa-cloud-upload:before {
  content: "\f0ee";
}
.fa-user-md:before {
  content: "\f0f0";
}
.fa-stethoscope:before {
  content: "\f0f1";
}
.fa-suitcase:before {
  content: "\f0f2";
}
.fa-bell-o:before {
  content: "\f0a2";
}
.fa-coffee:before {
  content: "\f0f4";
}
.fa-cutlery:before {
  content: "\f0f5";
}
.fa-file-text-o:before {
  content: "\f0f6";
}
.fa-building-o:before {
  content: "\f0f7";
}
.fa-hospital-o:before {
  content: "\f0f8";
}
.fa-ambulance:before {
  content: "\f0f9";
}
.fa-medkit:before {
  content: "\f0fa";
}
.fa-fighter-jet:before {
  content: "\f0fb";
}
.fa-beer:before {
  content: "\f0fc";
}
.fa-h-square:before {
  content: "\f0fd";
}
.fa-plus-square:before {
  content: "\f0fe";
}
.fa-angle-double-left:before {
  content: "\f100";
}
.fa-angle-double-right:before {
  content: "\f101";
}
.fa-angle-double-up:before {
  content: "\f102";
}
.fa-angle-double-down:before {
  content: "\f103";
}
.fa-angle-left:before {
  content: "\f104";
}
.fa-angle-right:before {
  content: "\f105";
}
.fa-angle-up:before {
  content: "\f106";
}
.fa-angle-down:before {
  content: "\f107";
}
.fa-desktop:before {
  content: "\f108";
}
.fa-laptop:before {
  content: "\f109";
}
.fa-tablet:before {
  content: "\f10a";
}
.fa-mobile-phone:before,
.fa-mobile:before {
  content: "\f10b";
}
.fa-circle-o:before {
  content: "\f10c";
}
.fa-quote-left:before {
  content: "\f10d";
}
.fa-quote-right:before {
  content: "\f10e";
}
.fa-spinner:before {
  content: "\f110";
}
.fa-circle:before {
  content: "\f111";
}
.fa-mail-reply:before,
.fa-reply:before {
  content: "\f112";
}
.fa-github-alt:before {
  content: "\f113";
}
.fa-folder-o:before {
  content: "\f114";
}
.fa-folder-open-o:before {
  content: "\f115";
}
.fa-smile-o:before {
  content: "\f118";
}
.fa-frown-o:before {
  content: "\f119";
}
.fa-meh-o:before {
  content: "\f11a";
}
.fa-gamepad:before {
  content: "\f11b";
}
.fa-keyboard-o:before {
  content: "\f11c";
}
.fa-flag-o:before {
  content: "\f11d";
}
.fa-flag-checkered:before {
  content: "\f11e";
}
.fa-terminal:before {
  content: "\f120";
}
.fa-code:before {
  content: "\f121";
}
.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: "\f122";
}
.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: "\f123";
}
.fa-location-arrow:before {
  content: "\f124";
}
.fa-crop:before {
  content: "\f125";
}
.fa-code-fork:before {
  content: "\f126";
}
.fa-unlink:before,
.fa-chain-broken:before {
  content: "\f127";
}
.fa-question:before {
  content: "\f128";
}
.fa-info:before {
  content: "\f129";
}
.fa-exclamation:before {
  content: "\f12a";
}
.fa-superscript:before {
  content: "\f12b";
}
.fa-subscript:before {
  content: "\f12c";
}
.fa-eraser:before {
  content: "\f12d";
}
.fa-puzzle-piece:before {
  content: "\f12e";
}
.fa-microphone:before {
  content: "\f130";
}
.fa-microphone-slash:before {
  content: "\f131";
}
.fa-shield:before {
  content: "\f132";
}
.fa-calendar-o:before {
  content: "\f133";
}
.fa-fire-extinguisher:before {
  content: "\f134";
}
.fa-rocket:before {
  content: "\f135";
}
.fa-maxcdn:before {
  content: "\f136";
}
.fa-chevron-circle-left:before {
  content: "\f137";
}
.fa-chevron-circle-right:before {
  content: "\f138";
}
.fa-chevron-circle-up:before {
  content: "\f139";
}
.fa-chevron-circle-down:before {
  content: "\f13a";
}
.fa-html5:before {
  content: "\f13b";
}
.fa-css3:before {
  content: "\f13c";
}
.fa-anchor:before {
  content: "\f13d";
}
.fa-unlock-alt:before {
  content: "\f13e";
}
.fa-bullseye:before {
  content: "\f140";
}
.fa-ellipsis-h:before {
  content: "\f141";
}
.fa-ellipsis-v:before {
  content: "\f142";
}
.fa-rss-square:before {
  content: "\f143";
}
.fa-play-circle:before {
  content: "\f144";
}
.fa-ticket:before {
  content: "\f145";
}
.fa-minus-square:before {
  content: "\f146";
}
.fa-minus-square-o:before {
  content: "\f147";
}
.fa-level-up:before {
  content: "\f148";
}
.fa-level-down:before {
  content: "\f149";
}
.fa-check-square:before {
  content: "\f14a";
}
.fa-pencil-square:before {
  content: "\f14b";
}
.fa-external-link-square:before {
  content: "\f14c";
}
.fa-share-square:before {
  content: "\f14d";
}
.fa-compass:before {
  content: "\f14e";
}
.fa-toggle-down:before,
.fa-caret-square-o-down:before {
  content: "\f150";
}
.fa-toggle-up:before,
.fa-caret-square-o-up:before {
  content: "\f151";
}
.fa-toggle-right:before,
.fa-caret-square-o-right:before {
  content: "\f152";
}
.fa-euro:before,
.fa-eur:before {
  content: "\f153";
}
.fa-gbp:before {
  content: "\f154";
}
.fa-dollar:before,
.fa-usd:before {
  content: "\f155";
}
.fa-rupee:before,
.fa-inr:before {
  content: "\f156";
}
.fa-cny:before,
.fa-rmb:before,
.fa-yen:before,
.fa-jpy:before {
  content: "\f157";
}
.fa-ruble:before,
.fa-rouble:before,
.fa-rub:before {
  content: "\f158";
}
.fa-won:before,
.fa-krw:before {
  content: "\f159";
}
.fa-bitcoin:before,
.fa-btc:before {
  content: "\f15a";
}
.fa-file:before {
  content: "\f15b";
}
.fa-file-text:before {
  content: "\f15c";
}
.fa-sort-alpha-asc:before {
  content: "\f15d";
}
.fa-sort-alpha-desc:before {
  content: "\f15e";
}
.fa-sort-amount-asc:before {
  content: "\f160";
}
.fa-sort-amount-desc:before {
  content: "\f161";
}
.fa-sort-numeric-asc:before {
  content: "\f162";
}
.fa-sort-numeric-desc:before {
  content: "\f163";
}
.fa-thumbs-up:before {
  content: "\f164";
}
.fa-thumbs-down:before {
  content: "\f165";
}
.fa-youtube-square:before {
  content: "\f166";
}
.fa-youtube:before {
  content: "\f167";
}
.fa-xing:before {
  content: "\f168";
}
.fa-xing-square:before {
  content: "\f169";
}
.fa-youtube-play:before {
  content: "\f16a";
}
.fa-dropbox:before {
  content: "\f16b";
}
.fa-stack-overflow:before {
  content: "\f16c";
}
.fa-instagram:before {
  content: "\f16d";
}
.fa-flickr:before {
  content: "\f16e";
}
.fa-adn:before {
  content: "\f170";
}
.fa-bitbucket:before {
  content: "\f171";
}
.fa-bitbucket-square:before {
  content: "\f172";
}
.fa-tumblr:before {
  content: "\f173";
}
.fa-tumblr-square:before {
  content: "\f174";
}
.fa-long-arrow-down:before {
  content: "\f175";
}
.fa-long-arrow-up:before {
  content: "\f176";
}
.fa-long-arrow-left:before {
  content: "\f177";
}
.fa-long-arrow-right:before {
  content: "\f178";
}
.fa-apple:before {
  content: "\f179";
}
.fa-windows:before {
  content: "\f17a";
}
.fa-android:before {
  content: "\f17b";
}
.fa-linux:before {
  content: "\f17c";
}
.fa-dribbble:before {
  content: "\f17d";
}
.fa-skype:before {
  content: "\f17e";
}
.fa-foursquare:before {
  content: "\f180";
}
.fa-trello:before {
  content: "\f181";
}
.fa-female:before {
  content: "\f182";
}
.fa-male:before {
  content: "\f183";
}
.fa-gittip:before,
.fa-gratipay:before {
  content: "\f184";
}
.fa-sun-o:before {
  content: "\f185";
}
.fa-moon-o:before {
  content: "\f186";
}
.fa-archive:before {
  content: "\f187";
}
.fa-bug:before {
  content: "\f188";
}
.fa-vk:before {
  content: "\f189";
}
.fa-weibo:before {
  content: "\f18a";
}
.fa-renren:before {
  content: "\f18b";
}
.fa-pagelines:before {
  content: "\f18c";
}
.fa-stack-exchange:before {
  content: "\f18d";
}
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}
.fa-arrow-circle-o-left:before {
  content: "\f190";
}
.fa-toggle-left:before,
.fa-caret-square-o-left:before {
  content: "\f191";
}
.fa-dot-circle-o:before {
  content: "\f192";
}
.fa-wheelchair:before {
  content: "\f193";
}
.fa-vimeo-square:before {
  content: "\f194";
}
.fa-turkish-lira:before,
.fa-try:before {
  content: "\f195";
}
.fa-plus-square-o:before {
  content: "\f196";
}
.fa-space-shuttle:before {
  content: "\f197";
}
.fa-slack:before {
  content: "\f198";
}
.fa-envelope-square:before {
  content: "\f199";
}
.fa-wordpress:before {
  content: "\f19a";
}
.fa-openid:before {
  content: "\f19b";
}
.fa-institution:before,
.fa-bank:before,
.fa-university:before {
  content: "\f19c";
}
.fa-mortar-board:before,
.fa-graduation-cap:before {
  content: "\f19d";
}
.fa-yahoo:before {
  content: "\f19e";
}
.fa-google:before {
  content: "\f1a0";
}
.fa-reddit:before {
  content: "\f1a1";
}
.fa-reddit-square:before {
  content: "\f1a2";
}
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}
.fa-stumbleupon:before {
  content: "\f1a4";
}
.fa-delicious:before {
  content: "\f1a5";
}
.fa-digg:before {
  content: "\f1a6";
}
.fa-pied-piper-pp:before {
  content: "\f1a7";
}
.fa-pied-piper-alt:before {
  content: "\f1a8";
}
.fa-drupal:before {
  content: "\f1a9";
}
.fa-joomla:before {
  content: "\f1aa";
}
.fa-language:before {
  content: "\f1ab";
}
.fa-fax:before {
  content: "\f1ac";
}
.fa-building:before {
  content: "\f1ad";
}
.fa-child:before {
  content: "\f1ae";
}
.fa-paw:before {
  content: "\f1b0";
}
.fa-spoon:before {
  content: "\f1b1";
}
.fa-cube:before {
  content: "\f1b2";
}
.fa-cubes:before {
  content: "\f1b3";
}
.fa-behance:before {
  content: "\f1b4";
}
.fa-behance-square:before {
  content: "\f1b5";
}
.fa-steam:before {
  content: "\f1b6";
}
.fa-steam-square:before {
  content: "\f1b7";
}
.fa-recycle:before {
  content: "\f1b8";
}
.fa-automobile:before,
.fa-car:before {
  content: "\f1b9";
}
.fa-cab:before,
.fa-taxi:before {
  content: "\f1ba";
}
.fa-tree:before {
  content: "\f1bb";
}
.fa-spotify:before {
  content: "\f1bc";
}
.fa-deviantart:before {
  content: "\f1bd";
}
.fa-soundcloud:before {
  content: "\f1be";
}
.fa-database:before {
  content: "\f1c0";
}
.fa-file-pdf-o:before {
  content: "\f1c1";
}
.fa-file-word-o:before {
  content: "\f1c2";
}
.fa-file-excel-o:before {
  content: "\f1c3";
}
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}
.fa-file-photo-o:before,
.fa-file-picture-o:before,
.fa-file-image-o:before {
  content: "\f1c5";
}
.fa-file-zip-o:before,
.fa-file-archive-o:before {
  content: "\f1c6";
}
.fa-file-sound-o:before,
.fa-file-audio-o:before {
  content: "\f1c7";
}
.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: "\f1c8";
}
.fa-file-code-o:before {
  content: "\f1c9";
}
.fa-vine:before {
  content: "\f1ca";
}
.fa-codepen:before {
  content: "\f1cb";
}
.fa-jsfiddle:before {
  content: "\f1cc";
}
.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-saver:before,
.fa-support:before,
.fa-life-ring:before {
  content: "\f1cd";
}
.fa-circle-o-notch:before {
  content: "\f1ce";
}
.fa-ra:before,
.fa-resistance:before,
.fa-rebel:before {
  content: "\f1d0";
}
.fa-ge:before,
.fa-empire:before {
  content: "\f1d1";
}
.fa-git-square:before {
  content: "\f1d2";
}
.fa-git:before {
  content: "\f1d3";
}
.fa-y-combinator-square:before,
.fa-yc-square:before,
.fa-hacker-news:before {
  content: "\f1d4";
}
.fa-tencent-weibo:before {
  content: "\f1d5";
}
.fa-qq:before {
  content: "\f1d6";
}
.fa-wechat:before,
.fa-weixin:before {
  content: "\f1d7";
}
.fa-send:before,
.fa-paper-plane:before {
  content: "\f1d8";
}
.fa-send-o:before,
.fa-paper-plane-o:before {
  content: "\f1d9";
}
.fa-history:before {
  content: "\f1da";
}
.fa-circle-thin:before {
  content: "\f1db";
}
.fa-header:before {
  content: "\f1dc";
}
.fa-paragraph:before {
  content: "\f1dd";
}
.fa-sliders:before {
  content: "\f1de";
}
.fa-share-alt:before {
  content: "\f1e0";
}
.fa-share-alt-square:before {
  content: "\f1e1";
}
.fa-bomb:before {
  content: "\f1e2";
}
.fa-soccer-ball-o:before,
.fa-futbol-o:before {
  content: "\f1e3";
}
.fa-tty:before {
  content: "\f1e4";
}
.fa-binoculars:before {
  content: "\f1e5";
}
.fa-plug:before {
  content: "\f1e6";
}
.fa-slideshare:before {
  content: "\f1e7";
}
.fa-twitch:before {
  content: "\f1e8";
}
.fa-yelp:before {
  content: "\f1e9";
}
.fa-newspaper-o:before {
  content: "\f1ea";
}
.fa-wifi:before {
  content: "\f1eb";
}
.fa-calculator:before {
  content: "\f1ec";
}
.fa-paypal:before {
  content: "\f1ed";
}
.fa-google-wallet:before {
  content: "\f1ee";
}
.fa-cc-visa:before {
  content: "\f1f0";
}
.fa-cc-mastercard:before {
  content: "\f1f1";
}
.fa-cc-discover:before {
  content: "\f1f2";
}
.fa-cc-amex:before {
  content: "\f1f3";
}
.fa-cc-paypal:before {
  content: "\f1f4";
}
.fa-cc-stripe:before {
  content: "\f1f5";
}
.fa-bell-slash:before {
  content: "\f1f6";
}
.fa-bell-slash-o:before {
  content: "\f1f7";
}
.fa-trash:before {
  content: "\f1f8";
}
.fa-copyright:before {
  content: "\f1f9";
}
.fa-at:before {
  content: "\f1fa";
}
.fa-eyedropper:before {
  content: "\f1fb";
}
.fa-paint-brush:before {
  content: "\f1fc";
}
.fa-birthday-cake:before {
  content: "\f1fd";
}
.fa-area-chart:before {
  content: "\f1fe";
}
.fa-pie-chart:before {
  content: "\f200";
}
.fa-line-chart:before {
  content: "\f201";
}
.fa-lastfm:before {
  content: "\f202";
}
.fa-lastfm-square:before {
  content: "\f203";
}
.fa-toggle-off:before {
  content: "\f204";
}
.fa-toggle-on:before {
  content: "\f205";
}
.fa-bicycle:before {
  content: "\f206";
}
.fa-bus:before {
  content: "\f207";
}
.fa-ioxhost:before {
  content: "\f208";
}
.fa-angellist:before {
  content: "\f209";
}
.fa-cc:before {
  content: "\f20a";
}
.fa-shekel:before,
.fa-sheqel:before,
.fa-ils:before {
  content: "\f20b";
}
.fa-meanpath:before {
  content: "\f20c";
}
.fa-buysellads:before {
  content: "\f20d";
}
.fa-connectdevelop:before {
  content: "\f20e";
}
.fa-dashcube:before {
  content: "\f210";
}
.fa-forumbee:before {
  content: "\f211";
}
.fa-leanpub:before {
  content: "\f212";
}
.fa-sellsy:before {
  content: "\f213";
}
.fa-shirtsinbulk:before {
  content: "\f214";
}
.fa-simplybuilt:before {
  content: "\f215";
}
.fa-skyatlas:before {
  content: "\f216";
}
.fa-cart-plus:before {
  content: "\f217";
}
.fa-cart-arrow-down:before {
  content: "\f218";
}
.fa-diamond:before {
  content: "\f219";
}
.fa-ship:before {
  content: "\f21a";
}
.fa-user-secret:before {
  content: "\f21b";
}
.fa-motorcycle:before {
  content: "\f21c";
}
.fa-street-view:before {
  content: "\f21d";
}
.fa-heartbeat:before {
  content: "\f21e";
}
.fa-venus:before {
  content: "\f221";
}
.fa-mars:before {
  content: "\f222";
}
.fa-mercury:before {
  content: "\f223";
}
.fa-intersex:before,
.fa-transgender:before {
  content: "\f224";
}
.fa-transgender-alt:before {
  content: "\f225";
}
.fa-venus-double:before {
  content: "\f226";
}
.fa-mars-double:before {
  content: "\f227";
}
.fa-venus-mars:before {
  content: "\f228";
}
.fa-mars-stroke:before {
  content: "\f229";
}
.fa-mars-stroke-v:before {
  content: "\f22a";
}
.fa-mars-stroke-h:before {
  content: "\f22b";
}
.fa-neuter:before {
  content: "\f22c";
}
.fa-genderless:before {
  content: "\f22d";
}
.fa-facebook-official:before {
  content: "\f230";
}
.fa-pinterest-p:before {
  content: "\f231";
}
.fa-whatsapp:before {
  content: "\f232";
}
.fa-server:before {
  content: "\f233";
}
.fa-user-plus:before {
  content: "\f234";
}
.fa-user-times:before {
  content: "\f235";
}
.fa-hotel:before,
.fa-bed:before {
  content: "\f236";
}
.fa-viacoin:before {
  content: "\f237";
}
.fa-train:before {
  content: "\f238";
}
.fa-subway:before {
  content: "\f239";
}
.fa-medium:before {
  content: "\f23a";
}
.fa-yc:before,
.fa-y-combinator:before {
  content: "\f23b";
}
.fa-optin-monster:before {
  content: "\f23c";
}
.fa-opencart:before {
  content: "\f23d";
}
.fa-expeditedssl:before {
  content: "\f23e";
}
.fa-battery-4:before,
.fa-battery:before,
.fa-battery-full:before {
  content: "\f240";
}
.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: "\f241";
}
.fa-battery-2:before,
.fa-battery-half:before {
  content: "\f242";
}
.fa-battery-1:before,
.fa-battery-quarter:before {
  content: "\f243";
}
.fa-battery-0:before,
.fa-battery-empty:before {
  content: "\f244";
}
.fa-mouse-pointer:before {
  content: "\f245";
}
.fa-i-cursor:before {
  content: "\f246";
}
.fa-object-group:before {
  content: "\f247";
}
.fa-object-ungroup:before {
  content: "\f248";
}
.fa-sticky-note:before {
  content: "\f249";
}
.fa-sticky-note-o:before {
  content: "\f24a";
}
.fa-cc-jcb:before {
  content: "\f24b";
}
.fa-cc-diners-club:before {
  content: "\f24c";
}
.fa-clone:before {
  content: "\f24d";
}
.fa-balance-scale:before {
  content: "\f24e";
}
.fa-hourglass-o:before {
  content: "\f250";
}
.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: "\f251";
}
.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: "\f252";
}
.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: "\f253";
}
.fa-hourglass:before {
  content: "\f254";
}
.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: "\f255";
}
.fa-hand-stop-o:before,
.fa-hand-paper-o:before {
  content: "\f256";
}
.fa-hand-scissors-o:before {
  content: "\f257";
}
.fa-hand-lizard-o:before {
  content: "\f258";
}
.fa-hand-spock-o:before {
  content: "\f259";
}
.fa-hand-pointer-o:before {
  content: "\f25a";
}
.fa-hand-peace-o:before {
  content: "\f25b";
}
.fa-trademark:before {
  content: "\f25c";
}
.fa-registered:before {
  content: "\f25d";
}
.fa-creative-commons:before {
  content: "\f25e";
}
.fa-gg:before {
  content: "\f260";
}
.fa-gg-circle:before {
  content: "\f261";
}
.fa-tripadvisor:before {
  content: "\f262";
}
.fa-odnoklassniki:before {
  content: "\f263";
}
.fa-odnoklassniki-square:before {
  content: "\f264";
}
.fa-get-pocket:before {
  content: "\f265";
}
.fa-wikipedia-w:before {
  content: "\f266";
}
.fa-safari:before {
  content: "\f267";
}
.fa-chrome:before {
  content: "\f268";
}
.fa-firefox:before {
  content: "\f269";
}
.fa-opera:before {
  content: "\f26a";
}
.fa-internet-explorer:before {
  content: "\f26b";
}
.fa-tv:before,
.fa-television:before {
  content: "\f26c";
}
.fa-contao:before {
  content: "\f26d";
}
.fa-500px:before {
  content: "\f26e";
}
.fa-amazon:before {
  content: "\f270";
}
.fa-calendar-plus-o:before {
  content: "\f271";
}
.fa-calendar-minus-o:before {
  content: "\f272";
}
.fa-calendar-times-o:before {
  content: "\f273";
}
.fa-calendar-check-o:before {
  content: "\f274";
}
.fa-industry:before {
  content: "\f275";
}
.fa-map-pin:before {
  content: "\f276";
}
.fa-map-signs:before {
  content: "\f277";
}
.fa-map-o:before {
  content: "\f278";
}
.fa-map:before {
  content: "\f279";
}
.fa-commenting:before {
  content: "\f27a";
}
.fa-commenting-o:before {
  content: "\f27b";
}
.fa-houzz:before {
  content: "\f27c";
}
.fa-vimeo:before {
  content: "\f27d";
}
.fa-black-tie:before {
  content: "\f27e";
}
.fa-fonticons:before {
  content: "\f280";
}
.fa-reddit-alien:before {
  content: "\f281";
}
.fa-edge:before {
  content: "\f282";
}
.fa-credit-card-alt:before {
  content: "\f283";
}
.fa-codiepie:before {
  content: "\f284";
}
.fa-modx:before {
  content: "\f285";
}
.fa-fort-awesome:before {
  content: "\f286";
}
.fa-usb:before {
  content: "\f287";
}
.fa-product-hunt:before {
  content: "\f288";
}
.fa-mixcloud:before {
  content: "\f289";
}
.fa-scribd:before {
  content: "\f28a";
}
.fa-pause-circle:before {
  content: "\f28b";
}
.fa-pause-circle-o:before {
  content: "\f28c";
}
.fa-stop-circle:before {
  content: "\f28d";
}
.fa-stop-circle-o:before {
  content: "\f28e";
}
.fa-shopping-bag:before {
  content: "\f290";
}
.fa-shopping-basket:before {
  content: "\f291";
}
.fa-hashtag:before {
  content: "\f292";
}
.fa-bluetooth:before {
  content: "\f293";
}
.fa-bluetooth-b:before {
  content: "\f294";
}
.fa-percent:before {
  content: "\f295";
}
.fa-gitlab:before {
  content: "\f296";
}
.fa-wpbeginner:before {
  content: "\f297";
}
.fa-wpforms:before {
  content: "\f298";
}
.fa-envira:before {
  content: "\f299";
}
.fa-universal-access:before {
  content: "\f29a";
}
.fa-wheelchair-alt:before {
  content: "\f29b";
}
.fa-question-circle-o:before {
  content: "\f29c";
}
.fa-blind:before {
  content: "\f29d";
}
.fa-audio-description:before {
  content: "\f29e";
}
.fa-volume-control-phone:before {
  content: "\f2a0";
}
.fa-braille:before {
  content: "\f2a1";
}
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}
.fa-asl-interpreting:before,
.fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}
.fa-deafness:before,
.fa-hard-of-hearing:before,
.fa-deaf:before {
  content: "\f2a4";
}
.fa-glide:before {
  content: "\f2a5";
}
.fa-glide-g:before {
  content: "\f2a6";
}
.fa-signing:before,
.fa-sign-language:before {
  content: "\f2a7";
}
.fa-low-vision:before {
  content: "\f2a8";
}
.fa-viadeo:before {
  content: "\f2a9";
}
.fa-viadeo-square:before {
  content: "\f2aa";
}
.fa-snapchat:before {
  content: "\f2ab";
}
.fa-snapchat-ghost:before {
  content: "\f2ac";
}
.fa-snapchat-square:before {
  content: "\f2ad";
}
.fa-pied-piper:before {
  content: "\f2ae";
}
.fa-first-order:before {
  content: "\f2b0";
}
.fa-yoast:before {
  content: "\f2b1";
}
.fa-themeisle:before {
  content: "\f2b2";
}
.fa-google-plus-circle:before,
.fa-google-plus-official:before {
  content: "\f2b3";
}
.fa-fa:before,
.fa-font-awesome:before {
  content: "\f2b4";
}
.fa-handshake-o:before {
  content: "\f2b5";
}
.fa-envelope-open:before {
  content: "\f2b6";
}
.fa-envelope-open-o:before {
  content: "\f2b7";
}
.fa-linode:before {
  content: "\f2b8";
}
.fa-address-book:before {
  content: "\f2b9";
}
.fa-address-book-o:before {
  content: "\f2ba";
}
.fa-vcard:before,
.fa-address-card:before {
  content: "\f2bb";
}
.fa-vcard-o:before,
.fa-address-card-o:before {
  content: "\f2bc";
}
.fa-user-circle:before {
  content: "\f2bd";
}
.fa-user-circle-o:before {
  content: "\f2be";
}
.fa-user-o:before {
  content: "\f2c0";
}
.fa-id-badge:before {
  content: "\f2c1";
}
.fa-drivers-license:before,
.fa-id-card:before {
  content: "\f2c2";
}
.fa-drivers-license-o:before,
.fa-id-card-o:before {
  content: "\f2c3";
}
.fa-quora:before {
  content: "\f2c4";
}
.fa-free-code-camp:before {
  content: "\f2c5";
}
.fa-telegram:before {
  content: "\f2c6";
}
.fa-thermometer-4:before,
.fa-thermometer:before,
.fa-thermometer-full:before {
  content: "\f2c7";
}
.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
  content: "\f2c8";
}
.fa-thermometer-2:before,
.fa-thermometer-half:before {
  content: "\f2c9";
}
.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
  content: "\f2ca";
}
.fa-thermometer-0:before,
.fa-thermometer-empty:before {
  content: "\f2cb";
}
.fa-shower:before {
  content: "\f2cc";
}
.fa-bathtub:before,
.fa-s15:before,
.fa-bath:before {
  content: "\f2cd";
}
.fa-podcast:before {
  content: "\f2ce";
}
.fa-window-maximize:before {
  content: "\f2d0";
}
.fa-window-minimize:before {
  content: "\f2d1";
}
.fa-window-restore:before {
  content: "\f2d2";
}
.fa-times-rectangle:before,
.fa-window-close:before {
  content: "\f2d3";
}
.fa-times-rectangle-o:before,
.fa-window-close-o:before {
  content: "\f2d4";
}
.fa-bandcamp:before {
  content: "\f2d5";
}
.fa-grav:before {
  content: "\f2d6";
}
.fa-etsy:before {
  content: "\f2d7";
}
.fa-imdb:before {
  content: "\f2d8";
}
.fa-ravelry:before {
  content: "\f2d9";
}
.fa-eercast:before {
  content: "\f2da";
}
.fa-microchip:before {
  content: "\f2db";
}
.fa-snowflake-o:before {
  content: "\f2dc";
}
.fa-superpowers:before {
  content: "\f2dd";
}
.fa-wpexplorer:before {
  content: "\f2de";
}
.fa-meetup:before {
  content: "\f2e0";
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/* Themify Icons*/


@font-face {
  font-family: 'themify';
  src:url('../fonts/themify.eot?-fvbane');
  src:url('../fonts/themify.eot?#iefix-fvbane') format('embedded-opentype'),
    url('../fonts/themify.woff?-fvbane') format('woff'),
    url('../fonts/themify.ttf?-fvbane') format('truetype'),
    url('../fonts/themify.svg?-fvbane#themify') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="ti-"], [class*=" ti-"] {
  font-family: 'themify';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ti-wand:before {
  content: "\e600";
}
.ti-volume:before {
  content: "\e601";
}
.ti-user:before {
  content: "\e602";
}
.ti-unlock:before {
  content: "\e603";
}
.ti-unlink:before {
  content: "\e604";
}
.ti-trash:before {
  content: "\e605";
}
.ti-thought:before {
  content: "\e606";
}
.ti-target:before {
  content: "\e607";
}
.ti-tag:before {
  content: "\e608";
}
.ti-tablet:before {
  content: "\e609";
}
.ti-star:before {
  content: "\e60a";
}
.ti-spray:before {
  content: "\e60b";
}
.ti-signal:before {
  content: "\e60c";
}
.ti-shopping-cart:before {
  content: "\e60d";
}
.ti-shopping-cart-full:before {
  content: "\e60e";
}
.ti-settings:before {
  content: "\e60f";
}
.ti-search:before {
  content: "\e610";
}
.ti-zoom-in:before {
  content: "\e611";
}
.ti-zoom-out:before {
  content: "\e612";
}
.ti-cut:before {
  content: "\e613";
}
.ti-ruler:before {
  content: "\e614";
}
.ti-ruler-pencil:before {
  content: "\e615";
}
.ti-ruler-alt:before {
  content: "\e616";
}
.ti-bookmark:before {
  content: "\e617";
}
.ti-bookmark-alt:before {
  content: "\e618";
}
.ti-reload:before {
  content: "\e619";
}
.ti-plus:before {
  content: "\e61a";
}
.ti-pin:before {
  content: "\e61b";
}
.ti-pencil:before {
  content: "\e61c";
}
.ti-pencil-alt:before {
  content: "\e61d";
}
.ti-paint-roller:before {
  content: "\e61e";
}
.ti-paint-bucket:before {
  content: "\e61f";
}
.ti-na:before {
  content: "\e620";
}
.ti-mobile:before {
  content: "\e621";
}
.ti-minus:before {
  content: "\e622";
}
.ti-medall:before {
  content: "\e623";
}
.ti-medall-alt:before {
  content: "\e624";
}
.ti-marker:before {
  content: "\e625";
}
.ti-marker-alt:before {
  content: "\e626";
}
.ti-arrow-up:before {
  content: "\e627";
}
.ti-arrow-right:before {
  content: "\e628";
}
.ti-arrow-left:before {
  content: "\e629";
}
.ti-arrow-down:before {
  content: "\e62a";
}
.ti-lock:before {
  content: "\e62b";
}
.ti-location-arrow:before {
  content: "\e62c";
}
.ti-link:before {
  content: "\e62d";
}
.ti-layout:before {
  content: "\e62e";
}
.ti-layers:before {
  content: "\e62f";
}
.ti-layers-alt:before {
  content: "\e630";
}
.ti-key:before {
  content: "\e631";
}
.ti-import:before {
  content: "\e632";
}
.ti-image:before {
  content: "\e633";
}
.ti-heart:before {
  content: "\e634";
}
.ti-heart-broken:before {
  content: "\e635";
}
.ti-hand-stop:before {
  content: "\e636";
}
.ti-hand-open:before {
  content: "\e637";
}
.ti-hand-drag:before {
  content: "\e638";
}
.ti-folder:before {
  content: "\e639";
}
.ti-flag:before {
  content: "\e63a";
}
.ti-flag-alt:before {
  content: "\e63b";
}
.ti-flag-alt-2:before {
  content: "\e63c";
}
.ti-eye:before {
  content: "\e63d";
}
.ti-export:before {
  content: "\e63e";
}
.ti-exchange-vertical:before {
  content: "\e63f";
}
.ti-desktop:before {
  content: "\e640";
}
.ti-cup:before {
  content: "\e641";
}
.ti-crown:before {
  content: "\e642";
}
.ti-comments:before {
  content: "\e643";
}
.ti-comment:before {
  content: "\e644";
}
.ti-comment-alt:before {
  content: "\e645";
}
.ti-close:before {
  content: "\e646";
}
.ti-clip:before {
  content: "\e647";
}
.ti-angle-up:before {
  content: "\e648";
}
.ti-angle-right:before {
  content: "\e649";
}
.ti-angle-left:before {
  content: "\e64a";
}
.ti-angle-down:before {
  content: "\e64b";
}
.ti-check:before {
  content: "\e64c";
}
.ti-check-box:before {
  content: "\e64d";
}
.ti-camera:before {
  content: "\e64e";
}
.ti-announcement:before {
  content: "\e64f";
}
.ti-brush:before {
  content: "\e650";
}
.ti-briefcase:before {
  content: "\e651";
}
.ti-bolt:before {
  content: "\e652";
}
.ti-bolt-alt:before {
  content: "\e653";
}
.ti-blackboard:before {
  content: "\e654";
}
.ti-bag:before {
  content: "\e655";
}
.ti-move:before {
  content: "\e656";
}
.ti-arrows-vertical:before {
  content: "\e657";
}
.ti-arrows-horizontal:before {
  content: "\e658";
}
.ti-fullscreen:before {
  content: "\e659";
}
.ti-arrow-top-right:before {
  content: "\e65a";
}
.ti-arrow-top-left:before {
  content: "\e65b";
}
.ti-arrow-circle-up:before {
  content: "\e65c";
}
.ti-arrow-circle-right:before {
  content: "\e65d";
}
.ti-arrow-circle-left:before {
  content: "\e65e";
}
.ti-arrow-circle-down:before {
  content: "\e65f";
}
.ti-angle-double-up:before {
  content: "\e660";
}
.ti-angle-double-right:before {
  content: "\e661";
}
.ti-angle-double-left:before {
  content: "\e662";
}
.ti-angle-double-down:before {
  content: "\e663";
}
.ti-zip:before {
  content: "\e664";
}
.ti-world:before {
  content: "\e665";
}
.ti-wheelchair:before {
  content: "\e666";
}
.ti-view-list:before {
  content: "\e667";
}
.ti-view-list-alt:before {
  content: "\e668";
}
.ti-view-grid:before {
  content: "\e669";
}
.ti-uppercase:before {
  content: "\e66a";
}
.ti-upload:before {
  content: "\e66b";
}
.ti-underline:before {
  content: "\e66c";
}
.ti-truck:before {
  content: "\e66d";
}
.ti-timer:before {
  content: "\e66e";
}
.ti-ticket:before {
  content: "\e66f";
}
.ti-thumb-up:before {
  content: "\e670";
}
.ti-thumb-down:before {
  content: "\e671";
}
.ti-text:before {
  content: "\e672";
}
.ti-stats-up:before {
  content: "\e673";
}
.ti-stats-down:before {
  content: "\e674";
}
.ti-split-v:before {
  content: "\e675";
}
.ti-split-h:before {
  content: "\e676";
}
.ti-smallcap:before {
  content: "\e677";
}
.ti-shine:before {
  content: "\e678";
}
.ti-shift-right:before {
  content: "\e679";
}
.ti-shift-left:before {
  content: "\e67a";
}
.ti-shield:before {
  content: "\e67b";
}
.ti-notepad:before {
  content: "\e67c";
}
.ti-server:before {
  content: "\e67d";
}
.ti-quote-right:before {
  content: "\e67e";
}
.ti-quote-left:before {
  content: "\e67f";
}
.ti-pulse:before {
  content: "\e680";
}
.ti-printer:before {
  content: "\e681";
}
.ti-power-off:before {
  content: "\e682";
}
.ti-plug:before {
  content: "\e683";
}
.ti-pie-chart:before {
  content: "\e684";
}
.ti-paragraph:before {
  content: "\e685";
}
.ti-panel:before {
  content: "\e686";
}
.ti-package:before {
  content: "\e687";
}
.ti-music:before {
  content: "\e688";
}
.ti-music-alt:before {
  content: "\e689";
}
.ti-mouse:before {
  content: "\e68a";
}
.ti-mouse-alt:before {
  content: "\e68b";
}
.ti-money:before {
  content: "\e68c";
}
.ti-microphone:before {
  content: "\e68d";
}
.ti-menu:before {
  content: "\e68e";
}
.ti-menu-alt:before {
  content: "\e68f";
}
.ti-map:before {
  content: "\e690";
}
.ti-map-alt:before {
  content: "\e691";
}
.ti-loop:before {
  content: "\e692";
}
.ti-location-pin:before {
  content: "\e693";
}
.ti-list:before {
  content: "\e694";
}
.ti-light-bulb:before {
  content: "\e695";
}
.ti-Italic:before {
  content: "\e696";
}
.ti-info:before {
  content: "\e697";
}
.ti-infinite:before {
  content: "\e698";
}
.ti-id-badge:before {
  content: "\e699";
}
.ti-hummer:before {
  content: "\e69a";
}
.ti-home:before {
  content: "\e69b";
}
.ti-help:before {
  content: "\e69c";
}
.ti-headphone:before {
  content: "\e69d";
}
.ti-harddrives:before {
  content: "\e69e";
}
.ti-harddrive:before {
  content: "\e69f";
}
.ti-gift:before {
  content: "\e6a0";
}
.ti-game:before {
  content: "\e6a1";
}
.ti-filter:before {
  content: "\e6a2";
}
.ti-files:before {
  content: "\e6a3";
}
.ti-file:before {
  content: "\e6a4";
}
.ti-eraser:before {
  content: "\e6a5";
}
.ti-envelope:before {
  content: "\e6a6";
}
.ti-download:before {
  content: "\e6a7";
}
.ti-direction:before {
  content: "\e6a8";
}
.ti-direction-alt:before {
  content: "\e6a9";
}
.ti-dashboard:before {
  content: "\e6aa";
}
.ti-control-stop:before {
  content: "\e6ab";
}
.ti-control-shuffle:before {
  content: "\e6ac";
}
.ti-control-play:before {
  content: "\e6ad";
}
.ti-control-pause:before {
  content: "\e6ae";
}
.ti-control-forward:before {
  content: "\e6af";
}
.ti-control-backward:before {
  content: "\e6b0";
}
.ti-cloud:before {
  content: "\e6b1";
}
.ti-cloud-up:before {
  content: "\e6b2";
}
.ti-cloud-down:before {
  content: "\e6b3";
}
.ti-clipboard:before {
  content: "\e6b4";
}
.ti-car:before {
  content: "\e6b5";
}
.ti-calendar:before {
  content: "\e6b6";
}
.ti-book:before {
  content: "\e6b7";
}
.ti-bell:before {
  content: "\e6b8";
}
.ti-basketball:before {
  content: "\e6b9";
}
.ti-bar-chart:before {
  content: "\e6ba";
}
.ti-bar-chart-alt:before {
  content: "\e6bb";
}
.ti-back-right:before {
  content: "\e6bc";
}
.ti-back-left:before {
  content: "\e6bd";
}
.ti-arrows-corner:before {
  content: "\e6be";
}
.ti-archive:before {
  content: "\e6bf";
}
.ti-anchor:before {
  content: "\e6c0";
}
.ti-align-right:before {
  content: "\e6c1";
}
.ti-align-left:before {
  content: "\e6c2";
}
.ti-align-justify:before {
  content: "\e6c3";
}
.ti-align-center:before {
  content: "\e6c4";
}
.ti-alert:before {
  content: "\e6c5";
}
.ti-alarm-clock:before {
  content: "\e6c6";
}
.ti-agenda:before {
  content: "\e6c7";
}
.ti-write:before {
  content: "\e6c8";
}
.ti-window:before {
  content: "\e6c9";
}
.ti-widgetized:before {
  content: "\e6ca";
}
.ti-widget:before {
  content: "\e6cb";
}
.ti-widget-alt:before {
  content: "\e6cc";
}
.ti-wallet:before {
  content: "\e6cd";
}
.ti-video-clapper:before {
  content: "\e6ce";
}
.ti-video-camera:before {
  content: "\e6cf";
}
.ti-vector:before {
  content: "\e6d0";
}
.ti-themify-logo:before {
  content: "\e6d1";
}
.ti-themify-favicon:before {
  content: "\e6d2";
}
.ti-themify-favicon-alt:before {
  content: "\e6d3";
}
.ti-support:before {
  content: "\e6d4";
}
.ti-stamp:before {
  content: "\e6d5";
}
.ti-split-v-alt:before {
  content: "\e6d6";
}
.ti-slice:before {
  content: "\e6d7";
}
.ti-shortcode:before {
  content: "\e6d8";
}
.ti-shift-right-alt:before {
  content: "\e6d9";
}
.ti-shift-left-alt:before {
  content: "\e6da";
}
.ti-ruler-alt-2:before {
  content: "\e6db";
}
.ti-receipt:before {
  content: "\e6dc";
}
.ti-pin2:before {
  content: "\e6dd";
}
.ti-pin-alt:before {
  content: "\e6de";
}
.ti-pencil-alt2:before {
  content: "\e6df";
}
.ti-palette:before {
  content: "\e6e0";
}
.ti-more:before {
  content: "\e6e1";
}
.ti-more-alt:before {
  content: "\e6e2";
}
.ti-microphone-alt:before {
  content: "\e6e3";
}
.ti-magnet:before {
  content: "\e6e4";
}
.ti-line-double:before {
  content: "\e6e5";
}
.ti-line-dotted:before {
  content: "\e6e6";
}
.ti-line-dashed:before {
  content: "\e6e7";
}
.ti-layout-width-full:before {
  content: "\e6e8";
}
.ti-layout-width-default:before {
  content: "\e6e9";
}
.ti-layout-width-default-alt:before {
  content: "\e6ea";
}
.ti-layout-tab:before {
  content: "\e6eb";
}
.ti-layout-tab-window:before {
  content: "\e6ec";
}
.ti-layout-tab-v:before {
  content: "\e6ed";
}
.ti-layout-tab-min:before {
  content: "\e6ee";
}
.ti-layout-slider:before {
  content: "\e6ef";
}
.ti-layout-slider-alt:before {
  content: "\e6f0";
}
.ti-layout-sidebar-right:before {
  content: "\e6f1";
}
.ti-layout-sidebar-none:before {
  content: "\e6f2";
}
.ti-layout-sidebar-left:before {
  content: "\e6f3";
}
.ti-layout-placeholder:before {
  content: "\e6f4";
}
.ti-layout-menu:before {
  content: "\e6f5";
}
.ti-layout-menu-v:before {
  content: "\e6f6";
}
.ti-layout-menu-separated:before {
  content: "\e6f7";
}
.ti-layout-menu-full:before {
  content: "\e6f8";
}
.ti-layout-media-right-alt:before {
  content: "\e6f9";
}
.ti-layout-media-right:before {
  content: "\e6fa";
}
.ti-layout-media-overlay:before {
  content: "\e6fb";
}
.ti-layout-media-overlay-alt:before {
  content: "\e6fc";
}
.ti-layout-media-overlay-alt-2:before {
  content: "\e6fd";
}
.ti-layout-media-left-alt:before {
  content: "\e6fe";
}
.ti-layout-media-left:before {
  content: "\e6ff";
}
.ti-layout-media-center-alt:before {
  content: "\e700";
}
.ti-layout-media-center:before {
  content: "\e701";
}
.ti-layout-list-thumb:before {
  content: "\e702";
}
.ti-layout-list-thumb-alt:before {
  content: "\e703";
}
.ti-layout-list-post:before {
  content: "\e704";
}
.ti-layout-list-large-image:before {
  content: "\e705";
}
.ti-layout-line-solid:before {
  content: "\e706";
}
.ti-layout-grid4:before {
  content: "\e707";
}
.ti-layout-grid3:before {
  content: "\e708";
}
.ti-layout-grid2:before {
  content: "\e709";
}
.ti-layout-grid2-thumb:before {
  content: "\e70a";
}
.ti-layout-cta-right:before {
  content: "\e70b";
}
.ti-layout-cta-left:before {
  content: "\e70c";
}
.ti-layout-cta-center:before {
  content: "\e70d";
}
.ti-layout-cta-btn-right:before {
  content: "\e70e";
}
.ti-layout-cta-btn-left:before {
  content: "\e70f";
}
.ti-layout-column4:before {
  content: "\e710";
}
.ti-layout-column3:before {
  content: "\e711";
}
.ti-layout-column2:before {
  content: "\e712";
}
.ti-layout-accordion-separated:before {
  content: "\e713";
}
.ti-layout-accordion-merged:before {
  content: "\e714";
}
.ti-layout-accordion-list:before {
  content: "\e715";
}
.ti-ink-pen:before {
  content: "\e716";
}
.ti-info-alt:before {
  content: "\e717";
}
.ti-help-alt:before {
  content: "\e718";
}
.ti-headphone-alt:before {
  content: "\e719";
}
.ti-hand-point-up:before {
  content: "\e71a";
}
.ti-hand-point-right:before {
  content: "\e71b";
}
.ti-hand-point-left:before {
  content: "\e71c";
}
.ti-hand-point-down:before {
  content: "\e71d";
}
.ti-gallery:before {
  content: "\e71e";
}
.ti-face-smile:before {
  content: "\e71f";
}
.ti-face-sad:before {
  content: "\e720";
}
.ti-credit-card:before {
  content: "\e721";
}
.ti-control-skip-forward:before {
  content: "\e722";
}
.ti-control-skip-backward:before {
  content: "\e723";
}
.ti-control-record:before {
  content: "\e724";
}
.ti-control-eject:before {
  content: "\e725";
}
.ti-comments-smiley:before {
  content: "\e726";
}
.ti-brush-alt:before {
  content: "\e727";
}
.ti-youtube:before {
  content: "\e728";
}
.ti-vimeo:before {
  content: "\e729";
}
.ti-twitter:before {
  content: "\e72a";
}
.ti-time:before {
  content: "\e72b";
}
.ti-tumblr:before {
  content: "\e72c";
}
.ti-skype:before {
  content: "\e72d";
}
.ti-share:before {
  content: "\e72e";
}
.ti-share-alt:before {
  content: "\e72f";
}
.ti-rocket:before {
  content: "\e730";
}
.ti-pinterest:before {
  content: "\e731";
}
.ti-new-window:before {
  content: "\e732";
}
.ti-microsoft:before {
  content: "\e733";
}
.ti-list-ol:before {
  content: "\e734";
}
.ti-linkedin:before {
  content: "\e735";
}
.ti-layout-sidebar-2:before {
  content: "\e736";
}
.ti-layout-grid4-alt:before {
  content: "\e737";
}
.ti-layout-grid3-alt:before {
  content: "\e738";
}
.ti-layout-grid2-alt:before {
  content: "\e739";
}
.ti-layout-column4-alt:before {
  content: "\e73a";
}
.ti-layout-column3-alt:before {
  content: "\e73b";
}
.ti-layout-column2-alt:before {
  content: "\e73c";
}
.ti-instagram:before {
  content: "\e73d";
}
.ti-google:before {
  content: "\e73e";
}
.ti-github:before {
  content: "\e73f";
}
.ti-flickr:before {
  content: "\e740";
}
.ti-facebook:before {
  content: "\e741";
}
.ti-dropbox:before {
  content: "\e742";
}
.ti-dribbble:before {
  content: "\e743";
}
.ti-apple:before {
  content: "\e744";
}
.ti-android:before {
  content: "\e745";
}
.ti-save:before {
  content: "\e746";
}
.ti-save-alt:before {
  content: "\e747";
}
.ti-yahoo:before {
  content: "\e748";
}
.ti-wordpress:before {
  content: "\e749";
}
.ti-vimeo-alt:before {
  content: "\e74a";
}
.ti-twitter-alt:before {
  content: "\e74b";
}
.ti-tumblr-alt:before {
  content: "\e74c";
}
.ti-trello:before {
  content: "\e74d";
}
.ti-stack-overflow:before {
  content: "\e74e";
}
.ti-soundcloud:before {
  content: "\e74f";
}
.ti-sharethis:before {
  content: "\e750";
}
.ti-sharethis-alt:before {
  content: "\e751";
}
.ti-reddit:before {
  content: "\e752";
}
.ti-pinterest-alt:before {
  content: "\e753";
}
.ti-microsoft-alt:before {
  content: "\e754";
}
.ti-linux:before {
  content: "\e755";
}
.ti-jsfiddle:before {
  content: "\e756";
}
.ti-joomla:before {
  content: "\e757";
}
.ti-html5:before {
  content: "\e758";
}
.ti-flickr-alt:before {
  content: "\e759";
}
.ti-email:before {
  content: "\e75a";
}
.ti-drupal:before {
  content: "\e75b";
}
.ti-dropbox-alt:before {
  content: "\e75c";
}
.ti-css3:before {
  content: "\e75d";
}
.ti-rss:before {
  content: "\e75e";
}
.ti-rss-alt:before {
  content: "\e75f";
}






/* simple line icons*/
@font-face {
  font-family: 'simple-line-icons';
  src: url('../fonts/Simple-Line-Icons.eot?v=2.4.0');
  src: url('../fonts/Simple-Line-Icons.eot?v=2.4.0#iefix') format('embedded-opentype'), url('../fonts/Simple-Line-Icons.woff2?v=2.4.0') format('woff2'), url('../fonts/Simple-Line-Icons.ttf?v=2.4.0') format('truetype'), url('../fonts/Simple-Line-Icons.woff?v=2.4.0') format('woff'), url('../fonts/Simple-Line-Icons.svg?v=2.4.0#simple-line-icons') format('svg');
  font-weight: normal;
  font-style: normal;
}
/*
 Use the following CSS code if you want to have a class per icon.
 Instead of a list of all class selectors, you can use the generic [class*="icon-"] selector, but it's slower:
*/
.icon-user,
.icon-people,
.icon-user-female,
.icon-user-follow,
.icon-user-following,
.icon-user-unfollow,
.icon-login,
.icon-logout,
.icon-emotsmile,
.icon-phone,
.icon-call-end,
.icon-call-in,
.icon-call-out,
.icon-map,
.icon-location-pin,
.icon-direction,
.icon-directions,
.icon-compass,
.icon-layers,
.icon-menu,
.icon-list,
.icon-options-vertical,
.icon-options,
.icon-arrow-down,
.icon-arrow-left,
.icon-arrow-right,
.icon-arrow-up,
.icon-arrow-up-circle,
.icon-arrow-left-circle,
.icon-arrow-right-circle,
.icon-arrow-down-circle,
.icon-check,
.icon-clock,
.icon-plus,
.icon-minus,
.icon-close,
.icon-event,
.icon-exclamation,
.icon-organization,
.icon-trophy,
.icon-screen-smartphone,
.icon-screen-desktop,
.icon-plane,
.icon-notebook,
.icon-mustache,
.icon-mouse,
.icon-magnet,
.icon-energy,
.icon-disc,
.icon-cursor,
.icon-cursor-move,
.icon-crop,
.icon-chemistry,
.icon-speedometer,
.icon-shield,
.icon-screen-tablet,
.icon-magic-wand,
.icon-hourglass,
.icon-graduation,
.icon-ghost,
.icon-game-controller,
.icon-fire,
.icon-eyeglass,
.icon-envelope-open,
.icon-envelope-letter,
.icon-bell,
.icon-badge,
.icon-anchor,
.icon-wallet,
.icon-vector,
.icon-speech,
.icon-puzzle,
.icon-printer,
.icon-present,
.icon-playlist,
.icon-pin,
.icon-picture,
.icon-handbag,
.icon-globe-alt,
.icon-globe,
.icon-folder-alt,
.icon-folder,
.icon-film,
.icon-feed,
.icon-drop,
.icon-drawer,
.icon-docs,
.icon-doc,
.icon-diamond,
.icon-cup,
.icon-calculator,
.icon-bubbles,
.icon-briefcase,
.icon-book-open,
.icon-basket-loaded,
.icon-basket,
.icon-bag,
.icon-action-undo,
.icon-action-redo,
.icon-wrench,
.icon-umbrella,
.icon-trash,
.icon-tag,
.icon-support,
.icon-frame,
.icon-size-fullscreen,
.icon-size-actual,
.icon-shuffle,
.icon-share-alt,
.icon-share,
.icon-rocket,
.icon-question,
.icon-pie-chart,
.icon-pencil,
.icon-note,
.icon-loop,
.icon-home,
.icon-grid,
.icon-graph,
.icon-microphone,
.icon-music-tone-alt,
.icon-music-tone,
.icon-earphones-alt,
.icon-earphones,
.icon-equalizer,
.icon-like,
.icon-dislike,
.icon-control-start,
.icon-control-rewind,
.icon-control-play,
.icon-control-pause,
.icon-control-forward,
.icon-control-end,
.icon-volume-1,
.icon-volume-2,
.icon-volume-off,
.icon-calendar,
.icon-bulb,
.icon-chart,
.icon-ban,
.icon-bubble,
.icon-camrecorder,
.icon-camera,
.icon-cloud-download,
.icon-cloud-upload,
.icon-envelope,
.icon-eye,
.icon-flag,
.icon-heart,
.icon-info,
.icon-key,
.icon-link,
.icon-lock,
.icon-lock-open,
.icon-magnifier,
.icon-magnifier-add,
.icon-magnifier-remove,
.icon-paper-clip,
.icon-paper-plane,
.icon-power,
.icon-refresh,
.icon-reload,
.icon-settings,
.icon-star,
.icon-symbol-female,
.icon-symbol-male,
.icon-target,
.icon-credit-card,
.icon-paypal,
.icon-social-tumblr,
.icon-social-twitter,
.icon-social-facebook,
.icon-social-instagram,
.icon-social-linkedin,
.icon-social-pinterest,
.icon-social-github,
.icon-social-google,
.icon-social-reddit,
.icon-social-skype,
.icon-social-dribbble,
.icon-social-behance,
.icon-social-foursqare,
.icon-social-soundcloud,
.icon-social-spotify,
.icon-social-stumbleupon,
.icon-social-youtube,
.icon-social-dropbox,
.icon-social-vkontakte,
.icon-social-steam {
  font-family: 'simple-line-icons';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-user:before {
  content: "\e005";
}
.icon-people:before {
  content: "\e001";
}
.icon-user-female:before {
  content: "\e000";
}
.icon-user-follow:before {
  content: "\e002";
}
.icon-user-following:before {
  content: "\e003";
}
.icon-user-unfollow:before {
  content: "\e004";
}
.icon-login:before {
  content: "\e066";
}
.icon-logout:before {
  content: "\e065";
}
.icon-emotsmile:before {
  content: "\e021";
}
.icon-phone:before {
  content: "\e600";
}
.icon-call-end:before {
  content: "\e048";
}
.icon-call-in:before {
  content: "\e047";
}
.icon-call-out:before {
  content: "\e046";
}
.icon-map:before {
  content: "\e033";
}
.icon-location-pin:before {
  content: "\e096";
}
.icon-direction:before {
  content: "\e042";
}
.icon-directions:before {
  content: "\e041";
}
.icon-compass:before {
  content: "\e045";
}
.icon-layers:before {
  content: "\e034";
}
.icon-menu:before {
  content: "\e601";
}
.icon-list:before {
  content: "\e067";
}
.icon-options-vertical:before {
  content: "\e602";
}
.icon-options:before {
  content: "\e603";
}
.icon-arrow-down:before {
  content: "\e604";
}
.icon-arrow-left:before {
  content: "\e605";
}
.icon-arrow-right:before {
  content: "\e606";
}
.icon-arrow-up:before {
  content: "\e607";
}
.icon-arrow-up-circle:before {
  content: "\e078";
}
.icon-arrow-left-circle:before {
  content: "\e07a";
}
.icon-arrow-right-circle:before {
  content: "\e079";
}
.icon-arrow-down-circle:before {
  content: "\e07b";
}
.icon-check:before {
  content: "\e080";
}
.icon-clock:before {
  content: "\e081";
}
.icon-plus:before {
  content: "\e095";
}
.icon-minus:before {
  content: "\e615";
}
.icon-close:before {
  content: "\e082";
}
.icon-event:before {
  content: "\e619";
}
.icon-exclamation:before {
  content: "\e617";
}
.icon-organization:before {
  content: "\e616";
}
.icon-trophy:before {
  content: "\e006";
}
.icon-screen-smartphone:before {
  content: "\e010";
}
.icon-screen-desktop:before {
  content: "\e011";
}
.icon-plane:before {
  content: "\e012";
}
.icon-notebook:before {
  content: "\e013";
}
.icon-mustache:before {
  content: "\e014";
}
.icon-mouse:before {
  content: "\e015";
}
.icon-magnet:before {
  content: "\e016";
}
.icon-energy:before {
  content: "\e020";
}
.icon-disc:before {
  content: "\e022";
}
.icon-cursor:before {
  content: "\e06e";
}
.icon-cursor-move:before {
  content: "\e023";
}
.icon-crop:before {
  content: "\e024";
}
.icon-chemistry:before {
  content: "\e026";
}
.icon-speedometer:before {
  content: "\e007";
}
.icon-shield:before {
  content: "\e00e";
}
.icon-screen-tablet:before {
  content: "\e00f";
}
.icon-magic-wand:before {
  content: "\e017";
}
.icon-hourglass:before {
  content: "\e018";
}
.icon-graduation:before {
  content: "\e019";
}
.icon-ghost:before {
  content: "\e01a";
}
.icon-game-controller:before {
  content: "\e01b";
}
.icon-fire:before {
  content: "\e01c";
}
.icon-eyeglass:before {
  content: "\e01d";
}
.icon-envelope-open:before {
  content: "\e01e";
}
.icon-envelope-letter:before {
  content: "\e01f";
}
.icon-bell:before {
  content: "\e027";
}
.icon-badge:before {
  content: "\e028";
}
.icon-anchor:before {
  content: "\e029";
}
.icon-wallet:before {
  content: "\e02a";
}
.icon-vector:before {
  content: "\e02b";
}
.icon-speech:before {
  content: "\e02c";
}
.icon-puzzle:before {
  content: "\e02d";
}
.icon-printer:before {
  content: "\e02e";
}
.icon-present:before {
  content: "\e02f";
}
.icon-playlist:before {
  content: "\e030";
}
.icon-pin:before {
  content: "\e031";
}
.icon-picture:before {
  content: "\e032";
}
.icon-handbag:before {
  content: "\e035";
}
.icon-globe-alt:before {
  content: "\e036";
}
.icon-globe:before {
  content: "\e037";
}
.icon-folder-alt:before {
  content: "\e039";
}
.icon-folder:before {
  content: "\e089";
}
.icon-film:before {
  content: "\e03a";
}
.icon-feed:before {
  content: "\e03b";
}
.icon-drop:before {
  content: "\e03e";
}
.icon-drawer:before {
  content: "\e03f";
}
.icon-docs:before {
  content: "\e040";
}
.icon-doc:before {
  content: "\e085";
}
.icon-diamond:before {
  content: "\e043";
}
.icon-cup:before {
  content: "\e044";
}
.icon-calculator:before {
  content: "\e049";
}
.icon-bubbles:before {
  content: "\e04a";
}
.icon-briefcase:before {
  content: "\e04b";
}
.icon-book-open:before {
  content: "\e04c";
}
.icon-basket-loaded:before {
  content: "\e04d";
}
.icon-basket:before {
  content: "\e04e";
}
.icon-bag:before {
  content: "\e04f";
}
.icon-action-undo:before {
  content: "\e050";
}
.icon-action-redo:before {
  content: "\e051";
}
.icon-wrench:before {
  content: "\e052";
}
.icon-umbrella:before {
  content: "\e053";
}
.icon-trash:before {
  content: "\e054";
}
.icon-tag:before {
  content: "\e055";
}
.icon-support:before {
  content: "\e056";
}
.icon-frame:before {
  content: "\e038";
}
.icon-size-fullscreen:before {
  content: "\e057";
}
.icon-size-actual:before {
  content: "\e058";
}
.icon-shuffle:before {
  content: "\e059";
}
.icon-share-alt:before {
  content: "\e05a";
}
.icon-share:before {
  content: "\e05b";
}
.icon-rocket:before {
  content: "\e05c";
}
.icon-question:before {
  content: "\e05d";
}
.icon-pie-chart:before {
  content: "\e05e";
}
.icon-pencil:before {
  content: "\e05f";
}
.icon-note:before {
  content: "\e060";
}
.icon-loop:before {
  content: "\e064";
}
.icon-home:before {
  content: "\e069";
}
.icon-grid:before {
  content: "\e06a";
}
.icon-graph:before {
  content: "\e06b";
}
.icon-microphone:before {
  content: "\e063";
}
.icon-music-tone-alt:before {
  content: "\e061";
}
.icon-music-tone:before {
  content: "\e062";
}
.icon-earphones-alt:before {
  content: "\e03c";
}
.icon-earphones:before {
  content: "\e03d";
}
.icon-equalizer:before {
  content: "\e06c";
}
.icon-like:before {
  content: "\e068";
}
.icon-dislike:before {
  content: "\e06d";
}
.icon-control-start:before {
  content: "\e06f";
}
.icon-control-rewind:before {
  content: "\e070";
}
.icon-control-play:before {
  content: "\e071";
}
.icon-control-pause:before {
  content: "\e072";
}
.icon-control-forward:before {
  content: "\e073";
}
.icon-control-end:before {
  content: "\e074";
}
.icon-volume-1:before {
  content: "\e09f";
}
.icon-volume-2:before {
  content: "\e0a0";
}
.icon-volume-off:before {
  content: "\e0a1";
}
.icon-calendar:before {
  content: "\e075";
}
.icon-bulb:before {
  content: "\e076";
}
.icon-chart:before {
  content: "\e077";
}
.icon-ban:before {
  content: "\e07c";
}
.icon-bubble:before {
  content: "\e07d";
}
.icon-camrecorder:before {
  content: "\e07e";
}
.icon-camera:before {
  content: "\e07f";
}
.icon-cloud-download:before {
  content: "\e083";
}
.icon-cloud-upload:before {
  content: "\e084";
}
.icon-envelope:before {
  content: "\e086";
}
.icon-eye:before {
  content: "\e087";
}
.icon-flag:before {
  content: "\e088";
}
.icon-heart:before {
  content: "\e08a";
}
.icon-info:before {
  content: "\e08b";
}
.icon-key:before {
  content: "\e08c";
}
.icon-link:before {
  content: "\e08d";
}
.icon-lock:before {
  content: "\e08e";
}
.icon-lock-open:before {
  content: "\e08f";
}
.icon-magnifier:before {
  content: "\e090";
}
.icon-magnifier-add:before {
  content: "\e091";
}
.icon-magnifier-remove:before {
  content: "\e092";
}
.icon-paper-clip:before {
  content: "\e093";
}
.icon-paper-plane:before {
  content: "\e094";
}
.icon-power:before {
  content: "\e097";
}
.icon-refresh:before {
  content: "\e098";
}
.icon-reload:before {
  content: "\e099";
}
.icon-settings:before {
  content: "\e09a";
}
.icon-star:before {
  content: "\e09b";
}
.icon-symbol-female:before {
  content: "\e09c";
}
.icon-symbol-male:before {
  content: "\e09d";
}
.icon-target:before {
  content: "\e09e";
}
.icon-credit-card:before {
  content: "\e025";
}
.icon-paypal:before {
  content: "\e608";
}
.icon-social-tumblr:before {
  content: "\e00a";
}
.icon-social-twitter:before {
  content: "\e009";
}
.icon-social-facebook:before {
  content: "\e00b";
}
.icon-social-instagram:before {
  content: "\e609";
}
.icon-social-linkedin:before {
  content: "\e60a";
}
.icon-social-pinterest:before {
  content: "\e60b";
}
.icon-social-github:before {
  content: "\e60c";
}
.icon-social-google:before {
  content: "\e60d";
}
.icon-social-reddit:before {
  content: "\e60e";
}
.icon-social-skype:before {
  content: "\e60f";
}
.icon-social-dribbble:before {
  content: "\e00d";
}
.icon-social-behance:before {
  content: "\e610";
}
.icon-social-foursqare:before {
  content: "\e611";
}
.icon-social-soundcloud:before {
  content: "\e612";
}
.icon-social-spotify:before {
  content: "\e613";
}
.icon-social-stumbleupon:before {
  content: "\e614";
}
.icon-social-youtube:before {
  content: "\e008";
}
.icon-social-dropbox:before {
  content: "\e00c";
}
.icon-social-vkontakte:before {
  content: "\e618";
}
.icon-social-steam:before {
  content: "\e620";
}



/* Weather Icons*/

/*!
 *  Weather Icons 2.0.8
 *  Updated September 19, 2015
 *  Weather themed icons for Bootstrap
 *  Author - Erik Flowers - <EMAIL>
 *  Email: <EMAIL>
 *  Twitter: http://twitter.com/Erik_UX
 *  ------------------------------------------------------------------------------
 *  Maintained at http://erikflowers.github.io/weather-icons
 *
 *  License
 *  ------------------------------------------------------------------------------
 *  - Font licensed under SIL OFL 1.1 -
 *    http://scripts.sil.org/OFL
 *  - CSS, SCSS and LESS are licensed under MIT License -
 *    http://opensource.org/licenses/mit-license.html
 *  - Documentation licensed under CC BY 3.0 -
 *    http://creativecommons.org/licenses/by/3.0/
 *  - Inspired by and works great as a companion with Font Awesome
 *    "Font Awesome by Dave Gandy - http://fontawesome.io"
 */
@font-face {
  font-family: 'weathericons';
  src: url('../fonts/weathericons-regular-webfont.eot');
  src: url('../fonts/weathericons-regular-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/weathericons-regular-webfont.woff2') format('woff2'), url('../font/weathericons-regular-webfont.woff') format('woff'), url('../font/weathericons-regular-webfont.ttf') format('truetype'), url('../font/weathericons-regular-webfont.svg#weather_iconsregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
.wi {
  display: inline-block;
  font-family: 'weathericons';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.wi-fw {
  text-align: center;
  width: 1.4em;
}
.wi-rotate-90 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.wi-rotate-180 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.wi-rotate-270 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}
.wi-flip-horizontal {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}
.wi-flip-vertical {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}
.wi-day-sunny:before {
  content: "\f00d";
}
.wi-day-cloudy:before {
  content: "\f002";
}
.wi-day-cloudy-gusts:before {
  content: "\f000";
}
.wi-day-cloudy-windy:before {
  content: "\f001";
}
.wi-day-fog:before {
  content: "\f003";
}
.wi-day-hail:before {
  content: "\f004";
}
.wi-day-haze:before {
  content: "\f0b6";
}
.wi-day-lightning:before {
  content: "\f005";
}
.wi-day-rain:before {
  content: "\f008";
}
.wi-day-rain-mix:before {
  content: "\f006";
}
.wi-day-rain-wind:before {
  content: "\f007";
}
.wi-day-showers:before {
  content: "\f009";
}
.wi-day-sleet:before {
  content: "\f0b2";
}
.wi-day-sleet-storm:before {
  content: "\f068";
}
.wi-day-snow:before {
  content: "\f00a";
}
.wi-day-snow-thunderstorm:before {
  content: "\f06b";
}
.wi-day-snow-wind:before {
  content: "\f065";
}
.wi-day-sprinkle:before {
  content: "\f00b";
}
.wi-day-storm-showers:before {
  content: "\f00e";
}
.wi-day-sunny-overcast:before {
  content: "\f00c";
}
.wi-day-thunderstorm:before {
  content: "\f010";
}
.wi-day-windy:before {
  content: "\f085";
}
.wi-solar-eclipse:before {
  content: "\f06e";
}
.wi-hot:before {
  content: "\f072";
}
.wi-day-cloudy-high:before {
  content: "\f07d";
}
.wi-day-light-wind:before {
  content: "\f0c4";
}
.wi-night-clear:before {
  content: "\f02e";
}
.wi-night-alt-cloudy:before {
  content: "\f086";
}
.wi-night-alt-cloudy-gusts:before {
  content: "\f022";
}
.wi-night-alt-cloudy-windy:before {
  content: "\f023";
}
.wi-night-alt-hail:before {
  content: "\f024";
}
.wi-night-alt-lightning:before {
  content: "\f025";
}
.wi-night-alt-rain:before {
  content: "\f028";
}
.wi-night-alt-rain-mix:before {
  content: "\f026";
}
.wi-night-alt-rain-wind:before {
  content: "\f027";
}
.wi-night-alt-showers:before {
  content: "\f029";
}
.wi-night-alt-sleet:before {
  content: "\f0b4";
}
.wi-night-alt-sleet-storm:before {
  content: "\f06a";
}
.wi-night-alt-snow:before {
  content: "\f02a";
}
.wi-night-alt-snow-thunderstorm:before {
  content: "\f06d";
}
.wi-night-alt-snow-wind:before {
  content: "\f067";
}
.wi-night-alt-sprinkle:before {
  content: "\f02b";
}
.wi-night-alt-storm-showers:before {
  content: "\f02c";
}
.wi-night-alt-thunderstorm:before {
  content: "\f02d";
}
.wi-night-cloudy:before {
  content: "\f031";
}
.wi-night-cloudy-gusts:before {
  content: "\f02f";
}
.wi-night-cloudy-windy:before {
  content: "\f030";
}
.wi-night-fog:before {
  content: "\f04a";
}
.wi-night-hail:before {
  content: "\f032";
}
.wi-night-lightning:before {
  content: "\f033";
}
.wi-night-partly-cloudy:before {
  content: "\f083";
}
.wi-night-rain:before {
  content: "\f036";
}
.wi-night-rain-mix:before {
  content: "\f034";
}
.wi-night-rain-wind:before {
  content: "\f035";
}
.wi-night-showers:before {
  content: "\f037";
}
.wi-night-sleet:before {
  content: "\f0b3";
}
.wi-night-sleet-storm:before {
  content: "\f069";
}
.wi-night-snow:before {
  content: "\f038";
}
.wi-night-snow-thunderstorm:before {
  content: "\f06c";
}
.wi-night-snow-wind:before {
  content: "\f066";
}
.wi-night-sprinkle:before {
  content: "\f039";
}
.wi-night-storm-showers:before {
  content: "\f03a";
}
.wi-night-thunderstorm:before {
  content: "\f03b";
}
.wi-lunar-eclipse:before {
  content: "\f070";
}
.wi-stars:before {
  content: "\f077";
}
.wi-storm-showers:before {
  content: "\f01d";
}
.wi-thunderstorm:before {
  content: "\f01e";
}
.wi-night-alt-cloudy-high:before {
  content: "\f07e";
}
.wi-night-cloudy-high:before {
  content: "\f080";
}
.wi-night-alt-partly-cloudy:before {
  content: "\f081";
}
.wi-cloud:before {
  content: "\f041";
}
.wi-cloudy:before {
  content: "\f013";
}
.wi-cloudy-gusts:before {
  content: "\f011";
}
.wi-cloudy-windy:before {
  content: "\f012";
}
.wi-fog:before {
  content: "\f014";
}
.wi-hail:before {
  content: "\f015";
}
.wi-rain:before {
  content: "\f019";
}
.wi-rain-mix:before {
  content: "\f017";
}
.wi-rain-wind:before {
  content: "\f018";
}
.wi-showers:before {
  content: "\f01a";
}
.wi-sleet:before {
  content: "\f0b5";
}
.wi-snow:before {
  content: "\f01b";
}
.wi-sprinkle:before {
  content: "\f01c";
}
.wi-storm-showers:before {
  content: "\f01d";
}
.wi-thunderstorm:before {
  content: "\f01e";
}
.wi-snow-wind:before {
  content: "\f064";
}
.wi-snow:before {
  content: "\f01b";
}
.wi-smog:before {
  content: "\f074";
}
.wi-smoke:before {
  content: "\f062";
}
.wi-lightning:before {
  content: "\f016";
}
.wi-raindrops:before {
  content: "\f04e";
}
.wi-raindrop:before {
  content: "\f078";
}
.wi-dust:before {
  content: "\f063";
}
.wi-snowflake-cold:before {
  content: "\f076";
}
.wi-windy:before {
  content: "\f021";
}
.wi-strong-wind:before {
  content: "\f050";
}
.wi-sandstorm:before {
  content: "\f082";
}
.wi-earthquake:before {
  content: "\f0c6";
}
.wi-fire:before {
  content: "\f0c7";
}
.wi-flood:before {
  content: "\f07c";
}
.wi-meteor:before {
  content: "\f071";
}
.wi-tsunami:before {
  content: "\f0c5";
}
.wi-volcano:before {
  content: "\f0c8";
}
.wi-hurricane:before {
  content: "\f073";
}
.wi-tornado:before {
  content: "\f056";
}
.wi-small-craft-advisory:before {
  content: "\f0cc";
}
.wi-gale-warning:before {
  content: "\f0cd";
}
.wi-storm-warning:before {
  content: "\f0ce";
}
.wi-hurricane-warning:before {
  content: "\f0cf";
}
.wi-wind-direction:before {
  content: "\f0b1";
}
.wi-alien:before {
  content: "\f075";
}
.wi-celsius:before {
  content: "\f03c";
}
.wi-fahrenheit:before {
  content: "\f045";
}
.wi-degrees:before {
  content: "\f042";
}
.wi-thermometer:before {
  content: "\f055";
}
.wi-thermometer-exterior:before {
  content: "\f053";
}
.wi-thermometer-internal:before {
  content: "\f054";
}
.wi-cloud-down:before {
  content: "\f03d";
}
.wi-cloud-up:before {
  content: "\f040";
}
.wi-cloud-refresh:before {
  content: "\f03e";
}
.wi-horizon:before {
  content: "\f047";
}
.wi-horizon-alt:before {
  content: "\f046";
}
.wi-sunrise:before {
  content: "\f051";
}
.wi-sunset:before {
  content: "\f052";
}
.wi-moonrise:before {
  content: "\f0c9";
}
.wi-moonset:before {
  content: "\f0ca";
}
.wi-refresh:before {
  content: "\f04c";
}
.wi-refresh-alt:before {
  content: "\f04b";
}
.wi-umbrella:before {
  content: "\f084";
}
.wi-barometer:before {
  content: "\f079";
}
.wi-humidity:before {
  content: "\f07a";
}
.wi-na:before {
  content: "\f07b";
}
.wi-train:before {
  content: "\f0cb";
}
.wi-moon-new:before {
  content: "\f095";
}
.wi-moon-waxing-crescent-1:before {
  content: "\f096";
}
.wi-moon-waxing-crescent-2:before {
  content: "\f097";
}
.wi-moon-waxing-crescent-3:before {
  content: "\f098";
}
.wi-moon-waxing-crescent-4:before {
  content: "\f099";
}
.wi-moon-waxing-crescent-5:before {
  content: "\f09a";
}
.wi-moon-waxing-crescent-6:before {
  content: "\f09b";
}
.wi-moon-first-quarter:before {
  content: "\f09c";
}
.wi-moon-waxing-gibbous-1:before {
  content: "\f09d";
}
.wi-moon-waxing-gibbous-2:before {
  content: "\f09e";
}
.wi-moon-waxing-gibbous-3:before {
  content: "\f09f";
}
.wi-moon-waxing-gibbous-4:before {
  content: "\f0a0";
}
.wi-moon-waxing-gibbous-5:before {
  content: "\f0a1";
}
.wi-moon-waxing-gibbous-6:before {
  content: "\f0a2";
}
.wi-moon-full:before {
  content: "\f0a3";
}
.wi-moon-waning-gibbous-1:before {
  content: "\f0a4";
}
.wi-moon-waning-gibbous-2:before {
  content: "\f0a5";
}
.wi-moon-waning-gibbous-3:before {
  content: "\f0a6";
}
.wi-moon-waning-gibbous-4:before {
  content: "\f0a7";
}
.wi-moon-waning-gibbous-5:before {
  content: "\f0a8";
}
.wi-moon-waning-gibbous-6:before {
  content: "\f0a9";
}
.wi-moon-third-quarter:before {
  content: "\f0aa";
}
.wi-moon-waning-crescent-1:before {
  content: "\f0ab";
}
.wi-moon-waning-crescent-2:before {
  content: "\f0ac";
}
.wi-moon-waning-crescent-3:before {
  content: "\f0ad";
}
.wi-moon-waning-crescent-4:before {
  content: "\f0ae";
}
.wi-moon-waning-crescent-5:before {
  content: "\f0af";
}
.wi-moon-waning-crescent-6:before {
  content: "\f0b0";
}
.wi-moon-alt-new:before {
  content: "\f0eb";
}
.wi-moon-alt-waxing-crescent-1:before {
  content: "\f0d0";
}
.wi-moon-alt-waxing-crescent-2:before {
  content: "\f0d1";
}
.wi-moon-alt-waxing-crescent-3:before {
  content: "\f0d2";
}
.wi-moon-alt-waxing-crescent-4:before {
  content: "\f0d3";
}
.wi-moon-alt-waxing-crescent-5:before {
  content: "\f0d4";
}
.wi-moon-alt-waxing-crescent-6:before {
  content: "\f0d5";
}
.wi-moon-alt-first-quarter:before {
  content: "\f0d6";
}
.wi-moon-alt-waxing-gibbous-1:before {
  content: "\f0d7";
}
.wi-moon-alt-waxing-gibbous-2:before {
  content: "\f0d8";
}
.wi-moon-alt-waxing-gibbous-3:before {
  content: "\f0d9";
}
.wi-moon-alt-waxing-gibbous-4:before {
  content: "\f0da";
}
.wi-moon-alt-waxing-gibbous-5:before {
  content: "\f0db";
}
.wi-moon-alt-waxing-gibbous-6:before {
  content: "\f0dc";
}
.wi-moon-alt-full:before {
  content: "\f0dd";
}
.wi-moon-alt-waning-gibbous-1:before {
  content: "\f0de";
}
.wi-moon-alt-waning-gibbous-2:before {
  content: "\f0df";
}
.wi-moon-alt-waning-gibbous-3:before {
  content: "\f0e0";
}
.wi-moon-alt-waning-gibbous-4:before {
  content: "\f0e1";
}
.wi-moon-alt-waning-gibbous-5:before {
  content: "\f0e2";
}
.wi-moon-alt-waning-gibbous-6:before {
  content: "\f0e3";
}
.wi-moon-alt-third-quarter:before {
  content: "\f0e4";
}
.wi-moon-alt-waning-crescent-1:before {
  content: "\f0e5";
}
.wi-moon-alt-waning-crescent-2:before {
  content: "\f0e6";
}
.wi-moon-alt-waning-crescent-3:before {
  content: "\f0e7";
}
.wi-moon-alt-waning-crescent-4:before {
  content: "\f0e8";
}
.wi-moon-alt-waning-crescent-5:before {
  content: "\f0e9";
}
.wi-moon-alt-waning-crescent-6:before {
  content: "\f0ea";
}
.wi-moon-0:before {
  content: "\f095";
}
.wi-moon-1:before {
  content: "\f096";
}
.wi-moon-2:before {
  content: "\f097";
}
.wi-moon-3:before {
  content: "\f098";
}
.wi-moon-4:before {
  content: "\f099";
}
.wi-moon-5:before {
  content: "\f09a";
}
.wi-moon-6:before {
  content: "\f09b";
}
.wi-moon-7:before {
  content: "\f09c";
}
.wi-moon-8:before {
  content: "\f09d";
}
.wi-moon-9:before {
  content: "\f09e";
}
.wi-moon-10:before {
  content: "\f09f";
}
.wi-moon-11:before {
  content: "\f0a0";
}
.wi-moon-12:before {
  content: "\f0a1";
}
.wi-moon-13:before {
  content: "\f0a2";
}
.wi-moon-14:before {
  content: "\f0a3";
}
.wi-moon-15:before {
  content: "\f0a4";
}
.wi-moon-16:before {
  content: "\f0a5";
}
.wi-moon-17:before {
  content: "\f0a6";
}
.wi-moon-18:before {
  content: "\f0a7";
}
.wi-moon-19:before {
  content: "\f0a8";
}
.wi-moon-20:before {
  content: "\f0a9";
}
.wi-moon-21:before {
  content: "\f0aa";
}
.wi-moon-22:before {
  content: "\f0ab";
}
.wi-moon-23:before {
  content: "\f0ac";
}
.wi-moon-24:before {
  content: "\f0ad";
}
.wi-moon-25:before {
  content: "\f0ae";
}
.wi-moon-26:before {
  content: "\f0af";
}
.wi-moon-27:before {
  content: "\f0b0";
}
.wi-time-1:before {
  content: "\f08a";
}
.wi-time-2:before {
  content: "\f08b";
}
.wi-time-3:before {
  content: "\f08c";
}
.wi-time-4:before {
  content: "\f08d";
}
.wi-time-5:before {
  content: "\f08e";
}
.wi-time-6:before {
  content: "\f08f";
}
.wi-time-7:before {
  content: "\f090";
}
.wi-time-8:before {
  content: "\f091";
}
.wi-time-9:before {
  content: "\f092";
}
.wi-time-10:before {
  content: "\f093";
}
.wi-time-11:before {
  content: "\f094";
}
.wi-time-12:before {
  content: "\f089";
}
.wi-direction-up:before {
  content: "\f058";
}
.wi-direction-up-right:before {
  content: "\f057";
}
.wi-direction-right:before {
  content: "\f04d";
}
.wi-direction-down-right:before {
  content: "\f088";
}
.wi-direction-down:before {
  content: "\f044";
}
.wi-direction-down-left:before {
  content: "\f043";
}
.wi-direction-left:before {
  content: "\f048";
}
.wi-direction-up-left:before {
  content: "\f087";
}
.wi-wind-beaufort-0:before {
  content: "\f0b7";
}
.wi-wind-beaufort-1:before {
  content: "\f0b8";
}
.wi-wind-beaufort-2:before {
  content: "\f0b9";
}
.wi-wind-beaufort-3:before {
  content: "\f0ba";
}
.wi-wind-beaufort-4:before {
  content: "\f0bb";
}
.wi-wind-beaufort-5:before {
  content: "\f0bc";
}
.wi-wind-beaufort-6:before {
  content: "\f0bd";
}
.wi-wind-beaufort-7:before {
  content: "\f0be";
}
.wi-wind-beaufort-8:before {
  content: "\f0bf";
}
.wi-wind-beaufort-9:before {
  content: "\f0c0";
}
.wi-wind-beaufort-10:before {
  content: "\f0c1";
}
.wi-wind-beaufort-11:before {
  content: "\f0c2";
}
.wi-wind-beaufort-12:before {
  content: "\f0c3";
}
.wi-yahoo-0:before {
  content: "\f056";
}
.wi-yahoo-1:before {
  content: "\f00e";
}
.wi-yahoo-2:before {
  content: "\f073";
}
.wi-yahoo-3:before {
  content: "\f01e";
}
.wi-yahoo-4:before {
  content: "\f01e";
}
.wi-yahoo-5:before {
  content: "\f017";
}
.wi-yahoo-6:before {
  content: "\f017";
}
.wi-yahoo-7:before {
  content: "\f017";
}
.wi-yahoo-8:before {
  content: "\f015";
}
.wi-yahoo-9:before {
  content: "\f01a";
}
.wi-yahoo-10:before {
  content: "\f015";
}
.wi-yahoo-11:before {
  content: "\f01a";
}
.wi-yahoo-12:before {
  content: "\f01a";
}
.wi-yahoo-13:before {
  content: "\f01b";
}
.wi-yahoo-14:before {
  content: "\f00a";
}
.wi-yahoo-15:before {
  content: "\f064";
}
.wi-yahoo-16:before {
  content: "\f01b";
}
.wi-yahoo-17:before {
  content: "\f015";
}
.wi-yahoo-18:before {
  content: "\f017";
}
.wi-yahoo-19:before {
  content: "\f063";
}
.wi-yahoo-20:before {
  content: "\f014";
}
.wi-yahoo-21:before {
  content: "\f021";
}
.wi-yahoo-22:before {
  content: "\f062";
}
.wi-yahoo-23:before {
  content: "\f050";
}
.wi-yahoo-24:before {
  content: "\f050";
}
.wi-yahoo-25:before {
  content: "\f076";
}
.wi-yahoo-26:before {
  content: "\f013";
}
.wi-yahoo-27:before {
  content: "\f031";
}
.wi-yahoo-28:before {
  content: "\f002";
}
.wi-yahoo-29:before {
  content: "\f031";
}
.wi-yahoo-30:before {
  content: "\f002";
}
.wi-yahoo-31:before {
  content: "\f02e";
}
.wi-yahoo-32:before {
  content: "\f00d";
}
.wi-yahoo-33:before {
  content: "\f083";
}
.wi-yahoo-34:before {
  content: "\f00c";
}
.wi-yahoo-35:before {
  content: "\f017";
}
.wi-yahoo-36:before {
  content: "\f072";
}
.wi-yahoo-37:before {
  content: "\f00e";
}
.wi-yahoo-38:before {
  content: "\f00e";
}
.wi-yahoo-39:before {
  content: "\f00e";
}
.wi-yahoo-40:before {
  content: "\f01a";
}
.wi-yahoo-41:before {
  content: "\f064";
}
.wi-yahoo-42:before {
  content: "\f01b";
}
.wi-yahoo-43:before {
  content: "\f064";
}
.wi-yahoo-44:before {
  content: "\f00c";
}
.wi-yahoo-45:before {
  content: "\f00e";
}
.wi-yahoo-46:before {
  content: "\f01b";
}
.wi-yahoo-47:before {
  content: "\f00e";
}
.wi-yahoo-3200:before {
  content: "\f077";
}
.wi-forecast-io-clear-day:before {
  content: "\f00d";
}
.wi-forecast-io-clear-night:before {
  content: "\f02e";
}
.wi-forecast-io-rain:before {
  content: "\f019";
}
.wi-forecast-io-snow:before {
  content: "\f01b";
}
.wi-forecast-io-sleet:before {
  content: "\f0b5";
}
.wi-forecast-io-wind:before {
  content: "\f050";
}
.wi-forecast-io-fog:before {
  content: "\f014";
}
.wi-forecast-io-cloudy:before {
  content: "\f013";
}
.wi-forecast-io-partly-cloudy-day:before {
  content: "\f002";
}
.wi-forecast-io-partly-cloudy-night:before {
  content: "\f031";
}
.wi-forecast-io-hail:before {
  content: "\f015";
}
.wi-forecast-io-thunderstorm:before {
  content: "\f01e";
}
.wi-forecast-io-tornado:before {
  content: "\f056";
}
.wi-wmo4680-0:before,
.wi-wmo4680-00:before {
  content: "\f055";
}
.wi-wmo4680-1:before,
.wi-wmo4680-01:before {
  content: "\f013";
}
.wi-wmo4680-2:before,
.wi-wmo4680-02:before {
  content: "\f055";
}
.wi-wmo4680-3:before,
.wi-wmo4680-03:before {
  content: "\f013";
}
.wi-wmo4680-4:before,
.wi-wmo4680-04:before {
  content: "\f014";
}
.wi-wmo4680-5:before,
.wi-wmo4680-05:before {
  content: "\f014";
}
.wi-wmo4680-10:before {
  content: "\f014";
}
.wi-wmo4680-11:before {
  content: "\f014";
}
.wi-wmo4680-12:before {
  content: "\f016";
}
.wi-wmo4680-18:before {
  content: "\f050";
}
.wi-wmo4680-20:before {
  content: "\f014";
}
.wi-wmo4680-21:before {
  content: "\f017";
}
.wi-wmo4680-22:before {
  content: "\f017";
}
.wi-wmo4680-23:before {
  content: "\f019";
}
.wi-wmo4680-24:before {
  content: "\f01b";
}
.wi-wmo4680-25:before {
  content: "\f015";
}
.wi-wmo4680-26:before {
  content: "\f01e";
}
.wi-wmo4680-27:before {
  content: "\f063";
}
.wi-wmo4680-28:before {
  content: "\f063";
}
.wi-wmo4680-29:before {
  content: "\f063";
}
.wi-wmo4680-30:before {
  content: "\f014";
}
.wi-wmo4680-31:before {
  content: "\f014";
}
.wi-wmo4680-32:before {
  content: "\f014";
}
.wi-wmo4680-33:before {
  content: "\f014";
}
.wi-wmo4680-34:before {
  content: "\f014";
}
.wi-wmo4680-35:before {
  content: "\f014";
}
.wi-wmo4680-40:before {
  content: "\f017";
}
.wi-wmo4680-41:before {
  content: "\f01c";
}
.wi-wmo4680-42:before {
  content: "\f019";
}
.wi-wmo4680-43:before {
  content: "\f01c";
}
.wi-wmo4680-44:before {
  content: "\f019";
}
.wi-wmo4680-45:before {
  content: "\f015";
}
.wi-wmo4680-46:before {
  content: "\f015";
}
.wi-wmo4680-47:before {
  content: "\f01b";
}
.wi-wmo4680-48:before {
  content: "\f01b";
}
.wi-wmo4680-50:before {
  content: "\f01c";
}
.wi-wmo4680-51:before {
  content: "\f01c";
}
.wi-wmo4680-52:before {
  content: "\f019";
}
.wi-wmo4680-53:before {
  content: "\f019";
}
.wi-wmo4680-54:before {
  content: "\f076";
}
.wi-wmo4680-55:before {
  content: "\f076";
}
.wi-wmo4680-56:before {
  content: "\f076";
}
.wi-wmo4680-57:before {
  content: "\f01c";
}
.wi-wmo4680-58:before {
  content: "\f019";
}
.wi-wmo4680-60:before {
  content: "\f01c";
}
.wi-wmo4680-61:before {
  content: "\f01c";
}
.wi-wmo4680-62:before {
  content: "\f019";
}
.wi-wmo4680-63:before {
  content: "\f019";
}
.wi-wmo4680-64:before {
  content: "\f015";
}
.wi-wmo4680-65:before {
  content: "\f015";
}
.wi-wmo4680-66:before {
  content: "\f015";
}
.wi-wmo4680-67:before {
  content: "\f017";
}
.wi-wmo4680-68:before {
  content: "\f017";
}
.wi-wmo4680-70:before {
  content: "\f01b";
}
.wi-wmo4680-71:before {
  content: "\f01b";
}
.wi-wmo4680-72:before {
  content: "\f01b";
}
.wi-wmo4680-73:before {
  content: "\f01b";
}
.wi-wmo4680-74:before {
  content: "\f076";
}
.wi-wmo4680-75:before {
  content: "\f076";
}
.wi-wmo4680-76:before {
  content: "\f076";
}
.wi-wmo4680-77:before {
  content: "\f01b";
}
.wi-wmo4680-78:before {
  content: "\f076";
}
.wi-wmo4680-80:before {
  content: "\f019";
}
.wi-wmo4680-81:before {
  content: "\f01c";
}
.wi-wmo4680-82:before {
  content: "\f019";
}
.wi-wmo4680-83:before {
  content: "\f019";
}
.wi-wmo4680-84:before {
  content: "\f01d";
}
.wi-wmo4680-85:before {
  content: "\f017";
}
.wi-wmo4680-86:before {
  content: "\f017";
}
.wi-wmo4680-87:before {
  content: "\f017";
}
.wi-wmo4680-89:before {
  content: "\f015";
}
.wi-wmo4680-90:before {
  content: "\f016";
}
.wi-wmo4680-91:before {
  content: "\f01d";
}
.wi-wmo4680-92:before {
  content: "\f01e";
}
.wi-wmo4680-93:before {
  content: "\f01e";
}
.wi-wmo4680-94:before {
  content: "\f016";
}
.wi-wmo4680-95:before {
  content: "\f01e";
}
.wi-wmo4680-96:before {
  content: "\f01e";
}
.wi-wmo4680-99:before {
  content: "\f056";
}
.wi-owm-200:before {
  content: "\f01e";
}
.wi-owm-201:before {
  content: "\f01e";
}
.wi-owm-202:before {
  content: "\f01e";
}
.wi-owm-210:before {
  content: "\f016";
}
.wi-owm-211:before {
  content: "\f016";
}
.wi-owm-212:before {
  content: "\f016";
}
.wi-owm-221:before {
  content: "\f016";
}
.wi-owm-230:before {
  content: "\f01e";
}
.wi-owm-231:before {
  content: "\f01e";
}
.wi-owm-232:before {
  content: "\f01e";
}
.wi-owm-300:before {
  content: "\f01c";
}
.wi-owm-301:before {
  content: "\f01c";
}
.wi-owm-302:before {
  content: "\f019";
}
.wi-owm-310:before {
  content: "\f017";
}
.wi-owm-311:before {
  content: "\f019";
}
.wi-owm-312:before {
  content: "\f019";
}
.wi-owm-313:before {
  content: "\f01a";
}
.wi-owm-314:before {
  content: "\f019";
}
.wi-owm-321:before {
  content: "\f01c";
}
.wi-owm-500:before {
  content: "\f01c";
}
.wi-owm-501:before {
  content: "\f019";
}
.wi-owm-502:before {
  content: "\f019";
}
.wi-owm-503:before {
  content: "\f019";
}
.wi-owm-504:before {
  content: "\f019";
}
.wi-owm-511:before {
  content: "\f017";
}
.wi-owm-520:before {
  content: "\f01a";
}
.wi-owm-521:before {
  content: "\f01a";
}
.wi-owm-522:before {
  content: "\f01a";
}
.wi-owm-531:before {
  content: "\f01d";
}
.wi-owm-600:before {
  content: "\f01b";
}
.wi-owm-601:before {
  content: "\f01b";
}
.wi-owm-602:before {
  content: "\f0b5";
}
.wi-owm-611:before {
  content: "\f017";
}
.wi-owm-612:before {
  content: "\f017";
}
.wi-owm-615:before {
  content: "\f017";
}
.wi-owm-616:before {
  content: "\f017";
}
.wi-owm-620:before {
  content: "\f017";
}
.wi-owm-621:before {
  content: "\f01b";
}
.wi-owm-622:before {
  content: "\f01b";
}
.wi-owm-701:before {
  content: "\f01a";
}
.wi-owm-711:before {
  content: "\f062";
}
.wi-owm-721:before {
  content: "\f0b6";
}
.wi-owm-731:before {
  content: "\f063";
}
.wi-owm-741:before {
  content: "\f014";
}
.wi-owm-761:before {
  content: "\f063";
}
.wi-owm-762:before {
  content: "\f063";
}
.wi-owm-771:before {
  content: "\f011";
}
.wi-owm-781:before {
  content: "\f056";
}
.wi-owm-800:before {
  content: "\f00d";
}
.wi-owm-801:before {
  content: "\f011";
}
.wi-owm-802:before {
  content: "\f011";
}
.wi-owm-803:before {
  content: "\f012";
}
.wi-owm-804:before {
  content: "\f013";
}
.wi-owm-900:before {
  content: "\f056";
}
.wi-owm-901:before {
  content: "\f01d";
}
.wi-owm-902:before {
  content: "\f073";
}
.wi-owm-903:before {
  content: "\f076";
}
.wi-owm-904:before {
  content: "\f072";
}
.wi-owm-905:before {
  content: "\f021";
}
.wi-owm-906:before {
  content: "\f015";
}
.wi-owm-957:before {
  content: "\f050";
}
.wi-owm-day-200:before {
  content: "\f010";
}
.wi-owm-day-201:before {
  content: "\f010";
}
.wi-owm-day-202:before {
  content: "\f010";
}
.wi-owm-day-210:before {
  content: "\f005";
}
.wi-owm-day-211:before {
  content: "\f005";
}
.wi-owm-day-212:before {
  content: "\f005";
}
.wi-owm-day-221:before {
  content: "\f005";
}
.wi-owm-day-230:before {
  content: "\f010";
}
.wi-owm-day-231:before {
  content: "\f010";
}
.wi-owm-day-232:before {
  content: "\f010";
}
.wi-owm-day-300:before {
  content: "\f00b";
}
.wi-owm-day-301:before {
  content: "\f00b";
}
.wi-owm-day-302:before {
  content: "\f008";
}
.wi-owm-day-310:before {
  content: "\f008";
}
.wi-owm-day-311:before {
  content: "\f008";
}
.wi-owm-day-312:before {
  content: "\f008";
}
.wi-owm-day-313:before {
  content: "\f008";
}
.wi-owm-day-314:before {
  content: "\f008";
}
.wi-owm-day-321:before {
  content: "\f00b";
}
.wi-owm-day-500:before {
  content: "\f00b";
}
.wi-owm-day-501:before {
  content: "\f008";
}
.wi-owm-day-502:before {
  content: "\f008";
}
.wi-owm-day-503:before {
  content: "\f008";
}
.wi-owm-day-504:before {
  content: "\f008";
}
.wi-owm-day-511:before {
  content: "\f006";
}
.wi-owm-day-520:before {
  content: "\f009";
}
.wi-owm-day-521:before {
  content: "\f009";
}
.wi-owm-day-522:before {
  content: "\f009";
}
.wi-owm-day-531:before {
  content: "\f00e";
}
.wi-owm-day-600:before {
  content: "\f00a";
}
.wi-owm-day-601:before {
  content: "\f0b2";
}
.wi-owm-day-602:before {
  content: "\f00a";
}
.wi-owm-day-611:before {
  content: "\f006";
}
.wi-owm-day-612:before {
  content: "\f006";
}
.wi-owm-day-615:before {
  content: "\f006";
}
.wi-owm-day-616:before {
  content: "\f006";
}
.wi-owm-day-620:before {
  content: "\f006";
}
.wi-owm-day-621:before {
  content: "\f00a";
}
.wi-owm-day-622:before {
  content: "\f00a";
}
.wi-owm-day-701:before {
  content: "\f009";
}
.wi-owm-day-711:before {
  content: "\f062";
}
.wi-owm-day-721:before {
  content: "\f0b6";
}
.wi-owm-day-731:before {
  content: "\f063";
}
.wi-owm-day-741:before {
  content: "\f003";
}
.wi-owm-day-761:before {
  content: "\f063";
}
.wi-owm-day-762:before {
  content: "\f063";
}
.wi-owm-day-781:before {
  content: "\f056";
}
.wi-owm-day-800:before {
  content: "\f00d";
}
.wi-owm-day-801:before {
  content: "\f000";
}
.wi-owm-day-802:before {
  content: "\f000";
}
.wi-owm-day-803:before {
  content: "\f000";
}
.wi-owm-day-804:before {
  content: "\f00c";
}
.wi-owm-day-900:before {
  content: "\f056";
}
.wi-owm-day-902:before {
  content: "\f073";
}
.wi-owm-day-903:before {
  content: "\f076";
}
.wi-owm-day-904:before {
  content: "\f072";
}
.wi-owm-day-906:before {
  content: "\f004";
}
.wi-owm-day-957:before {
  content: "\f050";
}
.wi-owm-night-200:before {
  content: "\f02d";
}
.wi-owm-night-201:before {
  content: "\f02d";
}
.wi-owm-night-202:before {
  content: "\f02d";
}
.wi-owm-night-210:before {
  content: "\f025";
}
.wi-owm-night-211:before {
  content: "\f025";
}
.wi-owm-night-212:before {
  content: "\f025";
}
.wi-owm-night-221:before {
  content: "\f025";
}
.wi-owm-night-230:before {
  content: "\f02d";
}
.wi-owm-night-231:before {
  content: "\f02d";
}
.wi-owm-night-232:before {
  content: "\f02d";
}
.wi-owm-night-300:before {
  content: "\f02b";
}
.wi-owm-night-301:before {
  content: "\f02b";
}
.wi-owm-night-302:before {
  content: "\f028";
}
.wi-owm-night-310:before {
  content: "\f028";
}
.wi-owm-night-311:before {
  content: "\f028";
}
.wi-owm-night-312:before {
  content: "\f028";
}
.wi-owm-night-313:before {
  content: "\f028";
}
.wi-owm-night-314:before {
  content: "\f028";
}
.wi-owm-night-321:before {
  content: "\f02b";
}
.wi-owm-night-500:before {
  content: "\f02b";
}
.wi-owm-night-501:before {
  content: "\f028";
}
.wi-owm-night-502:before {
  content: "\f028";
}
.wi-owm-night-503:before {
  content: "\f028";
}
.wi-owm-night-504:before {
  content: "\f028";
}
.wi-owm-night-511:before {
  content: "\f026";
}
.wi-owm-night-520:before {
  content: "\f029";
}
.wi-owm-night-521:before {
  content: "\f029";
}
.wi-owm-night-522:before {
  content: "\f029";
}
.wi-owm-night-531:before {
  content: "\f02c";
}
.wi-owm-night-600:before {
  content: "\f02a";
}
.wi-owm-night-601:before {
  content: "\f0b4";
}
.wi-owm-night-602:before {
  content: "\f02a";
}
.wi-owm-night-611:before {
  content: "\f026";
}
.wi-owm-night-612:before {
  content: "\f026";
}
.wi-owm-night-615:before {
  content: "\f026";
}
.wi-owm-night-616:before {
  content: "\f026";
}
.wi-owm-night-620:before {
  content: "\f026";
}
.wi-owm-night-621:before {
  content: "\f02a";
}
.wi-owm-night-622:before {
  content: "\f02a";
}
.wi-owm-night-701:before {
  content: "\f029";
}
.wi-owm-night-711:before {
  content: "\f062";
}
.wi-owm-night-721:before {
  content: "\f0b6";
}
.wi-owm-night-731:before {
  content: "\f063";
}
.wi-owm-night-741:before {
  content: "\f04a";
}
.wi-owm-night-761:before {
  content: "\f063";
}
.wi-owm-night-762:before {
  content: "\f063";
}
.wi-owm-night-781:before {
  content: "\f056";
}
.wi-owm-night-800:before {
  content: "\f02e";
}
.wi-owm-night-801:before {
  content: "\f022";
}
.wi-owm-night-802:before {
  content: "\f022";
}
.wi-owm-night-803:before {
  content: "\f022";
}
.wi-owm-night-804:before {
  content: "\f086";
}
.wi-owm-night-900:before {
  content: "\f056";
}
.wi-owm-night-902:before {
  content: "\f073";
}
.wi-owm-night-903:before {
  content: "\f076";
}
.wi-owm-night-904:before {
  content: "\f072";
}
.wi-owm-night-906:before {
  content: "\f024";
}
.wi-owm-night-957:before {
  content: "\f050";
}
.wi-wu-chanceflurries:before {
  content: "\f064";
}
.wi-wu-chancerain:before {
  content: "\f019";
}
.wi-wu-chancesleat:before {
  content: "\f0b5";
}
.wi-wu-chancesnow:before {
  content: "\f01b";
}
.wi-wu-chancetstorms:before {
  content: "\f01e";
}
.wi-wu-clear:before {
  content: "\f00d";
}
.wi-wu-cloudy:before {
  content: "\f002";
}
.wi-wu-flurries:before {
  content: "\f064";
}
.wi-wu-hazy:before {
  content: "\f0b6";
}
.wi-wu-mostlycloudy:before {
  content: "\f002";
}
.wi-wu-mostlysunny:before {
  content: "\f00d";
}
.wi-wu-partlycloudy:before {
  content: "\f002";
}
.wi-wu-partlysunny:before {
  content: "\f00d";
}
.wi-wu-rain:before {
  content: "\f01a";
}
.wi-wu-sleat:before {
  content: "\f0b5";
}
.wi-wu-snow:before {
  content: "\f01b";
}
.wi-wu-sunny:before {
  content: "\f00d";
}
.wi-wu-tstorms:before {
  content: "\f01e";
}
.wi-wu-unknown:before {
  content: "\f00d";
}


/* Flag Icons */


.flag-icon-background {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
}
.flag-icon {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  position: relative;
  display: inline-block;
  width: 1.33333333em;
  line-height: 1em;
}
.flag-icon:before {
  content: "\00a0";
}
.flag-icon.flag-icon-squared {
  width: 1em;
}
.flag-icon-ad {
  background-image: url(../flags/4x3/ad.svg);
}
.flag-icon-ad.flag-icon-squared {
  background-image: url(../flags/1x1/ad.svg);
}
.flag-icon-ae {
  background-image: url(../flags/4x3/ae.svg);
}
.flag-icon-ae.flag-icon-squared {
  background-image: url(../flags/1x1/ae.svg);
}
.flag-icon-af {
  background-image: url(../flags/4x3/af.svg);
}
.flag-icon-af.flag-icon-squared {
  background-image: url(../flags/1x1/af.svg);
}
.flag-icon-ag {
  background-image: url(../flags/4x3/ag.svg);
}
.flag-icon-ag.flag-icon-squared {
  background-image: url(../flags/1x1/ag.svg);
}
.flag-icon-ai {
  background-image: url(../flags/4x3/ai.svg);
}
.flag-icon-ai.flag-icon-squared {
  background-image: url(../flags/1x1/ai.svg);
}
.flag-icon-al {
  background-image: url(../flags/4x3/al.svg);
}
.flag-icon-al.flag-icon-squared {
  background-image: url(../flags/1x1/al.svg);
}
.flag-icon-am {
  background-image: url(../flags/4x3/am.svg);
}
.flag-icon-am.flag-icon-squared {
  background-image: url(../flags/1x1/am.svg);
}
.flag-icon-ao {
  background-image: url(../flags/4x3/ao.svg);
}
.flag-icon-ao.flag-icon-squared {
  background-image: url(../flags/1x1/ao.svg);
}
.flag-icon-aq {
  background-image: url(../flags/4x3/aq.svg);
}
.flag-icon-aq.flag-icon-squared {
  background-image: url(../flags/1x1/aq.svg);
}
.flag-icon-ar {
  background-image: url(../flags/4x3/ar.svg);
}
.flag-icon-ar.flag-icon-squared {
  background-image: url(../flags/1x1/ar.svg);
}
.flag-icon-as {
  background-image: url(../flags/4x3/as.svg);
}
.flag-icon-as.flag-icon-squared {
  background-image: url(../flags/1x1/as.svg);
}
.flag-icon-at {
  background-image: url(../flags/4x3/at.svg);
}
.flag-icon-at.flag-icon-squared {
  background-image: url(../flags/1x1/at.svg);
}
.flag-icon-au {
  background-image: url(../flags/4x3/au.svg);
}
.flag-icon-au.flag-icon-squared {
  background-image: url(../flags/1x1/au.svg);
}
.flag-icon-aw {
  background-image: url(../flags/4x3/aw.svg);
}
.flag-icon-aw.flag-icon-squared {
  background-image: url(../flags/1x1/aw.svg);
}
.flag-icon-ax {
  background-image: url(../flags/4x3/ax.svg);
}
.flag-icon-ax.flag-icon-squared {
  background-image: url(../flags/1x1/ax.svg);
}
.flag-icon-az {
  background-image: url(../flags/4x3/az.svg);
}
.flag-icon-az.flag-icon-squared {
  background-image: url(../flags/1x1/az.svg);
}
.flag-icon-ba {
  background-image: url(../flags/4x3/ba.svg);
}
.flag-icon-ba.flag-icon-squared {
  background-image: url(../flags/1x1/ba.svg);
}
.flag-icon-bb {
  background-image: url(../flags/4x3/bb.svg);
}
.flag-icon-bb.flag-icon-squared {
  background-image: url(../flags/1x1/bb.svg);
}
.flag-icon-bd {
  background-image: url(../flags/4x3/bd.svg);
}
.flag-icon-bd.flag-icon-squared {
  background-image: url(../flags/1x1/bd.svg);
}
.flag-icon-be {
  background-image: url(../flags/4x3/be.svg);
}
.flag-icon-be.flag-icon-squared {
  background-image: url(../flags/1x1/be.svg);
}
.flag-icon-bf {
  background-image: url(../flags/4x3/bf.svg);
}
.flag-icon-bf.flag-icon-squared {
  background-image: url(../flags/1x1/bf.svg);
}
.flag-icon-bg {
  background-image: url(../flags/4x3/bg.svg);
}
.flag-icon-bg.flag-icon-squared {
  background-image: url(../flags/1x1/bg.svg);
}
.flag-icon-bh {
  background-image: url(../flags/4x3/bh.svg);
}
.flag-icon-bh.flag-icon-squared {
  background-image: url(../flags/1x1/bh.svg);
}
.flag-icon-bi {
  background-image: url(../flags/4x3/bi.svg);
}
.flag-icon-bi.flag-icon-squared {
  background-image: url(../flags/1x1/bi.svg);
}
.flag-icon-bj {
  background-image: url(../flags/4x3/bj.svg);
}
.flag-icon-bj.flag-icon-squared {
  background-image: url(../flags/1x1/bj.svg);
}
.flag-icon-bl {
  background-image: url(../flags/4x3/bl.svg);
}
.flag-icon-bl.flag-icon-squared {
  background-image: url(../flags/1x1/bl.svg);
}
.flag-icon-bm {
  background-image: url(../flags/4x3/bm.svg);
}
.flag-icon-bm.flag-icon-squared {
  background-image: url(../flags/1x1/bm.svg);
}
.flag-icon-bn {
  background-image: url(../flags/4x3/bn.svg);
}
.flag-icon-bn.flag-icon-squared {
  background-image: url(../flags/1x1/bn.svg);
}
.flag-icon-bo {
  background-image: url(../flags/4x3/bo.svg);
}
.flag-icon-bo.flag-icon-squared {
  background-image: url(../flags/1x1/bo.svg);
}
.flag-icon-bq {
  background-image: url(../flags/4x3/bq.svg);
}
.flag-icon-bq.flag-icon-squared {
  background-image: url(../flags/1x1/bq.svg);
}
.flag-icon-br {
  background-image: url(../flags/4x3/br.svg);
}
.flag-icon-br.flag-icon-squared {
  background-image: url(../flags/1x1/br.svg);
}
.flag-icon-bs {
  background-image: url(../flags/4x3/bs.svg);
}
.flag-icon-bs.flag-icon-squared {
  background-image: url(../flags/1x1/bs.svg);
}
.flag-icon-bt {
  background-image: url(../flags/4x3/bt.svg);
}
.flag-icon-bt.flag-icon-squared {
  background-image: url(../flags/1x1/bt.svg);
}
.flag-icon-bv {
  background-image: url(../flags/4x3/bv.svg);
}
.flag-icon-bv.flag-icon-squared {
  background-image: url(../flags/1x1/bv.svg);
}
.flag-icon-bw {
  background-image: url(../flags/4x3/bw.svg);
}
.flag-icon-bw.flag-icon-squared {
  background-image: url(../flags/1x1/bw.svg);
}
.flag-icon-by {
  background-image: url(../flags/4x3/by.svg);
}
.flag-icon-by.flag-icon-squared {
  background-image: url(../flags/1x1/by.svg);
}
.flag-icon-bz {
  background-image: url(../flags/4x3/bz.svg);
}
.flag-icon-bz.flag-icon-squared {
  background-image: url(../flags/1x1/bz.svg);
}
.flag-icon-ca {
  background-image: url(../flags/4x3/ca.svg);
}
.flag-icon-ca.flag-icon-squared {
  background-image: url(../flags/1x1/ca.svg);
}
.flag-icon-cc {
  background-image: url(../flags/4x3/cc.svg);
}
.flag-icon-cc.flag-icon-squared {
  background-image: url(../flags/1x1/cc.svg);
}
.flag-icon-cd {
  background-image: url(../flags/4x3/cd.svg);
}
.flag-icon-cd.flag-icon-squared {
  background-image: url(../flags/1x1/cd.svg);
}
.flag-icon-cf {
  background-image: url(../flags/4x3/cf.svg);
}
.flag-icon-cf.flag-icon-squared {
  background-image: url(../flags/1x1/cf.svg);
}
.flag-icon-cg {
  background-image: url(../flags/4x3/cg.svg);
}
.flag-icon-cg.flag-icon-squared {
  background-image: url(../flags/1x1/cg.svg);
}
.flag-icon-ch {
  background-image: url(../flags/4x3/ch.svg);
}
.flag-icon-ch.flag-icon-squared {
  background-image: url(../flags/1x1/ch.svg);
}
.flag-icon-ci {
  background-image: url(../flags/4x3/ci.svg);
}
.flag-icon-ci.flag-icon-squared {
  background-image: url(../flags/1x1/ci.svg);
}
.flag-icon-ck {
  background-image: url(../flags/4x3/ck.svg);
}
.flag-icon-ck.flag-icon-squared {
  background-image: url(../flags/1x1/ck.svg);
}
.flag-icon-cl {
  background-image: url(../flags/4x3/cl.svg);
}
.flag-icon-cl.flag-icon-squared {
  background-image: url(../flags/1x1/cl.svg);
}
.flag-icon-cm {
  background-image: url(../flags/4x3/cm.svg);
}
.flag-icon-cm.flag-icon-squared {
  background-image: url(../flags/1x1/cm.svg);
}
.flag-icon-cn {
  background-image: url(../flags/4x3/cn.svg);
}
.flag-icon-cn.flag-icon-squared {
  background-image: url(../flags/1x1/cn.svg);
}
.flag-icon-co {
  background-image: url(../flags/4x3/co.svg);
}
.flag-icon-co.flag-icon-squared {
  background-image: url(../flags/1x1/co.svg);
}
.flag-icon-cr {
  background-image: url(../flags/4x3/cr.svg);
}
.flag-icon-cr.flag-icon-squared {
  background-image: url(../flags/1x1/cr.svg);
}
.flag-icon-cu {
  background-image: url(../flags/4x3/cu.svg);
}
.flag-icon-cu.flag-icon-squared {
  background-image: url(../flags/1x1/cu.svg);
}
.flag-icon-cv {
  background-image: url(../flags/4x3/cv.svg);
}
.flag-icon-cv.flag-icon-squared {
  background-image: url(../flags/1x1/cv.svg);
}
.flag-icon-cw {
  background-image: url(../flags/4x3/cw.svg);
}
.flag-icon-cw.flag-icon-squared {
  background-image: url(../flags/1x1/cw.svg);
}
.flag-icon-cx {
  background-image: url(../flags/4x3/cx.svg);
}
.flag-icon-cx.flag-icon-squared {
  background-image: url(../flags/1x1/cx.svg);
}
.flag-icon-cy {
  background-image: url(../flags/4x3/cy.svg);
}
.flag-icon-cy.flag-icon-squared {
  background-image: url(../flags/1x1/cy.svg);
}
.flag-icon-cz {
  background-image: url(../flags/4x3/cz.svg);
}
.flag-icon-cz.flag-icon-squared {
  background-image: url(../flags/1x1/cz.svg);
}
.flag-icon-de {
  background-image: url(../flags/4x3/de.svg);
}
.flag-icon-de.flag-icon-squared {
  background-image: url(../flags/1x1/de.svg);
}
.flag-icon-dj {
  background-image: url(../flags/4x3/dj.svg);
}
.flag-icon-dj.flag-icon-squared {
  background-image: url(../flags/1x1/dj.svg);
}
.flag-icon-dk {
  background-image: url(../flags/4x3/dk.svg);
}
.flag-icon-dk.flag-icon-squared {
  background-image: url(../flags/1x1/dk.svg);
}
.flag-icon-dm {
  background-image: url(../flags/4x3/dm.svg);
}
.flag-icon-dm.flag-icon-squared {
  background-image: url(../flags/1x1/dm.svg);
}
.flag-icon-do {
  background-image: url(../flags/4x3/do.svg);
}
.flag-icon-do.flag-icon-squared {
  background-image: url(../flags/1x1/do.svg);
}
.flag-icon-dz {
  background-image: url(../flags/4x3/dz.svg);
}
.flag-icon-dz.flag-icon-squared {
  background-image: url(../flags/1x1/dz.svg);
}
.flag-icon-ec {
  background-image: url(../flags/4x3/ec.svg);
}
.flag-icon-ec.flag-icon-squared {
  background-image: url(../flags/1x1/ec.svg);
}
.flag-icon-ee {
  background-image: url(../flags/4x3/ee.svg);
}
.flag-icon-ee.flag-icon-squared {
  background-image: url(../flags/1x1/ee.svg);
}
.flag-icon-eg {
  background-image: url(../flags/4x3/eg.svg);
}
.flag-icon-eg.flag-icon-squared {
  background-image: url(../flags/1x1/eg.svg);
}
.flag-icon-eh {
  background-image: url(../flags/4x3/eh.svg);
}
.flag-icon-eh.flag-icon-squared {
  background-image: url(../flags/1x1/eh.svg);
}
.flag-icon-er {
  background-image: url(../flags/4x3/er.svg);
}
.flag-icon-er.flag-icon-squared {
  background-image: url(../flags/1x1/er.svg);
}
.flag-icon-es {
  background-image: url(../flags/4x3/es.svg);
}
.flag-icon-es.flag-icon-squared {
  background-image: url(../flags/1x1/es.svg);
}
.flag-icon-et {
  background-image: url(../flags/4x3/et.svg);
}
.flag-icon-et.flag-icon-squared {
  background-image: url(../flags/1x1/et.svg);
}
.flag-icon-fi {
  background-image: url(../flags/4x3/fi.svg);
}
.flag-icon-fi.flag-icon-squared {
  background-image: url(../flags/1x1/fi.svg);
}
.flag-icon-fj {
  background-image: url(../flags/4x3/fj.svg);
}
.flag-icon-fj.flag-icon-squared {
  background-image: url(../flags/1x1/fj.svg);
}
.flag-icon-fk {
  background-image: url(../flags/4x3/fk.svg);
}
.flag-icon-fk.flag-icon-squared {
  background-image: url(../flags/1x1/fk.svg);
}
.flag-icon-fm {
  background-image: url(../flags/4x3/fm.svg);
}
.flag-icon-fm.flag-icon-squared {
  background-image: url(../flags/1x1/fm.svg);
}
.flag-icon-fo {
  background-image: url(../flags/4x3/fo.svg);
}
.flag-icon-fo.flag-icon-squared {
  background-image: url(../flags/1x1/fo.svg);
}
.flag-icon-fr {
  background-image: url(../flags/4x3/fr.svg);
}
.flag-icon-fr.flag-icon-squared {
  background-image: url(../flags/1x1/fr.svg);
}
.flag-icon-ga {
  background-image: url(../flags/4x3/ga.svg);
}
.flag-icon-ga.flag-icon-squared {
  background-image: url(../flags/1x1/ga.svg);
}
.flag-icon-gb {
  background-image: url(../flags/4x3/gb.svg);
}
.flag-icon-gb.flag-icon-squared {
  background-image: url(../flags/1x1/gb.svg);
}
.flag-icon-gd {
  background-image: url(../flags/4x3/gd.svg);
}
.flag-icon-gd.flag-icon-squared {
  background-image: url(../flags/1x1/gd.svg);
}
.flag-icon-ge {
  background-image: url(../flags/4x3/ge.svg);
}
.flag-icon-ge.flag-icon-squared {
  background-image: url(../flags/1x1/ge.svg);
}
.flag-icon-gf {
  background-image: url(../flags/4x3/gf.svg);
}
.flag-icon-gf.flag-icon-squared {
  background-image: url(../flags/1x1/gf.svg);
}
.flag-icon-gg {
  background-image: url(../flags/4x3/gg.svg);
}
.flag-icon-gg.flag-icon-squared {
  background-image: url(../flags/1x1/gg.svg);
}
.flag-icon-gh {
  background-image: url(../flags/4x3/gh.svg);
}
.flag-icon-gh.flag-icon-squared {
  background-image: url(../flags/1x1/gh.svg);
}
.flag-icon-gi {
  background-image: url(../flags/4x3/gi.svg);
}
.flag-icon-gi.flag-icon-squared {
  background-image: url(../flags/1x1/gi.svg);
}
.flag-icon-gl {
  background-image: url(../flags/4x3/gl.svg);
}
.flag-icon-gl.flag-icon-squared {
  background-image: url(../flags/1x1/gl.svg);
}
.flag-icon-gm {
  background-image: url(../flags/4x3/gm.svg);
}
.flag-icon-gm.flag-icon-squared {
  background-image: url(../flags/1x1/gm.svg);
}
.flag-icon-gn {
  background-image: url(../flags/4x3/gn.svg);
}
.flag-icon-gn.flag-icon-squared {
  background-image: url(../flags/1x1/gn.svg);
}
.flag-icon-gp {
  background-image: url(../flags/4x3/gp.svg);
}
.flag-icon-gp.flag-icon-squared {
  background-image: url(../flags/1x1/gp.svg);
}
.flag-icon-gq {
  background-image: url(../flags/4x3/gq.svg);
}
.flag-icon-gq.flag-icon-squared {
  background-image: url(../flags/1x1/gq.svg);
}
.flag-icon-gr {
  background-image: url(../flags/4x3/gr.svg);
}
.flag-icon-gr.flag-icon-squared {
  background-image: url(../flags/1x1/gr.svg);
}
.flag-icon-gs {
  background-image: url(../flags/4x3/gs.svg);
}
.flag-icon-gs.flag-icon-squared {
  background-image: url(../flags/1x1/gs.svg);
}
.flag-icon-gt {
  background-image: url(../flags/4x3/gt.svg);
}
.flag-icon-gt.flag-icon-squared {
  background-image: url(../flags/1x1/gt.svg);
}
.flag-icon-gu {
  background-image: url(../flags/4x3/gu.svg);
}
.flag-icon-gu.flag-icon-squared {
  background-image: url(../flags/1x1/gu.svg);
}
.flag-icon-gw {
  background-image: url(../flags/4x3/gw.svg);
}
.flag-icon-gw.flag-icon-squared {
  background-image: url(../flags/1x1/gw.svg);
}
.flag-icon-gy {
  background-image: url(../flags/4x3/gy.svg);
}
.flag-icon-gy.flag-icon-squared {
  background-image: url(../flags/1x1/gy.svg);
}
.flag-icon-hk {
  background-image: url(../flags/4x3/hk.svg);
}
.flag-icon-hk.flag-icon-squared {
  background-image: url(../flags/1x1/hk.svg);
}
.flag-icon-hm {
  background-image: url(../flags/4x3/hm.svg);
}
.flag-icon-hm.flag-icon-squared {
  background-image: url(../flags/1x1/hm.svg);
}
.flag-icon-hn {
  background-image: url(../flags/4x3/hn.svg);
}
.flag-icon-hn.flag-icon-squared {
  background-image: url(../flags/1x1/hn.svg);
}
.flag-icon-hr {
  background-image: url(../flags/4x3/hr.svg);
}
.flag-icon-hr.flag-icon-squared {
  background-image: url(../flags/1x1/hr.svg);
}
.flag-icon-ht {
  background-image: url(../flags/4x3/ht.svg);
}
.flag-icon-ht.flag-icon-squared {
  background-image: url(../flags/1x1/ht.svg);
}
.flag-icon-hu {
  background-image: url(../flags/4x3/hu.svg);
}
.flag-icon-hu.flag-icon-squared {
  background-image: url(../flags/1x1/hu.svg);
}
.flag-icon-id {
  background-image: url(../flags/4x3/id.svg);
}
.flag-icon-id.flag-icon-squared {
  background-image: url(../flags/1x1/id.svg);
}
.flag-icon-ie {
  background-image: url(../flags/4x3/ie.svg);
}
.flag-icon-ie.flag-icon-squared {
  background-image: url(../flags/1x1/ie.svg);
}
.flag-icon-il {
  background-image: url(../flags/4x3/il.svg);
}
.flag-icon-il.flag-icon-squared {
  background-image: url(../flags/1x1/il.svg);
}
.flag-icon-im {
  background-image: url(../flags/4x3/im.svg);
}
.flag-icon-im.flag-icon-squared {
  background-image: url(../flags/1x1/im.svg);
}
.flag-icon-in {
  background-image: url(../flags/4x3/in.svg);
}
.flag-icon-in.flag-icon-squared {
  background-image: url(../flags/1x1/in.svg);
}
.flag-icon-io {
  background-image: url(../flags/4x3/io.svg);
}
.flag-icon-io.flag-icon-squared {
  background-image: url(../flags/1x1/io.svg);
}
.flag-icon-iq {
  background-image: url(../flags/4x3/iq.svg);
}
.flag-icon-iq.flag-icon-squared {
  background-image: url(../flags/1x1/iq.svg);
}
.flag-icon-ir {
  background-image: url(../flags/4x3/ir.svg);
}
.flag-icon-ir.flag-icon-squared {
  background-image: url(../flags/1x1/ir.svg);
}
.flag-icon-is {
  background-image: url(../flags/4x3/is.svg);
}
.flag-icon-is.flag-icon-squared {
  background-image: url(../flags/1x1/is.svg);
}
.flag-icon-it {
  background-image: url(../flags/4x3/it.svg);
}
.flag-icon-it.flag-icon-squared {
  background-image: url(../flags/1x1/it.svg);
}
.flag-icon-je {
  background-image: url(../flags/4x3/je.svg);
}
.flag-icon-je.flag-icon-squared {
  background-image: url(../flags/1x1/je.svg);
}
.flag-icon-jm {
  background-image: url(../flags/4x3/jm.svg);
}
.flag-icon-jm.flag-icon-squared {
  background-image: url(../flags/1x1/jm.svg);
}
.flag-icon-jo {
  background-image: url(../flags/4x3/jo.svg);
}
.flag-icon-jo.flag-icon-squared {
  background-image: url(../flags/1x1/jo.svg);
}
.flag-icon-jp {
  background-image: url(../flags/4x3/jp.svg);
}
.flag-icon-jp.flag-icon-squared {
  background-image: url(../flags/1x1/jp.svg);
}
.flag-icon-ke {
  background-image: url(../flags/4x3/ke.svg);
}
.flag-icon-ke.flag-icon-squared {
  background-image: url(../flags/1x1/ke.svg);
}
.flag-icon-kg {
  background-image: url(../flags/4x3/kg.svg);
}
.flag-icon-kg.flag-icon-squared {
  background-image: url(../flags/1x1/kg.svg);
}
.flag-icon-kh {
  background-image: url(../flags/4x3/kh.svg);
}
.flag-icon-kh.flag-icon-squared {
  background-image: url(../flags/1x1/kh.svg);
}
.flag-icon-ki {
  background-image: url(../flags/4x3/ki.svg);
}
.flag-icon-ki.flag-icon-squared {
  background-image: url(../flags/1x1/ki.svg);
}
.flag-icon-km {
  background-image: url(../flags/4x3/km.svg);
}
.flag-icon-km.flag-icon-squared {
  background-image: url(../flags/1x1/km.svg);
}
.flag-icon-kn {
  background-image: url(../flags/4x3/kn.svg);
}
.flag-icon-kn.flag-icon-squared {
  background-image: url(../flags/1x1/kn.svg);
}
.flag-icon-kp {
  background-image: url(../flags/4x3/kp.svg);
}
.flag-icon-kp.flag-icon-squared {
  background-image: url(../flags/1x1/kp.svg);
}
.flag-icon-kr {
  background-image: url(../flags/4x3/kr.svg);
}
.flag-icon-kr.flag-icon-squared {
  background-image: url(../flags/1x1/kr.svg);
}
.flag-icon-kw {
  background-image: url(../flags/4x3/kw.svg);
}
.flag-icon-kw.flag-icon-squared {
  background-image: url(../flags/1x1/kw.svg);
}
.flag-icon-ky {
  background-image: url(../flags/4x3/ky.svg);
}
.flag-icon-ky.flag-icon-squared {
  background-image: url(../flags/1x1/ky.svg);
}
.flag-icon-kz {
  background-image: url(../flags/4x3/kz.svg);
}
.flag-icon-kz.flag-icon-squared {
  background-image: url(../flags/1x1/kz.svg);
}
.flag-icon-la {
  background-image: url(../flags/4x3/la.svg);
}
.flag-icon-la.flag-icon-squared {
  background-image: url(../flags/1x1/la.svg);
}
.flag-icon-lb {
  background-image: url(../flags/4x3/lb.svg);
}
.flag-icon-lb.flag-icon-squared {
  background-image: url(../flags/1x1/lb.svg);
}
.flag-icon-lc {
  background-image: url(../flags/4x3/lc.svg);
}
.flag-icon-lc.flag-icon-squared {
  background-image: url(../flags/1x1/lc.svg);
}
.flag-icon-li {
  background-image: url(../flags/4x3/li.svg);
}
.flag-icon-li.flag-icon-squared {
  background-image: url(../flags/1x1/li.svg);
}
.flag-icon-lk {
  background-image: url(../flags/4x3/lk.svg);
}
.flag-icon-lk.flag-icon-squared {
  background-image: url(../flags/1x1/lk.svg);
}
.flag-icon-lr {
  background-image: url(../flags/4x3/lr.svg);
}
.flag-icon-lr.flag-icon-squared {
  background-image: url(../flags/1x1/lr.svg);
}
.flag-icon-ls {
  background-image: url(../flags/4x3/ls.svg);
}
.flag-icon-ls.flag-icon-squared {
  background-image: url(../flags/1x1/ls.svg);
}
.flag-icon-lt {
  background-image: url(../flags/4x3/lt.svg);
}
.flag-icon-lt.flag-icon-squared {
  background-image: url(../flags/1x1/lt.svg);
}
.flag-icon-lu {
  background-image: url(../flags/4x3/lu.svg);
}
.flag-icon-lu.flag-icon-squared {
  background-image: url(../flags/1x1/lu.svg);
}
.flag-icon-lv {
  background-image: url(../flags/4x3/lv.svg);
}
.flag-icon-lv.flag-icon-squared {
  background-image: url(../flags/1x1/lv.svg);
}
.flag-icon-ly {
  background-image: url(../flags/4x3/ly.svg);
}
.flag-icon-ly.flag-icon-squared {
  background-image: url(../flags/1x1/ly.svg);
}
.flag-icon-ma {
  background-image: url(../flags/4x3/ma.svg);
}
.flag-icon-ma.flag-icon-squared {
  background-image: url(../flags/1x1/ma.svg);
}
.flag-icon-mc {
  background-image: url(../flags/4x3/mc.svg);
}
.flag-icon-mc.flag-icon-squared {
  background-image: url(../flags/1x1/mc.svg);
}
.flag-icon-md {
  background-image: url(../flags/4x3/md.svg);
}
.flag-icon-md.flag-icon-squared {
  background-image: url(../flags/1x1/md.svg);
}
.flag-icon-me {
  background-image: url(../flags/4x3/me.svg);
}
.flag-icon-me.flag-icon-squared {
  background-image: url(../flags/1x1/me.svg);
}
.flag-icon-mf {
  background-image: url(../flags/4x3/mf.svg);
}
.flag-icon-mf.flag-icon-squared {
  background-image: url(../flags/1x1/mf.svg);
}
.flag-icon-mg {
  background-image: url(../flags/4x3/mg.svg);
}
.flag-icon-mg.flag-icon-squared {
  background-image: url(../flags/1x1/mg.svg);
}
.flag-icon-mh {
  background-image: url(../flags/4x3/mh.svg);
}
.flag-icon-mh.flag-icon-squared {
  background-image: url(../flags/1x1/mh.svg);
}
.flag-icon-mk {
  background-image: url(../flags/4x3/mk.svg);
}
.flag-icon-mk.flag-icon-squared {
  background-image: url(../flags/1x1/mk.svg);
}
.flag-icon-ml {
  background-image: url(../flags/4x3/ml.svg);
}
.flag-icon-ml.flag-icon-squared {
  background-image: url(../flags/1x1/ml.svg);
}
.flag-icon-mm {
  background-image: url(../flags/4x3/mm.svg);
}
.flag-icon-mm.flag-icon-squared {
  background-image: url(../flags/1x1/mm.svg);
}
.flag-icon-mn {
  background-image: url(../flags/4x3/mn.svg);
}
.flag-icon-mn.flag-icon-squared {
  background-image: url(../flags/1x1/mn.svg);
}
.flag-icon-mo {
  background-image: url(../flags/4x3/mo.svg);
}
.flag-icon-mo.flag-icon-squared {
  background-image: url(../flags/1x1/mo.svg);
}
.flag-icon-mp {
  background-image: url(../flags/4x3/mp.svg);
}
.flag-icon-mp.flag-icon-squared {
  background-image: url(../flags/1x1/mp.svg);
}
.flag-icon-mq {
  background-image: url(../flags/4x3/mq.svg);
}
.flag-icon-mq.flag-icon-squared {
  background-image: url(../flags/1x1/mq.svg);
}
.flag-icon-mr {
  background-image: url(../flags/4x3/mr.svg);
}
.flag-icon-mr.flag-icon-squared {
  background-image: url(../flags/1x1/mr.svg);
}
.flag-icon-ms {
  background-image: url(../flags/4x3/ms.svg);
}
.flag-icon-ms.flag-icon-squared {
  background-image: url(../flags/1x1/ms.svg);
}
.flag-icon-mt {
  background-image: url(../flags/4x3/mt.svg);
}
.flag-icon-mt.flag-icon-squared {
  background-image: url(../flags/1x1/mt.svg);
}
.flag-icon-mu {
  background-image: url(../flags/4x3/mu.svg);
}
.flag-icon-mu.flag-icon-squared {
  background-image: url(../flags/1x1/mu.svg);
}
.flag-icon-mv {
  background-image: url(../flags/4x3/mv.svg);
}
.flag-icon-mv.flag-icon-squared {
  background-image: url(../flags/1x1/mv.svg);
}
.flag-icon-mw {
  background-image: url(../flags/4x3/mw.svg);
}
.flag-icon-mw.flag-icon-squared {
  background-image: url(../flags/1x1/mw.svg);
}
.flag-icon-mx {
  background-image: url(../flags/4x3/mx.svg);
}
.flag-icon-mx.flag-icon-squared {
  background-image: url(../flags/1x1/mx.svg);
}
.flag-icon-my {
  background-image: url(../flags/4x3/my.svg);
}
.flag-icon-my.flag-icon-squared {
  background-image: url(../flags/1x1/my.svg);
}
.flag-icon-mz {
  background-image: url(../flags/4x3/mz.svg);
}
.flag-icon-mz.flag-icon-squared {
  background-image: url(../flags/1x1/mz.svg);
}
.flag-icon-na {
  background-image: url(../flags/4x3/na.svg);
}
.flag-icon-na.flag-icon-squared {
  background-image: url(../flags/1x1/na.svg);
}
.flag-icon-nc {
  background-image: url(../flags/4x3/nc.svg);
}
.flag-icon-nc.flag-icon-squared {
  background-image: url(../flags/1x1/nc.svg);
}
.flag-icon-ne {
  background-image: url(../flags/4x3/ne.svg);
}
.flag-icon-ne.flag-icon-squared {
  background-image: url(../flags/1x1/ne.svg);
}
.flag-icon-nf {
  background-image: url(../flags/4x3/nf.svg);
}
.flag-icon-nf.flag-icon-squared {
  background-image: url(../flags/1x1/nf.svg);
}
.flag-icon-ng {
  background-image: url(../flags/4x3/ng.svg);
}
.flag-icon-ng.flag-icon-squared {
  background-image: url(../flags/1x1/ng.svg);
}
.flag-icon-ni {
  background-image: url(../flags/4x3/ni.svg);
}
.flag-icon-ni.flag-icon-squared {
  background-image: url(../flags/1x1/ni.svg);
}
.flag-icon-nl {
  background-image: url(../flags/4x3/nl.svg);
}
.flag-icon-nl.flag-icon-squared {
  background-image: url(../flags/1x1/nl.svg);
}
.flag-icon-no {
  background-image: url(../flags/4x3/no.svg);
}
.flag-icon-no.flag-icon-squared {
  background-image: url(../flags/1x1/no.svg);
}
.flag-icon-np {
  background-image: url(../flags/4x3/np.svg);
}
.flag-icon-np.flag-icon-squared {
  background-image: url(../flags/1x1/np.svg);
}
.flag-icon-nr {
  background-image: url(../flags/4x3/nr.svg);
}
.flag-icon-nr.flag-icon-squared {
  background-image: url(../flags/1x1/nr.svg);
}
.flag-icon-nu {
  background-image: url(../flags/4x3/nu.svg);
}
.flag-icon-nu.flag-icon-squared {
  background-image: url(../flags/1x1/nu.svg);
}
.flag-icon-nz {
  background-image: url(../flags/4x3/nz.svg);
}
.flag-icon-nz.flag-icon-squared {
  background-image: url(../flags/1x1/nz.svg);
}
.flag-icon-om {
  background-image: url(../flags/4x3/om.svg);
}
.flag-icon-om.flag-icon-squared {
  background-image: url(../flags/1x1/om.svg);
}
.flag-icon-pa {
  background-image: url(../flags/4x3/pa.svg);
}
.flag-icon-pa.flag-icon-squared {
  background-image: url(../flags/1x1/pa.svg);
}
.flag-icon-pe {
  background-image: url(../flags/4x3/pe.svg);
}
.flag-icon-pe.flag-icon-squared {
  background-image: url(../flags/1x1/pe.svg);
}
.flag-icon-pf {
  background-image: url(../flags/4x3/pf.svg);
}
.flag-icon-pf.flag-icon-squared {
  background-image: url(../flags/1x1/pf.svg);
}
.flag-icon-pg {
  background-image: url(../flags/4x3/pg.svg);
}
.flag-icon-pg.flag-icon-squared {
  background-image: url(../flags/1x1/pg.svg);
}
.flag-icon-ph {
  background-image: url(../flags/4x3/ph.svg);
}
.flag-icon-ph.flag-icon-squared {
  background-image: url(../flags/1x1/ph.svg);
}
.flag-icon-pk {
  background-image: url(../flags/4x3/pk.svg);
}
.flag-icon-pk.flag-icon-squared {
  background-image: url(../flags/1x1/pk.svg);
}
.flag-icon-pl {
  background-image: url(../flags/4x3/pl.svg);
}
.flag-icon-pl.flag-icon-squared {
  background-image: url(../flags/1x1/pl.svg);
}
.flag-icon-pm {
  background-image: url(../flags/4x3/pm.svg);
}
.flag-icon-pm.flag-icon-squared {
  background-image: url(../flags/1x1/pm.svg);
}
.flag-icon-pn {
  background-image: url(../flags/4x3/pn.svg);
}
.flag-icon-pn.flag-icon-squared {
  background-image: url(../flags/1x1/pn.svg);
}
.flag-icon-pr {
  background-image: url(../flags/4x3/pr.svg);
}
.flag-icon-pr.flag-icon-squared {
  background-image: url(../flags/1x1/pr.svg);
}
.flag-icon-ps {
  background-image: url(../flags/4x3/ps.svg);
}
.flag-icon-ps.flag-icon-squared {
  background-image: url(../flags/1x1/ps.svg);
}
.flag-icon-pt {
  background-image: url(../flags/4x3/pt.svg);
}
.flag-icon-pt.flag-icon-squared {
  background-image: url(../flags/1x1/pt.svg);
}
.flag-icon-pw {
  background-image: url(../flags/4x3/pw.svg);
}
.flag-icon-pw.flag-icon-squared {
  background-image: url(../flags/1x1/pw.svg);
}
.flag-icon-py {
  background-image: url(../flags/4x3/py.svg);
}
.flag-icon-py.flag-icon-squared {
  background-image: url(../flags/1x1/py.svg);
}
.flag-icon-qa {
  background-image: url(../flags/4x3/qa.svg);
}
.flag-icon-qa.flag-icon-squared {
  background-image: url(../flags/1x1/qa.svg);
}
.flag-icon-re {
  background-image: url(../flags/4x3/re.svg);
}
.flag-icon-re.flag-icon-squared {
  background-image: url(../flags/1x1/re.svg);
}
.flag-icon-ro {
  background-image: url(../flags/4x3/ro.svg);
}
.flag-icon-ro.flag-icon-squared {
  background-image: url(../flags/1x1/ro.svg);
}
.flag-icon-rs {
  background-image: url(../flags/4x3/rs.svg);
}
.flag-icon-rs.flag-icon-squared {
  background-image: url(../flags/1x1/rs.svg);
}
.flag-icon-ru {
  background-image: url(../flags/4x3/ru.svg);
}
.flag-icon-ru.flag-icon-squared {
  background-image: url(../flags/1x1/ru.svg);
}
.flag-icon-rw {
  background-image: url(../flags/4x3/rw.svg);
}
.flag-icon-rw.flag-icon-squared {
  background-image: url(../flags/1x1/rw.svg);
}
.flag-icon-sa {
  background-image: url(../flags/4x3/sa.svg);
}
.flag-icon-sa.flag-icon-squared {
  background-image: url(../flags/1x1/sa.svg);
}
.flag-icon-sb {
  background-image: url(../flags/4x3/sb.svg);
}
.flag-icon-sb.flag-icon-squared {
  background-image: url(../flags/1x1/sb.svg);
}
.flag-icon-sc {
  background-image: url(../flags/4x3/sc.svg);
}
.flag-icon-sc.flag-icon-squared {
  background-image: url(../flags/1x1/sc.svg);
}
.flag-icon-sd {
  background-image: url(../flags/4x3/sd.svg);
}
.flag-icon-sd.flag-icon-squared {
  background-image: url(../flags/1x1/sd.svg);
}
.flag-icon-se {
  background-image: url(../flags/4x3/se.svg);
}
.flag-icon-se.flag-icon-squared {
  background-image: url(../flags/1x1/se.svg);
}
.flag-icon-sg {
  background-image: url(../flags/4x3/sg.svg);
}
.flag-icon-sg.flag-icon-squared {
  background-image: url(../flags/1x1/sg.svg);
}
.flag-icon-sh {
  background-image: url(../flags/4x3/sh.svg);
}
.flag-icon-sh.flag-icon-squared {
  background-image: url(../flags/1x1/sh.svg);
}
.flag-icon-si {
  background-image: url(../flags/4x3/si.svg);
}
.flag-icon-si.flag-icon-squared {
  background-image: url(../flags/1x1/si.svg);
}
.flag-icon-sj {
  background-image: url(../flags/4x3/sj.svg);
}
.flag-icon-sj.flag-icon-squared {
  background-image: url(../flags/1x1/sj.svg);
}
.flag-icon-sk {
  background-image: url(../flags/4x3/sk.svg);
}
.flag-icon-sk.flag-icon-squared {
  background-image: url(../flags/1x1/sk.svg);
}
.flag-icon-sl {
  background-image: url(../flags/4x3/sl.svg);
}
.flag-icon-sl.flag-icon-squared {
  background-image: url(../flags/1x1/sl.svg);
}
.flag-icon-sm {
  background-image: url(../flags/4x3/sm.svg);
}
.flag-icon-sm.flag-icon-squared {
  background-image: url(../flags/1x1/sm.svg);
}
.flag-icon-sn {
  background-image: url(../flags/4x3/sn.svg);
}
.flag-icon-sn.flag-icon-squared {
  background-image: url(../flags/1x1/sn.svg);
}
.flag-icon-so {
  background-image: url(../flags/4x3/so.svg);
}
.flag-icon-so.flag-icon-squared {
  background-image: url(../flags/1x1/so.svg);
}
.flag-icon-sr {
  background-image: url(../flags/4x3/sr.svg);
}
.flag-icon-sr.flag-icon-squared {
  background-image: url(../flags/1x1/sr.svg);
}
.flag-icon-ss {
  background-image: url(../flags/4x3/ss.svg);
}
.flag-icon-ss.flag-icon-squared {
  background-image: url(../flags/1x1/ss.svg);
}
.flag-icon-st {
  background-image: url(../flags/4x3/st.svg);
}
.flag-icon-st.flag-icon-squared {
  background-image: url(../flags/1x1/st.svg);
}
.flag-icon-sv {
  background-image: url(../flags/4x3/sv.svg);
}
.flag-icon-sv.flag-icon-squared {
  background-image: url(../flags/1x1/sv.svg);
}
.flag-icon-sx {
  background-image: url(../flags/4x3/sx.svg);
}
.flag-icon-sx.flag-icon-squared {
  background-image: url(../flags/1x1/sx.svg);
}
.flag-icon-sy {
  background-image: url(../flags/4x3/sy.svg);
}
.flag-icon-sy.flag-icon-squared {
  background-image: url(../flags/1x1/sy.svg);
}
.flag-icon-sz {
  background-image: url(../flags/4x3/sz.svg);
}
.flag-icon-sz.flag-icon-squared {
  background-image: url(../flags/1x1/sz.svg);
}
.flag-icon-tc {
  background-image: url(../flags/4x3/tc.svg);
}
.flag-icon-tc.flag-icon-squared {
  background-image: url(../flags/1x1/tc.svg);
}
.flag-icon-td {
  background-image: url(../flags/4x3/td.svg);
}
.flag-icon-td.flag-icon-squared {
  background-image: url(../flags/1x1/td.svg);
}
.flag-icon-tf {
  background-image: url(../flags/4x3/tf.svg);
}
.flag-icon-tf.flag-icon-squared {
  background-image: url(../flags/1x1/tf.svg);
}
.flag-icon-tg {
  background-image: url(../flags/4x3/tg.svg);
}
.flag-icon-tg.flag-icon-squared {
  background-image: url(../flags/1x1/tg.svg);
}
.flag-icon-th {
  background-image: url(../flags/4x3/th.svg);
}
.flag-icon-th.flag-icon-squared {
  background-image: url(../flags/1x1/th.svg);
}
.flag-icon-tj {
  background-image: url(../flags/4x3/tj.svg);
}
.flag-icon-tj.flag-icon-squared {
  background-image: url(../flags/1x1/tj.svg);
}
.flag-icon-tk {
  background-image: url(../flags/4x3/tk.svg);
}
.flag-icon-tk.flag-icon-squared {
  background-image: url(../flags/1x1/tk.svg);
}
.flag-icon-tl {
  background-image: url(../flags/4x3/tl.svg);
}
.flag-icon-tl.flag-icon-squared {
  background-image: url(../flags/1x1/tl.svg);
}
.flag-icon-tm {
  background-image: url(../flags/4x3/tm.svg);
}
.flag-icon-tm.flag-icon-squared {
  background-image: url(../flags/1x1/tm.svg);
}
.flag-icon-tn {
  background-image: url(../flags/4x3/tn.svg);
}
.flag-icon-tn.flag-icon-squared {
  background-image: url(../flags/1x1/tn.svg);
}
.flag-icon-to {
  background-image: url(../flags/4x3/to.svg);
}
.flag-icon-to.flag-icon-squared {
  background-image: url(../flags/1x1/to.svg);
}
.flag-icon-tr {
  background-image: url(../flags/4x3/tr.svg);
}
.flag-icon-tr.flag-icon-squared {
  background-image: url(../flags/1x1/tr.svg);
}
.flag-icon-tt {
  background-image: url(../flags/4x3/tt.svg);
}
.flag-icon-tt.flag-icon-squared {
  background-image: url(../flags/1x1/tt.svg);
}
.flag-icon-tv {
  background-image: url(../flags/4x3/tv.svg);
}
.flag-icon-tv.flag-icon-squared {
  background-image: url(../flags/1x1/tv.svg);
}
.flag-icon-tw {
  background-image: url(../flags/4x3/tw.svg);
}
.flag-icon-tw.flag-icon-squared {
  background-image: url(../flags/1x1/tw.svg);
}
.flag-icon-tz {
  background-image: url(../flags/4x3/tz.svg);
}
.flag-icon-tz.flag-icon-squared {
  background-image: url(../flags/1x1/tz.svg);
}
.flag-icon-ua {
  background-image: url(../flags/4x3/ua.svg);
}
.flag-icon-ua.flag-icon-squared {
  background-image: url(../flags/1x1/ua.svg);
}
.flag-icon-ug {
  background-image: url(../flags/4x3/ug.svg);
}
.flag-icon-ug.flag-icon-squared {
  background-image: url(../flags/1x1/ug.svg);
}
.flag-icon-um {
  background-image: url(../flags/4x3/um.svg);
}
.flag-icon-um.flag-icon-squared {
  background-image: url(../flags/1x1/um.svg);
}
.flag-icon-us {
  background-image: url(../flags/4x3/us.svg);
}
.flag-icon-us.flag-icon-squared {
  background-image: url(../flags/1x1/us.svg);
}
.flag-icon-uy {
  background-image: url(../flags/4x3/uy.svg);
}
.flag-icon-uy.flag-icon-squared {
  background-image: url(../flags/1x1/uy.svg);
}
.flag-icon-uz {
  background-image: url(../flags/4x3/uz.svg);
}
.flag-icon-uz.flag-icon-squared {
  background-image: url(../flags/1x1/uz.svg);
}
.flag-icon-va {
  background-image: url(../flags/4x3/va.svg);
}
.flag-icon-va.flag-icon-squared {
  background-image: url(../flags/1x1/va.svg);
}
.flag-icon-vc {
  background-image: url(../flags/4x3/vc.svg);
}
.flag-icon-vc.flag-icon-squared {
  background-image: url(../flags/1x1/vc.svg);
}
.flag-icon-ve {
  background-image: url(../flags/4x3/ve.svg);
}
.flag-icon-ve.flag-icon-squared {
  background-image: url(../flags/1x1/ve.svg);
}
.flag-icon-vg {
  background-image: url(../flags/4x3/vg.svg);
}
.flag-icon-vg.flag-icon-squared {
  background-image: url(../flags/1x1/vg.svg);
}
.flag-icon-vi {
  background-image: url(../flags/4x3/vi.svg);
}
.flag-icon-vi.flag-icon-squared {
  background-image: url(../flags/1x1/vi.svg);
}
.flag-icon-vn {
  background-image: url(../flags/4x3/vn.svg);
}
.flag-icon-vn.flag-icon-squared {
  background-image: url(../flags/1x1/vn.svg);
}
.flag-icon-vu {
  background-image: url(../flags/4x3/vu.svg);
}
.flag-icon-vu.flag-icon-squared {
  background-image: url(../flags/1x1/vu.svg);
}
.flag-icon-wf {
  background-image: url(../flags/4x3/wf.svg);
}
.flag-icon-wf.flag-icon-squared {
  background-image: url(../flags/1x1/wf.svg);
}
.flag-icon-ws {
  background-image: url(../flags/4x3/ws.svg);
}
.flag-icon-ws.flag-icon-squared {
  background-image: url(../flags/1x1/ws.svg);
}
.flag-icon-ye {
  background-image: url(../flags/4x3/ye.svg);
}
.flag-icon-ye.flag-icon-squared {
  background-image: url(../flags/1x1/ye.svg);
}
.flag-icon-yt {
  background-image: url(../flags/4x3/yt.svg);
}
.flag-icon-yt.flag-icon-squared {
  background-image: url(../flags/1x1/yt.svg);
}
.flag-icon-za {
  background-image: url(../flags/4x3/za.svg);
}
.flag-icon-za.flag-icon-squared {
  background-image: url(../flags/1x1/za.svg);
}
.flag-icon-zm {
  background-image: url(../flags/4x3/zm.svg);
}
.flag-icon-zm.flag-icon-squared {
  background-image: url(../flags/1x1/zm.svg);
}
.flag-icon-zw {
  background-image: url(../flags/4x3/zw.svg);
}
.flag-icon-zw.flag-icon-squared {
  background-image: url(../flags/1x1/zw.svg);
}
.flag-icon-es-ct {
  background-image: url(../flags/4x3/es-ct.svg);
}
.flag-icon-es-ct.flag-icon-squared {
  background-image: url(../flags/1x1/es-ct.svg);
}
.flag-icon-eu {
  background-image: url(../flags/4x3/eu.svg);
}
.flag-icon-eu.flag-icon-squared {
  background-image: url(../flags/1x1/eu.svg);
}
.flag-icon-gb-eng {
  background-image: url(../flags/4x3/gb-eng.svg);
}
.flag-icon-gb-eng.flag-icon-squared {
  background-image: url(../flags/1x1/gb-eng.svg);
}
.flag-icon-gb-nir {
  background-image: url(../flags/4x3/gb-nir.svg);
}
.flag-icon-gb-nir.flag-icon-squared {
  background-image: url(../flags/1x1/gb-nir.svg);
}
.flag-icon-gb-sct {
  background-image: url(../flags/4x3/gb-sct.svg);
}
.flag-icon-gb-sct.flag-icon-squared {
  background-image: url(../flags/1x1/gb-sct.svg);
}
.flag-icon-gb-wls {
  background-image: url(../flags/4x3/gb-wls.svg);
}
.flag-icon-gb-wls.flag-icon-squared {
  background-image: url(../flags/1x1/gb-wls.svg);
}
.flag-icon-un {
  background-image: url(../flags/4x3/un.svg);
}
.flag-icon-un.flag-icon-squared {
  background-image: url(../flags/1x1/un.svg);
}












