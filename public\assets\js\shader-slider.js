/**
 * Shader Slider - Modern Fullscreen Slider
 * Inspired by CodeCanyon Shader Slider design
 */

class ShaderSlider {
    constructor(element, options = {}) {
        this.slider = typeof element === 'string' ? document.querySelector(element) : element;

        if (!this.slider) {
            console.error('Shader Slider: Element not found');
            return;
        }

        // Default options
        this.options = {
            autoplay: true,
            autoplayDelay: 10000, // 10 seconds default (increased from 6 seconds)
            animationDuration: 2000, // 2 seconds for smoother transitions
            enableTouch: true,
            enableKeyboard: true,
            pauseOnHover: true,
            transitionEffect: 'fade', // fade, slide, zoom, glitch
            enableParallax: true,
            enableParticles: true,
            ...options
        };

        this.currentSlide = 0;
        this.isAnimating = false;
        this.autoplayTimer = null;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.slideDurations = []; // Store individual slide durations

        this.init();
    }

    init() {
        this.setupElements();
        this.setupVideo();
        this.bindEvents();
        this.startAutoplay();
        this.updateProgress();
    }

    setupElements() {
        this.slides = this.slider.querySelectorAll('.shader-slide');
        this.dots = this.slider.querySelectorAll('.shader-dot');
        this.prevBtn = this.slider.querySelector('.shader-prev');
        this.nextBtn = this.slider.querySelector('.shader-next');
        this.progressBar = this.slider.querySelector('.shader-progress-bar');

        // Set total slides
        this.totalSlides = this.slides.length;

        // Extract individual slide durations from data attributes
        this.slideDurations = [];
        this.slides.forEach((slide, index) => {
            const duration = slide.getAttribute('data-duration');
            this.slideDurations[index] = duration ? parseInt(duration) * 1000 : this.options.autoplayDelay;
        });

        // Initialize first slide
        if (this.slides.length > 0) {
            this.slides[0].classList.add('active');
            if (this.dots.length > 0) {
                this.dots[0].classList.add('active');
            }
        }
    }

    setupVideo() {
        this.videos = [];

        // Get all video elements from slides
        this.slides.forEach((slide, index) => {
            const video = slide.querySelector('.shader-video');
            if (video) {
                // Ensure video is muted for autoplay
                video.muted = true;
                video.currentTime = 0;

                // Handle video load events
                video.addEventListener('loadeddata', () => {
                    console.log(`Shader video ${index + 1} loaded successfully`);
                });

                video.addEventListener('error', (e) => {
                    console.error(`Shader video ${index + 1} failed to load:`, e);
                });

                // Store video reference
                this.videos[index] = video;

                // Only play the first video initially
                if (index === 0) {
                    video.play().catch(e => {
                        console.warn('Video autoplay failed:', e);
                    });
                } else {
                    video.pause();
                }
            }
        });
    }

    bindEvents() {
        // Navigation buttons
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prevSlide());
        }

        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.nextSlide());
        }

        // Dots navigation
        this.dots.forEach((dot, index) => {
            dot.addEventListener('click', () => this.goToSlide(index));
        });

        // Touch events
        if (this.options.enableTouch) {
            this.slider.addEventListener('touchstart', (e) => this.handleTouchStart(e));
            this.slider.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        }

        // Keyboard navigation
        if (this.options.enableKeyboard) {
            document.addEventListener('keydown', (e) => this.handleKeydown(e));
        }

        // Pause on hover
        if (this.options.pauseOnHover) {
            this.slider.addEventListener('mouseenter', () => this.pauseAutoplay());
            this.slider.addEventListener('mouseleave', () => this.startAutoplay());
        }

        // Visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAutoplay();
            } else {
                this.startAutoplay();
            }
        });
    }

    nextSlide() {
        if (this.isAnimating) return;

        const nextIndex = (this.currentSlide + 1) % this.totalSlides;
        this.goToSlide(nextIndex);
    }

    prevSlide() {
        if (this.isAnimating) return;

        const prevIndex = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.goToSlide(prevIndex);
    }

    goToSlide(index) {
        if (this.isAnimating || index === this.currentSlide) return;

        this.isAnimating = true;
        const currentSlideElement = this.slides[this.currentSlide];
        const nextSlideElement = this.slides[index];

        // Handle video switching
        this.switchVideo(this.currentSlide, index);

        // Randomly change transition effect for variety
        this.randomizeTransitionEffect();

        // Update dots
        this.updateDots(index);

        // Animate slides
        this.animateSlides(currentSlideElement, nextSlideElement, index);

        // Update current slide index
        this.currentSlide = index;

        // Reset autoplay
        this.resetAutoplay();
    }

    switchVideo(currentIndex, nextIndex) {
        // Pause current video
        if (this.videos[currentIndex]) {
            this.videos[currentIndex].pause();
        }

        // Play next video
        if (this.videos[nextIndex]) {
            this.videos[nextIndex].currentTime = 0;
            this.videos[nextIndex].play().catch(e => {
                console.warn(`Failed to play video ${nextIndex + 1}:`, e);
            });
        }
    }

    randomizeTransitionEffect() {
        const effects = ['fade', 'slide', 'zoom'];
        const randomEffect = effects[Math.floor(Math.random() * effects.length)];
        this.options.transitionEffect = randomEffect;
    }

    animateSlides(currentSlide, nextSlide, nextIndex) {
        const isNext = nextIndex > this.currentSlide || (this.currentSlide === this.totalSlides - 1 && nextIndex === 0);
        const effect = this.options.transitionEffect;

        // Apply transition classes based on effect and direction
        this.applyTransitionEffect(currentSlide, nextSlide, isNext, effect);

        // Add glitch effect occasionally for dramatic transitions
        if (Math.random() < 0.4) {
            const nextVideo = this.videos[nextIndex];
            if (nextVideo) {
                nextVideo.classList.add('glitch-effect');
                setTimeout(() => {
                    nextVideo.classList.remove('glitch-effect');
                }, 300);
            }
        }

        // Remove active class from current slide with delay for smooth transition
        setTimeout(() => {
            currentSlide.classList.remove('active');
            this.clearTransitionClasses(currentSlide);
        }, 100);

        // Add active class to next slide
        setTimeout(() => {
            nextSlide.classList.add('active');
            this.clearTransitionClasses(nextSlide);
        }, 200);

        // Reset animation flag after transition
        setTimeout(() => {
            this.isAnimating = false;
            this.triggerContentAnimations(nextSlide);
        }, this.options.animationDuration);
    }

    applyTransitionEffect(currentSlide, nextSlide, isNext, effect) {
        // Clear any existing transition classes
        this.clearTransitionClasses(currentSlide);
        this.clearTransitionClasses(nextSlide);

        switch (effect) {
            case 'slide':
                if (isNext) {
                    currentSlide.classList.add('slide-out-left');
                    nextSlide.classList.add('slide-in-right');
                } else {
                    currentSlide.classList.add('slide-out-right');
                    nextSlide.classList.add('slide-in-left');
                }
                break;

            case 'zoom':
                currentSlide.classList.add('zoom-out');
                nextSlide.classList.add('zoom-in');
                break;

            case 'fade':
            default:
                currentSlide.classList.add('fade-out');
                nextSlide.classList.add('fade-in');
                break;
        }
    }

    clearTransitionClasses(slide) {
        const transitionClasses = [
            'slide-in-right', 'slide-in-left', 'slide-out-right', 'slide-out-left',
            'zoom-in', 'zoom-out', 'fade-in', 'fade-out', 'glitch-effect'
        ];

        transitionClasses.forEach(className => {
            slide.classList.remove(className);
        });
    }

    triggerContentAnimations(slide) {
        // Reset and trigger content animations
        const contentElements = slide.querySelectorAll('.shader-subtitle, .shader-title, .shader-description, .shader-buttons');

        contentElements.forEach((element, index) => {
            element.style.animation = 'none';
            element.offsetHeight; // Trigger reflow
            element.style.animation = null;
        });
    }

    updateDots(activeIndex) {
        this.dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === activeIndex);
        });
    }

    updateProgress() {
        if (!this.progressBar) return;

        const progress = ((this.currentSlide + 1) / this.totalSlides) * 100;
        this.progressBar.style.width = `${progress}%`;
    }

    startAutoplay() {
        if (!this.options.autoplay) return;

        this.pauseAutoplay();

        // Resume current video when autoplay starts
        if (this.videos && this.videos[this.currentSlide]) {
            this.videos[this.currentSlide].play().catch(e => {
                console.warn('Failed to resume video:', e);
            });
        }

        // Use individual slide duration or default
        const currentDuration = this.slideDurations[this.currentSlide] || this.options.autoplayDelay;

        this.autoplayTimer = setTimeout(() => {
            this.nextSlide();
            this.updateProgress();
        }, currentDuration);
    }

    pauseAutoplay() {
        if (this.autoplayTimer) {
            clearTimeout(this.autoplayTimer);
            this.autoplayTimer = null;
        }

        // Pause current video when autoplay is paused
        if (this.videos && this.videos[this.currentSlide]) {
            this.videos[this.currentSlide].pause();
        }
    }

    resetAutoplay() {
        if (this.options.autoplay) {
            this.startAutoplay();
        }
        this.updateProgress();
    }

    handleTouchStart(e) {
        this.touchStartX = e.changedTouches[0].screenX;
    }

    handleTouchEnd(e) {
        this.touchEndX = e.changedTouches[0].screenX;
        this.handleSwipe();
    }

    handleSwipe() {
        const swipeThreshold = 50;
        const diff = this.touchStartX - this.touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                this.nextSlide();
            } else {
                this.prevSlide();
            }
        }
    }

    handleKeydown(e) {
        switch (e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.prevSlide();
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.nextSlide();
                break;
            case ' ':
                e.preventDefault();
                if (this.autoplayTimer) {
                    this.pauseAutoplay();
                } else {
                    this.startAutoplay();
                }
                break;
        }
    }

    // Public methods
    destroy() {
        this.pauseAutoplay();

        // Pause and cleanup all videos
        if (this.videos) {
            this.videos.forEach(video => {
                if (video) {
                    video.pause();
                    video.currentTime = 0;
                }
            });
        }

        // Remove event listeners
        // This would need more comprehensive cleanup in a production environment
    }

    getCurrentSlide() {
        return this.currentSlide;
    }

    getTotalSlides() {
        return this.totalSlides;
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const shaderSlider = document.querySelector('#shaderSlider');

    if (shaderSlider) {
        // Initialize the slider
        window.shaderSliderInstance = new ShaderSlider('#shaderSlider', {
            autoplay: true,
            autoplayDelay: 12000, // 12 seconds per slide for better viewing
            animationDuration: 2000, // 2 seconds for smooth transitions
            enableTouch: true,
            enableKeyboard: true,
            pauseOnHover: true,
            transitionEffect: 'fade',
            enableParallax: false, // Disabled for better performance
            enableParticles: false // Disabled for better performance
        });
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ShaderSlider;
}
