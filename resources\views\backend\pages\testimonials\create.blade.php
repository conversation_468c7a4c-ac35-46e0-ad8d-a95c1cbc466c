@extends('backend.layouts.app')

@section('title', 'Add New Testimonial')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Add New Testimonial</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('testimonials.index') }}">Testimonials</a></li>
                    <li class="breadcrumb-item active">Add New</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Testimonial Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Client Testimonial Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('testimonials.store') }}" method="POST" enctype="multipart/form-data" id="testimonialForm">
                    @csrf
                    
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Client Information</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Name -->
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Client Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Position & Company -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="position" class="form-label">Position/Title</label>
                                                <input type="text" class="form-control @error('position') is-invalid @enderror" 
                                                       id="position" name="position" value="{{ old('position') }}" 
                                                       placeholder="e.g., CEO, Manager, Developer">
                                                @error('position')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="company" class="form-label">Company/Organization</label>
                                                <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                                       id="company" name="company" value="{{ old('company') }}" 
                                                       placeholder="e.g., ABC Corp, XYZ Ltd">
                                                @error('company')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Rating -->
                                    <div class="mb-3">
                                        <label for="rating" class="form-label">Rating <span class="text-danger">*</span></label>
                                        <select class="form-select @error('rating') is-invalid @enderror" id="rating" name="rating" required>
                                            <option value="">Select Rating</option>
                                            <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>⭐⭐⭐⭐⭐ (5 Stars)</option>
                                            <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>⭐⭐⭐⭐ (4 Stars)</option>
                                            <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>⭐⭐⭐ (3 Stars)</option>
                                            <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>⭐⭐ (2 Stars)</option>
                                            <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>⭐ (1 Star)</option>
                                        </select>
                                        @error('rating')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Testimonial Comment -->
                                    <div class="mb-3">
                                        <label for="comment" class="form-label">Testimonial Comment <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('comment') is-invalid @enderror" 
                                                  id="comment" name="comment" rows="6" maxlength="2000" required>{{ old('comment') }}</textarea>
                                        <div class="form-text">Write the client's testimonial or review (max 2000 characters)</div>
                                        @error('comment')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Sidebar -->
                        <div class="col-lg-4">
                            <!-- Client Image -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Client Photo</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="client_image" class="form-label">Upload Photo</label>
                                        <input type="file" class="form-control @error('client_image') is-invalid @enderror" 
                                               id="client_image" name="client_image" accept="image/*">
                                        <div class="form-text">Upload client photo (JPEG, PNG, JPG, GIF, WebP - Max: 5MB)</div>
                                        @error('client_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div id="image_preview" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Settings -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Settings</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                            <option value="active" {{ old('status') === 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        </select>
                                        <div class="form-text">Active testimonials will be displayed on the website</div>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Featured -->
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                                                   value="1" {{ old('featured') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="featured">
                                                Featured Testimonial
                                            </label>
                                        </div>
                                        <div class="form-text">Featured testimonials are prioritized on the homepage</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Preview -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Preview</h5>
                                </div>
                                <div class="card-body">
                                    <div id="testimonial_preview" class="text-center">
                                        <div class="mb-2">
                                            <div class="rounded-circle bg-light mx-auto d-flex align-items-center justify-content-center" 
                                                 style="width: 60px; height: 60px;" id="preview_image">
                                                <i class="ri-user-line text-muted"></i>
                                            </div>
                                        </div>
                                        <h6 id="preview_name" class="mb-1">Client Name</h6>
                                        <p id="preview_position" class="text-muted small mb-2">Position at Company</p>
                                        <div id="preview_rating" class="mb-2">⭐⭐⭐⭐⭐</div>
                                        <blockquote id="preview_comment" class="blockquote small">
                                            <p class="mb-0">"Testimonial comment will appear here..."</p>
                                        </blockquote>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('testimonials.index') }}" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Testimonials
                                </a>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Add Testimonial
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.blockquote {
    border-left: 3px solid var(--vz-primary);
    padding-left: 1rem;
    font-style: italic;
}

#testimonial_preview {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview functionality
    document.getElementById('client_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('image_preview');
        const previewImage = document.getElementById('preview_image');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" class="image-preview" alt="Client Preview">`;
                previewImage.innerHTML = `<img src="${e.target.result}" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;" alt="Client Preview">`;
            };
            reader.readAsDataURL(file);
        } else {
            preview.innerHTML = '';
            previewImage.innerHTML = '<i class="ri-user-line text-muted"></i>';
        }
    });

    // Live preview updates
    document.getElementById('name').addEventListener('input', function() {
        document.getElementById('preview_name').textContent = this.value || 'Client Name';
    });

    document.getElementById('position').addEventListener('input', updatePositionPreview);
    document.getElementById('company').addEventListener('input', updatePositionPreview);

    function updatePositionPreview() {
        const position = document.getElementById('position').value;
        const company = document.getElementById('company').value;
        let text = '';
        
        if (position && company) {
            text = `${position} at ${company}`;
        } else if (position) {
            text = position;
        } else if (company) {
            text = company;
        } else {
            text = 'Position at Company';
        }
        
        document.getElementById('preview_position').textContent = text;
    }

    document.getElementById('rating').addEventListener('change', function() {
        const rating = parseInt(this.value) || 5;
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            stars += i <= rating ? '⭐' : '☆';
        }
        document.getElementById('preview_rating').textContent = stars;
    });

    document.getElementById('comment').addEventListener('input', function() {
        const comment = this.value || 'Testimonial comment will appear here...';
        document.getElementById('preview_comment').innerHTML = `<p class="mb-0">"${comment}"</p>`;
    });

    // Character counter for comment
    const commentTextarea = document.getElementById('comment');
    if (commentTextarea) {
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        commentTextarea.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = 2000 - commentTextarea.value.length;
            counter.textContent = `${commentTextarea.value.length}/2000 characters`;
            counter.className = `form-text text-end ${remaining < 200 ? 'text-warning' : remaining < 0 ? 'text-danger' : ''}`;
        }

        commentTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Form validation
    document.getElementById('testimonialForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const comment = document.getElementById('comment').value.trim();
        const rating = document.getElementById('rating').value;
        const status = document.getElementById('status').value;

        if (!name || !comment || !rating || !status) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ri-loader-4-line me-1 spinner-border spinner-border-sm"></i>Adding...';
    });
});
</script>
@endpush
