@extends('frontend.layouts.app')

@section('title', $project->meta_title ?? $project->title . ' - Project Details')
@section('description', $project->meta_description ?? $project->short_description)
@section('keywords', $project->meta_keywords ?? $project->title)

@section('content')
<!-- Page Header -->
<div class="container-fluid page-header py-5">
    <div class="container text-center py-5">
        <h1 class="display-2 text-white mb-4 animated slideInDown">{{ $project->title }}</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb justify-content-center mb-0">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ route('projects.all') }}">Projects</a></li>
                <li class="breadcrumb-item text-white active" aria-current="page">{{ $project->title }}</li>
            </ol>
        </nav>
    </div>
</div>

<!-- Project Detail Section -->
<div class="container-fluid py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Project Images -->
            <div class="col-lg-8">
                <!-- Main Project Image -->
                <div class="project-main-image mb-4 wow fadeIn" data-wow-delay=".3s">
                    @if($project->project_image_url)
                        <img src="{{ $project->project_image_url }}" class="img-fluid w-100 rounded" 
                             alt="{{ $project->title }}" style="height: 400px; object-fit: cover;">
                    @else
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 400px;">
                            <i class="ri-image-line text-muted" style="font-size: 4rem;"></i>
                        </div>
                    @endif
                </div>

                <!-- Project Gallery -->
                @if($project->gallery_image_urls && count($project->gallery_image_urls) > 0)
                    <div class="project-gallery mb-4 wow fadeIn" data-wow-delay=".5s">
                        <h4 class="mb-3">Project Gallery</h4>
                        <div class="row g-3">
                            @foreach($project->gallery_image_urls as $index => $image)
                                <div class="col-md-4 col-sm-6">
                                    <div class="gallery-item">
                                        <img src="{{ $image }}" class="img-fluid w-100 rounded" 
                                             alt="{{ $project->title }} Gallery {{ $index + 1 }}"
                                             style="height: 200px; object-fit: cover; cursor: pointer;"
                                             onclick="openImageModal('{{ $image }}')">
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Project Description -->
                <div class="project-description wow fadeIn" data-wow-delay=".7s">
                    <h4 class="mb-3">Project Overview</h4>
                    @if($project->full_description)
                        <div class="project-content">
                            {!! nl2br(e($project->full_description)) !!}
                        </div>
                    @else
                        <p class="text-muted">{{ $project->description }}</p>
                    @endif
                </div>
            </div>

            <!-- Project Info Sidebar -->
            <div class="col-lg-4">
                <div class="project-info-sidebar">
                    <!-- Project Details Card -->
                    <div class="card mb-4 wow fadeIn" data-wow-delay=".3s">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="ri-information-line me-2"></i>Project Details</h5>
                        </div>
                        <div class="card-body">
                            @if($project->client_name)
                                <div class="detail-item mb-3">
                                    <strong><i class="ri-user-line me-2 text-primary"></i>Client:</strong>
                                    <span class="ms-2">{{ $project->client_name }}</span>
                                </div>
                            @endif

                            @if($project->category)
                                <div class="detail-item mb-3">
                                    <strong><i class="ri-folder-line me-2 text-primary"></i>Category:</strong>
                                    <span class="ms-2">{{ $project->category }}</span>
                                </div>
                            @endif

                            @if($project->start_date)
                                <div class="detail-item mb-3">
                                    <strong><i class="ri-calendar-line me-2 text-primary"></i>Duration:</strong>
                                    <span class="ms-2">
                                        {{ $project->start_date->format('M Y') }}
                                        @if($project->end_date)
                                            - {{ $project->end_date->format('M Y') }}
                                        @else
                                            - Present
                                        @endif
                                    </span>
                                </div>
                            @endif

                            <div class="detail-item mb-3">
                                <strong><i class="ri-flag-line me-2 text-primary"></i>Status:</strong>
                                <span class="ms-2">
                                    @switch($project->status)
                                        @case('completed')
                                            <span class="badge bg-success">Completed</span>
                                            @break
                                        @case('in_progress')
                                            <span class="badge bg-warning">In Progress</span>
                                            @break
                                        @case('active')
                                            <span class="badge bg-primary">Active</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ ucfirst($project->status) }}</span>
                                    @endswitch
                                </span>
                            </div>

                            @if($project->project_url)
                                <div class="detail-item mb-0">
                                    <a href="{{ $project->project_url }}" target="_blank" class="btn btn-primary w-100">
                                        <i class="ri-external-link-line me-2"></i>View Live Project
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Technologies Used -->
                    @if($project->technologies && count($project->technologies) > 0)
                        <div class="card mb-4 wow fadeIn" data-wow-delay=".5s">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="ri-code-line me-2"></i>Technologies Used</h5>
                            </div>
                            <div class="card-body">
                                <div class="technologies-list">
                                    @foreach($project->technologies as $tech)
                                        <span class="badge bg-light text-dark me-2 mb-2">{{ $tech }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Contact CTA -->
                    <div class="card wow fadeIn" data-wow-delay=".7s">
                        <div class="card-body text-center">
                            <h5 class="card-title">Interested in Similar Project?</h5>
                            <p class="card-text text-muted">Let's discuss how we can help bring your ideas to life.</p>
                            <a href="{{ route('home') }}" class="btn btn-primary">
                                <i class="ri-mail-line me-2"></i>Get In Touch
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Related Projects Section -->
<div class="container-fluid bg-light py-5">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">More Projects</h5>
            <h1>Other Projects You Might Like</h1>
        </div>
        
        <!-- This would be populated with related projects -->
        <div class="row g-4">
            <div class="col-12 text-center">
                <a href="{{ route('projects.all') }}" class="btn btn-primary btn-lg">
                    <i class="ri-eye-line me-2"></i>View All Projects
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $project->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <img id="modalImage" src="" class="img-fluid w-100" alt="">
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.project-content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.detail-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.75rem;
}

.detail-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.technologies-list .badge {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.project-info-sidebar .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.project-info-sidebar .card-header {
    border-bottom: none;
    font-weight: 600;
}

@media (max-width: 768px) {
    .project-main-image img,
    .project-main-image .bg-light {
        height: 250px !important;
    }
    
    .gallery-item img {
        height: 150px !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    modal.show();
}
</script>
@endpush
