@extends('backend.layouts.app')

@section('title', 'Create About Content - GrandTek Admin')

@push('styles')
<style>
.value-item {
    transition: all 0.3s ease;
}
.value-item.removing {
    opacity: 0;
    transform: translateX(-100%);
}
</style>
@endpush

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Create About Content</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.about.index') }}">About Section</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<form action="{{ route('admin.about.store') }}" method="POST" enctype="multipart/form-data">
    @csrf
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Basic Information</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="tittle" class="form-label">Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('tittle') is-invalid @enderror" 
                               id="tittle" name="tittle" value="{{ old('tittle') }}" required>
                        @error('tittle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subtitle</label>
                        <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" name="subtitle" value="{{ old('subtitle') }}" placeholder="About Us">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Main Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="short_description" class="form-label">Short Description</label>
                        <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                  id="short_description" name="short_description" rows="3">{{ old('short_description') }}</textarea>
                        @error('short_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="full_description" class="form-label">Full Description (For About Page)</label>
                        <textarea class="form-control @error('full_description') is-invalid @enderror" 
                                  id="full_description" name="full_description" rows="6">{{ old('full_description') }}</textarea>
                        @error('full_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Mission & Vision -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Mission & Vision</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mission" class="form-label">Our Mission</label>
                                <textarea class="form-control @error('mission') is-invalid @enderror" 
                                          id="mission" name="mission" rows="4">{{ old('mission') }}</textarea>
                                @error('mission')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vision" class="form-label">Our Vision</label>
                                <textarea class="form-control @error('vision') is-invalid @enderror" 
                                          id="vision" name="vision" rows="4">{{ old('vision') }}</textarea>
                                @error('vision')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Values -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Company Values</h4>
                </div>
                <div class="card-body">
                    <div id="values-container">
                        <div class="value-item mb-3">
                            <div class="input-group">
                                <input type="text" class="form-control" name="values[]" placeholder="Enter a company value">
                                <button type="button" class="btn btn-outline-danger remove-value" onclick="removeValue(this)">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addValue()">
                        <i class="ri-add-line me-1"></i>Add Value
                    </button>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Images -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Images</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="aboutus_img_one" class="form-label">Primary Image</label>
                        <input type="file" class="form-control @error('aboutus_img_one') is-invalid @enderror" 
                               id="aboutus_img_one" name="aboutus_img_one" accept="image/*">
                        @error('aboutus_img_one')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="aboutus_img_two" class="form-label">Secondary Image</label>
                        <input type="file" class="form-control @error('aboutus_img_two') is-invalid @enderror" 
                               id="aboutus_img_two" name="aboutus_img_two" accept="image/*">
                        @error('aboutus_img_two')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Statistics</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="years_experience" class="form-label">Years of Experience</label>
                        <input type="number" class="form-control @error('years_experience') is-invalid @enderror" 
                               id="years_experience" name="years_experience" value="{{ old('years_experience', 0) }}" min="0">
                        @error('years_experience')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="projects_completed" class="form-label">Projects Completed</label>
                        <input type="number" class="form-control @error('projects_completed') is-invalid @enderror" 
                               id="projects_completed" name="projects_completed" value="{{ old('projects_completed', 0) }}" min="0">
                        @error('projects_completed')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="happy_clients" class="form-label">Happy Clients</label>
                        <input type="number" class="form-control @error('happy_clients') is-invalid @enderror" 
                               id="happy_clients" name="happy_clients" value="{{ old('happy_clients', 0) }}" min="0">
                        @error('happy_clients')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="team_members" class="form-label">Team Members</label>
                        <input type="number" class="form-control @error('team_members') is-invalid @enderror" 
                               id="team_members" name="team_members" value="{{ old('team_members', 0) }}" min="0">
                        @error('team_members')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Button Settings -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Button Settings</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="button_text" class="form-label">Button Text</label>
                        <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                               id="button_text" name="button_text" value="{{ old('button_text', 'More Details') }}">
                        @error('button_text')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="button_link" class="form-label">Button Link</label>
                        <input type="text" class="form-control @error('button_link') is-invalid @enderror" 
                               id="button_link" name="button_link" value="{{ old('button_link', '/about') }}">
                        @error('button_link')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Status & Actions -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Status & Actions</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                            <option value="active" {{ old('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Create About Content
                        </button>
                        <a href="{{ route('admin.about.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
function addValue() {
    const container = document.getElementById('values-container');
    const valueItem = document.createElement('div');
    valueItem.className = 'value-item mb-3';
    valueItem.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" name="values[]" placeholder="Enter a company value">
            <button type="button" class="btn btn-outline-danger remove-value" onclick="removeValue(this)">
                <i class="ri-delete-bin-line"></i>
            </button>
        </div>
    `;
    container.appendChild(valueItem);
}

function removeValue(button) {
    const valueItem = button.closest('.value-item');
    const container = document.getElementById('values-container');
    if (container.children.length > 1) {
        valueItem.remove();
    }
}
</script>

@endsection
