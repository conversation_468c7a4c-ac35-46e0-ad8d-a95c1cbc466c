<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SeoSetting;
use App\Models\PageSeo;
use App\Models\BlogPost;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class SeoController extends Controller
{
    /**
     * SEO Dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_pages' => PageSeo::count(),
            'active_pages' => PageSeo::active()->count(),
            'blog_posts' => BlogPost::published()->count(),
            'seo_settings' => SeoSetting::active()->count(),
        ];

        $recentPages = PageSeo::latest()->limit(5)->get();
        $recentPosts = BlogPost::published()->latest()->limit(5)->get();

        return view('admin.seo.dashboard', compact('stats', 'recentPages', 'recentPosts'));
    }

    /**
     * SEO Settings Management
     */
    public function settings()
    {
        $groups = SeoSetting::getGroups();
        $settings = SeoSetting::active()->orderBy('group')->orderBy('sort_order')->get()->groupBy('group');

        return view('admin.seo.settings', compact('groups', 'settings'));
    }

    /**
     * Update SEO Settings
     */
    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        foreach ($request->settings as $key => $value) {
            $setting = SeoSetting::where('key', $key)->first();
            if ($setting) {
                $setting->update(['value' => $value]);
            }
        }

        SeoSetting::clearCache();

        return redirect()->back()->with('success', 'SEO settings updated successfully!');
    }

    /**
     * Create new SEO setting
     */
    public function createSetting(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|unique:seo_settings,key',
            'value' => 'nullable|string',
            'type' => 'required|string',
            'group' => 'required|string',
            'label' => 'required|string',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        SeoSetting::create($request->all());

        return redirect()->back()->with('success', 'SEO setting created successfully!');
    }

    /**
     * Page SEO Management
     */
    public function pages(Request $request)
    {
        $query = PageSeo::query();

        if ($request->has('page_type') && $request->page_type) {
            $query->where('page_type', $request->page_type);
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('page_identifier', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $pages = $query->orderBy('page_type')->orderBy('page_identifier')->paginate(20);
        $pageTypes = PageSeo::getPageTypes();

        return view('admin.seo.pages', compact('pages', 'pageTypes'));
    }

    /**
     * Show page SEO form
     */
    public function showPage($id = null)
    {
        $page = $id ? PageSeo::findOrFail($id) : new PageSeo();
        $pageTypes = PageSeo::getPageTypes();
        $changeFrequencies = PageSeo::getChangeFrequencies();

        return view('admin.seo.page-form', compact('page', 'pageTypes', 'changeFrequencies'));
    }

    /**
     * Store/Update page SEO
     */
    public function storePage(Request $request, $id = null)
    {
        $rules = [
            'page_type' => 'required|string',
            'page_identifier' => 'required|string',
            'title' => 'nullable|string|max:60',
            'description' => 'nullable|string|max:160',
            'keywords' => 'nullable|string',
            'canonical_url' => 'nullable|url',
            'index' => 'boolean',
            'follow' => 'boolean',
            'og_title' => 'nullable|string|max:60',
            'og_description' => 'nullable|string|max:160',
            'og_image' => 'nullable|url',
            'og_type' => 'nullable|string',
            'twitter_title' => 'nullable|string|max:60',
            'twitter_description' => 'nullable|string|max:160',
            'twitter_image' => 'nullable|url',
            'twitter_card' => 'nullable|string',
            'sitemap_priority' => 'nullable|numeric|between:0,1',
            'sitemap_changefreq' => 'nullable|string',
            'is_active' => 'boolean',
        ];

        if (!$id) {
            $rules['page_identifier'] = 'required|string|unique:page_seo,page_identifier,NULL,id,page_type,' . $request->page_type;
        } else {
            $rules['page_identifier'] = 'required|string|unique:page_seo,page_identifier,' . $id . ',id,page_type,' . $request->page_type;
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $data['index'] = $request->has('index');
        $data['follow'] = $request->has('follow');
        $data['is_active'] = $request->has('is_active');

        if ($id) {
            $page = PageSeo::findOrFail($id);
            $page->update($data);
            $message = 'Page SEO updated successfully!';
        } else {
            PageSeo::create($data);
            $message = 'Page SEO created successfully!';
        }

        return redirect()->route('admin.seo.pages')->with('success', $message);
    }

    /**
     * Delete page SEO
     */
    public function deletePage($id)
    {
        $page = PageSeo::findOrFail($id);
        $page->delete();

        return redirect()->back()->with('success', 'Page SEO deleted successfully!');
    }

    /**
     * Bulk actions for pages
     */
    public function bulkPages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|string|in:activate,deactivate,delete',
            'pages' => 'required|array',
            'pages.*' => 'exists:page_seo,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator);
        }

        $pages = PageSeo::whereIn('id', $request->pages);

        switch ($request->action) {
            case 'activate':
                $pages->update(['is_active' => true]);
                $message = 'Selected pages activated successfully!';
                break;
            case 'deactivate':
                $pages->update(['is_active' => false]);
                $message = 'Selected pages deactivated successfully!';
                break;
            case 'delete':
                $pages->delete();
                $message = 'Selected pages deleted successfully!';
                break;
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Generate sitemap
     */
    public function generateSitemap()
    {
        // This would trigger sitemap regeneration
        return redirect()->back()->with('success', 'Sitemap generated successfully!');
    }

    /**
     * SEO Analysis
     */
    public function analysis()
    {
        $analysis = [
            'pages_without_title' => PageSeo::whereNull('title')->orWhere('title', '')->count(),
            'pages_without_description' => PageSeo::whereNull('description')->orWhere('description', '')->count(),
            'pages_without_keywords' => PageSeo::whereNull('keywords')->orWhere('keywords', '')->count(),
            'long_titles' => PageSeo::whereRaw('LENGTH(title) > 60')->count(),
            'long_descriptions' => PageSeo::whereRaw('LENGTH(description) > 160')->count(),
            'duplicate_titles' => PageSeo::select('title', DB::raw('COUNT(*) as count'))
                ->whereNotNull('title')
                ->where('title', '!=', '')
                ->groupBy('title')
                ->having('count', '>', 1)
                ->get(),
            'duplicate_descriptions' => PageSeo::select('description', DB::raw('COUNT(*) as count'))
                ->whereNotNull('description')
                ->where('description', '!=', '')
                ->groupBy('description')
                ->having('count', '>', 1)
                ->get(),
        ];

        return view('admin.seo.analysis', compact('analysis'));
    }


}
