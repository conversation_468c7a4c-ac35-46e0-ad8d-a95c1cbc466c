@extends('backend.layouts.app')

@section('title', 'Edit Slider - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Edit Slider</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('sliders.index') }}">Sliders</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Edit Slider Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('sliders.update', $slider) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- First Slogan One -->
                        <div class="col-md-6 mb-3">
                            <label for="first_slogan_one" class="form-label">Main Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('first_slogan_one') is-invalid @enderror" 
                                   id="first_slogan_one" name="first_slogan_one" 
                                   value="{{ old('first_slogan_one', $slider->first_slogan_one) }}" required>
                            @error('first_slogan_one')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Second Slogan One -->
                        <div class="col-md-6 mb-3">
                            <label for="second_slogan_one" class="form-label">Subtitle <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('second_slogan_one') is-invalid @enderror" 
                                   id="second_slogan_one" name="second_slogan_one" 
                                   value="{{ old('second_slogan_one', $slider->second_slogan_one) }}" required>
                            @error('second_slogan_one')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Main Image -->
                    <div class="mb-3">
                        <label for="slider_one_img" class="form-label">Main Slider Image</label>
                        @if($slider->slider_one_img)
                            <div class="current-image mb-2">
                                <img src="{{ $slider->slider_one_img_url }}" alt="Current Image" class="img-thumbnail" style="max-height: 200px;">
                                <p class="text-muted small mt-1">Current image</p>
                            </div>
                        @endif
                        <input type="file" class="form-control @error('slider_one_img') is-invalid @enderror" 
                               id="slider_one_img" name="slider_one_img" accept="image/*">
                        <div class="form-text">Leave empty to keep current image. Supported formats: JPEG, PNG, JPG, GIF, WEBP. Max size: 5MB</div>
                        @error('slider_one_img')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <!-- First Slogan Two -->
                        <div class="col-md-6 mb-3">
                            <label for="first_slogan_two" class="form-label">Description Line 1 <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('first_slogan_two') is-invalid @enderror" 
                                      id="first_slogan_two" name="first_slogan_two" rows="3" required>{{ old('first_slogan_two', $slider->first_slogan_two) }}</textarea>
                            @error('first_slogan_two')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Second Slogan Two -->
                        <div class="col-md-6 mb-3">
                            <label for="second_slogan_two" class="form-label">Description Line 2 <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('second_slogan_two') is-invalid @enderror" 
                                      id="second_slogan_two" name="second_slogan_two" rows="3" required>{{ old('second_slogan_two', $slider->second_slogan_two) }}</textarea>
                            @error('second_slogan_two')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Secondary Image -->
                    <div class="mb-3">
                        <label for="slogan_two_img" class="form-label">Secondary Image</label>
                        @if($slider->slogan_two_img)
                            <div class="current-image mb-2">
                                <img src="{{ $slider->slogan_two_img_url }}" alt="Current Secondary Image" class="img-thumbnail" style="max-height: 200px;">
                                <p class="text-muted small mt-1">Current secondary image</p>
                            </div>
                        @endif
                        <input type="file" class="form-control @error('slogan_two_img') is-invalid @enderror" 
                               id="slogan_two_img" name="slogan_two_img" accept="image/*">
                        <div class="form-text">Leave empty to keep current image. Supported formats: JPEG, PNG, JPG, GIF, WEBP. Max size: 5MB</div>
                        @error('slogan_two_img')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="active" {{ old('status', $slider->status) === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status', $slider->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('sliders.index') }}" class="btn btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to List
                        </a>
                        <div>
                            <a href="{{ route('sliders.show', $slider) }}" class="btn btn-outline-info me-2">
                                <i class="ri-eye-line me-1"></i>View
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line me-1"></i>Update Slider
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Preview uploaded images
document.getElementById('slider_one_img').addEventListener('change', function(e) {
    previewImage(e.target, 'slider_one_preview');
});

document.getElementById('slogan_two_img').addEventListener('change', function(e) {
    previewImage(e.target, 'slogan_two_preview');
});

function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            let preview = document.getElementById(previewId);
            if (!preview) {
                preview = document.createElement('img');
                preview.id = previewId;
                preview.className = 'img-thumbnail mt-2';
                preview.style.maxHeight = '200px';
                input.parentNode.appendChild(preview);
            }
            preview.src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}
</script>
@endpush
