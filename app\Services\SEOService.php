<?php

namespace App\Services;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class SEOService
{
    /**
     * Generate structured data for Organization (Local Business)
     */
    public static function getOrganizationSchema(): array
    {
        return [
            "@context" => "https://schema.org",
            "@type" => "LocalBusiness",
            "name" => "GrandTek IT Solutions Kenya Limited",
            "alternateName" => "GrandTek",
            "description" => "Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions.",
            "url" => url('/'),
            "logo" => asset('assets/img/grandtek-logo.png'),
            "image" => asset('assets/img/grandtek-office.jpg'),
            "telephone" => "+***********-000",
            "email" => "<EMAIL>",
            "address" => [
                "@type" => "PostalAddress",
                "streetAddress" => "Nairobi CBD",
                "addressLocality" => "Nairobi",
                "addressRegion" => "Nairobi County",
                "postalCode" => "00100",
                "addressCountry" => "KE"
            ],
            "geo" => [
                "@type" => "GeoCoordinates",
                "latitude" => -1.286389,
                "longitude" => 36.817223
            ],
            "openingHours" => "Mo-Fr 08:00-18:00",
            "priceRange" => "$$",
            "areaServed" => [
                [
                    "@type" => "Country",
                    "name" => "Kenya"
                ],
                [
                    "@type" => "City",
                    "name" => "Nairobi"
                ],
                [
                    "@type" => "City", 
                    "name" => "Mombasa"
                ],
                [
                    "@type" => "City",
                    "name" => "Kisumu"
                ]
            ],
            "serviceArea" => [
                "@type" => "GeoCircle",
                "geoMidpoint" => [
                    "@type" => "GeoCoordinates",
                    "latitude" => -1.286389,
                    "longitude" => 36.817223
                ],
                "geoRadius" => "1000000"
            ],
            "hasOfferCatalog" => [
                "@type" => "OfferCatalog",
                "name" => "Software Development Services",
                "itemListElement" => [
                    [
                        "@type" => "Offer",
                        "itemOffered" => [
                            "@type" => "Service",
                            "name" => "Custom Software Development",
                            "description" => "Tailored software solutions for businesses"
                        ]
                    ],
                    [
                        "@type" => "Offer",
                        "itemOffered" => [
                            "@type" => "Service",
                            "name" => "Web Application Development",
                            "description" => "Modern web applications using Laravel, React, and Vue.js"
                        ]
                    ],
                    [
                        "@type" => "Offer",
                        "itemOffered" => [
                            "@type" => "Service",
                            "name" => "Mobile App Development",
                            "description" => "Native and cross-platform mobile applications"
                        ]
                    ],
                    [
                        "@type" => "Offer",
                        "itemOffered" => [
                            "@type" => "Service",
                            "name" => "Database Solutions",
                            "description" => "Database design, optimization, and management"
                        ]
                    ]
                ]
            ],
            "sameAs" => [
                "https://www.facebook.com/GrandTekKenya",
                "https://www.twitter.com/GrandTekKenya",
                "https://www.linkedin.com/company/grandtek-kenya",
                "https://www.instagram.com/grandtekkenya"
            ]
        ];
    }

    /**
     * Generate structured data for Software Application
     */
    public static function getSoftwareApplicationSchema($system): array
    {
        return [
            "@context" => "https://schema.org",
            "@type" => "SoftwareApplication",
            "name" => $system->title,
            "description" => $system->description,
            "url" => $system->system_url ?? route('system.detail', $system->slug),
            "applicationCategory" => "BusinessApplication",
            "operatingSystem" => "Web Browser",
            "offers" => [
                "@type" => "Offer",
                "price" => "0",
                "priceCurrency" => "KES",
                "availability" => "https://schema.org/InStock"
            ],
            "creator" => [
                "@type" => "Organization",
                "name" => "GrandTek IT Solutions Kenya Limited"
            ],
            "datePublished" => $system->created_at->toISOString(),
            "dateModified" => $system->updated_at->toISOString(),
            "aggregateRating" => $system->average_rating ? [
                "@type" => "AggregateRating",
                "ratingValue" => $system->average_rating,
                "ratingCount" => $system->total_ratings,
                "bestRating" => "5",
                "worstRating" => "1"
            ] : null
        ];
    }

    /**
     * Generate structured data for Service
     */
    public static function getServiceSchema($service): array
    {
        return [
            "@context" => "https://schema.org",
            "@type" => "Service",
            "name" => $service->name,
            "description" => $service->description,
            "provider" => [
                "@type" => "LocalBusiness",
                "name" => "GrandTek IT Solutions Kenya Limited",
                "url" => url('/')
            ],
            "areaServed" => [
                "@type" => "Country",
                "name" => "Kenya"
            ],
            "hasOfferCatalog" => [
                "@type" => "OfferCatalog",
                "name" => $service->name,
                "itemListElement" => [
                    [
                        "@type" => "Offer",
                        "itemOffered" => [
                            "@type" => "Service",
                            "name" => $service->name,
                            "description" => $service->description
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public static function getBreadcrumbSchema($breadcrumbs): array
    {
        $itemListElement = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $itemListElement[] = [
                "@type" => "ListItem",
                "position" => $index + 1,
                "name" => $breadcrumb['name'],
                "item" => $breadcrumb['url']
            ];
        }

        return [
            "@context" => "https://schema.org",
            "@type" => "BreadcrumbList",
            "itemListElement" => $itemListElement
        ];
    }

    /**
     * Generate FAQ structured data
     */
    public static function getFAQSchema($faqs): array
    {
        $mainEntity = [];
        
        foreach ($faqs as $faq) {
            $mainEntity[] = [
                "@type" => "Question",
                "name" => $faq['question'],
                "acceptedAnswer" => [
                    "@type" => "Answer",
                    "text" => $faq['answer']
                ]
            ];
        }

        return [
            "@context" => "https://schema.org",
            "@type" => "FAQPage",
            "mainEntity" => $mainEntity
        ];
    }

    /**
     * Generate meta description from content
     */
    public static function generateMetaDescription($content, $maxLength = 160): string
    {
        $description = strip_tags($content);
        $description = preg_replace('/\s+/', ' ', $description);
        $description = trim($description);
        
        if (strlen($description) <= $maxLength) {
            return $description;
        }
        
        return Str::limit($description, $maxLength - 3) . '...';
    }

    /**
     * Generate keywords from content
     */
    public static function generateKeywords($content, $baseKeywords = []): string
    {
        $text = strip_tags($content);
        $words = str_word_count(strtolower($text), 1);
        
        // Remove common words
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];
        
        $words = array_diff($words, $stopWords);
        $wordCounts = array_count_values($words);
        arsort($wordCounts);
        
        $keywords = array_merge($baseKeywords, array_slice(array_keys($wordCounts), 0, 10));
        
        return implode(', ', array_unique($keywords));
    }

    /**
     * Get Kenya-specific software development keywords
     */
    public static function getKenyaKeywords(): array
    {
        return [
            'software development Kenya',
            'web development Nairobi',
            'mobile app development Kenya',
            'custom software solutions Kenya',
            'IT consulting Kenya',
            'software company Kenya',
            'Laravel development Kenya',
            'React development Nairobi',
            'database solutions Kenya',
            'e-commerce development Kenya',
            'business software Kenya',
            'software developers Nairobi',
            'web design Kenya',
            'digital solutions Kenya',
            'technology services Kenya'
        ];
    }
}
