@extends('backend.layouts.app')

@section('title', 'Admin Profile - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Admin Profile</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Profile</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <div class="card profile-card-2">
            <div class="card-img-block">
                <img class="img-fluid" src="https://via.placeholder.com/800x500" alt="Card image cap" />
            </div>
            <div class="card-body pt-5">
                <img src="https://via.placeholder.com/110x110" alt="profile-image" class="profile" />
                <Label>Name:</Label>
                <h5 class="card-title">{{ Auth::user()->name ?? 'Admin User' }}</h5>
                <Label>Email:</Label>
                <h5 class="card-title">{{ Auth::user()->email ?? '<EMAIL>' }}</h5>
                <Label>Phone No:</Label>
                <h5 class="card-title">+************</h5>
                <label for="">Slogan</label>
                <p class="card-text">
                    Innovate to Create Prosperity - Your tech caring partner
                </p>
                <div class="icon-block">
                    <a href="javascript:void();"><i class="fab fa-facebook bg-facebook text-white"></i></a>
                    <a href="javascript:void();">
                        <i class="fab fa-twitter bg-twitter text-white"></i></a>
                    <a href="javascript:void();">
                        <i class="fab fa-linkedin bg-linkedin text-white"></i></a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Profile Actions</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{{ route('profile.edit') }}" class="btn btn-primary w-100">
                            <i class="ri-edit-line me-2"></i>Edit Profile
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ route('site.setting') }}" class="btn btn-info w-100">
                            <i class="ri-settings-4-line me-2"></i>Site Settings
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ route('messages') }}" class="btn btn-warning w-100">
                            <i class="ri-message-3-line me-2"></i>View Messages
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ route('dashboard') }}" class="btn btn-secondary w-100">
                            <i class="ri-dashboard-3-line me-2"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
