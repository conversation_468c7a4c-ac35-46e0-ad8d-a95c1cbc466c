@extends('backend.layouts.app')

@section('title', 'Messages - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Messages</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Messages</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Contact Messages</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Project</th>
                                <th>Message</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{-- @forelse($messages as $message)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>{{ $message->name }}</td>
                                <td>{{ $message->email }}</td>
                                <td>{{ $message->project }}</td>
                                <td>{{ Str::limit($message->message, 50) }}</td>
                                <td>{{ $message->created_at->format('M d, Y') }}</td>
                                <td>
                                    <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#messageModal{{ $message->id }}">
                                        <i class="ri-eye-line"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteMessage({{ $message->id }})">
                                        <i class="ri-delete-bin-line"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            @empty --}}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="ri-message-3-line fs-1 text-muted mb-3"></i>
                                        <h5 class="text-muted">No messages yet</h5>
                                        <p class="text-muted">Contact messages will appear here when customers reach out.</p>
                                    </div>
                                </td>
                            </tr>
                            {{-- @endforelse --}}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Message View Modals --}}
{{-- @foreach($messages ?? [] as $message)
<div class="modal fade" id="messageModal{{ $message->id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message from {{ $message->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Name:</strong> {{ $message->name }}
                    </div>
                    <div class="col-md-6">
                        <strong>Email:</strong> {{ $message->email }}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <strong>Project:</strong> {{ $message->project }}
                    </div>
                    <div class="col-md-6">
                        <strong>Date:</strong> {{ $message->created_at->format('M d, Y H:i') }}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <strong>Message:</strong>
                        <p class="mt-2">{{ $message->message }}</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="mailto:{{ $message->email }}" class="btn btn-primary">
                    <i class="ri-mail-line me-1"></i>Reply via Email
                </a>
            </div>
        </div>
    </div>
</div>
@endforeach --}}
@endsection

@push('scripts')
<script>
function deleteMessage(id) {
    if (confirm('Are you sure you want to delete this message?')) {
        // Add delete functionality here
        console.log('Delete message with ID:', id);
    }
}
</script>
@endpush
