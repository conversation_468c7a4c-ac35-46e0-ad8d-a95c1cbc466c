<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sliders', function (Blueprint $table) {
            if (!Schema::hasColumn('sliders', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('slogan_two_img');
            }
            if (!Schema::hasColumn('sliders', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('status');
            }
        });
        
        // Update existing records to have sort_order based on their ID
        $sliders = DB::table('sliders')->orderBy('id')->get();
        foreach ($sliders as $index => $slider) {
            DB::table('sliders')
                ->where('id', $slider->id)
                ->update([
                    'sort_order' => $index + 1,
                    'status' => 'active' // Set all existing sliders as active
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sliders', function (Blueprint $table) {
            if (Schema::hasColumn('sliders', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('sliders', 'sort_order')) {
                $table->dropColumn('sort_order');
            }
        });
    }
};
