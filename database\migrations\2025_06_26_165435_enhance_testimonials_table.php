<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('testimonials', function (Blueprint $table) {
            // Add new fields for better testimonial management
            if (!Schema::hasColumn('testimonials', 'position')) {
                $table->string('position')->nullable()->after('name');
            }

            if (!Schema::hasColumn('testimonials', 'company')) {
                $table->string('company')->nullable()->after('position');
            }

            if (!Schema::hasColumn('testimonials', 'rating')) {
                $table->integer('rating')->default(5)->after('company');
            }

            if (!Schema::hasColumn('testimonials', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('client_image');
            }

            if (!Schema::hasColumn('testimonials', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('status');
            }

            if (!Schema::hasColumn('testimonials', 'featured')) {
                $table->boolean('featured')->default(false)->after('sort_order');
            }

            // Make comment field longer for detailed testimonials
            $table->text('comment')->change();

            // Make client_image nullable
            $table->string('client_image')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('testimonials', function (Blueprint $table) {
            // Remove added columns
            $columnsToRemove = ['position', 'company', 'rating', 'status', 'sort_order', 'featured'];
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('testimonials', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
