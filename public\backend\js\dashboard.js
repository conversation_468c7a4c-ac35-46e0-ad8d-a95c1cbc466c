/**
 * Dashboard JavaScript
 * Charts and interactive components for the admin dashboard
 */

class Dashboard {
    constructor() {
        this.charts = {};
        this.init();
    }

    init() {
        this.initCharts();
        this.initInteractiveElements();
    }

    initCharts() {
        // Revenue Chart
        this.initRevenueChart();
        
        // Sales by Location Chart (if needed)
        this.initLocationChart();
    }

    initRevenueChart() {
        const ctx = document.getElementById('customer_impression_charts');
        if (!ctx) return;

        // Sample data - replace with real data from your backend
        const data = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [
                {
                    label: 'Orders',
                    data: [65, 59, 80, 81, 56, 55, 40, 65, 59, 80, 81, 56],
                    borderColor: 'rgb(64, 81, 137)',
                    backgroundColor: 'rgba(64, 81, 137, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Earnings',
                    data: [28, 48, 40, 19, 86, 27, 90, 28, 48, 40, 19, 86],
                    borderColor: 'rgb(10, 179, 156)',
                    backgroundColor: 'rgba(10, 179, 156, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Refunds',
                    data: [12, 19, 3, 5, 2, 3, 15, 12, 19, 3, 5, 2],
                    borderColor: 'rgb(240, 101, 72)',
                    backgroundColor: 'rgba(240, 101, 72, 0.1)',
                    tension: 0.4,
                    fill: true
                }
            ]
        };

        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    },
                    y: {
                        display: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value + 'k';
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 6
                    }
                }
            }
        };

        this.charts.revenue = new Chart(ctx, config);
    }

    initLocationChart() {
        // This would be for a more complex location-based chart
        // For now, we'll use the static data shown in the template
        console.log('Location chart initialized with static data');
    }

    initInteractiveElements() {
        // Initialize date picker if available
        this.initDatePicker();
        
        // Initialize table interactions
        this.initTableInteractions();
        
        // Initialize card animations
        this.initCardAnimations();
    }

    initDatePicker() {
        const datePicker = document.querySelector('.dash-filter-picker');
        if (datePicker) {
            // Simple date range picker implementation
            datePicker.addEventListener('click', () => {
                // You can integrate with a date picker library like Flatpickr here
                console.log('Date picker clicked');
            });
        }
    }

    initTableInteractions() {
        // Add sorting functionality to tables
        const tableHeaders = document.querySelectorAll('th[data-sort]');
        tableHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(header);
            });
        });

        // Add row hover effects
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.backgroundColor = 'var(--vz-light)';
            });
            row.addEventListener('mouseleave', () => {
                row.style.backgroundColor = '';
            });
        });
    }

    sortTable(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = header.classList.contains('sort-asc');

        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return bValue.localeCompare(aValue, undefined, { numeric: true });
            } else {
                return aValue.localeCompare(bValue, undefined, { numeric: true });
            }
        });

        // Clear all sort classes
        header.parentNode.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });

        // Add appropriate sort class
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');

        // Reorder rows
        rows.forEach(row => tbody.appendChild(row));
    }

    initCardAnimations() {
        // Add stagger animation to cards
        const cards = document.querySelectorAll('.card-animate');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in-up');
        });
    }

    // Update chart data (for real-time updates)
    updateChartData(chartName, newData) {
        if (this.charts[chartName]) {
            this.charts[chartName].data = newData;
            this.charts[chartName].update();
        }
    }

    // Refresh dashboard data
    refreshDashboard() {
        // This would typically fetch new data from your Laravel backend
        console.log('Refreshing dashboard data...');
        
        // Example of how you might fetch data
        /*
        fetch('/api/dashboard-data')
            .then(response => response.json())
            .then(data => {
                this.updateCounters(data.counters);
                this.updateChartData('revenue', data.chartData);
            })
            .catch(error => {
                console.error('Error fetching dashboard data:', error);
            });
        */
    }

    updateCounters(data) {
        // Update counter values with new data
        Object.keys(data).forEach(key => {
            const counter = document.querySelector(`[data-counter="${key}"]`);
            if (counter) {
                const countUp = new CountUp(counter, data[key]);
                countUp.start();
            }
        });
    }

    // Export chart as image
    exportChart(chartName, filename = 'chart.png') {
        if (this.charts[chartName]) {
            const url = this.charts[chartName].toBase64Image();
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            link.click();
        }
    }

    // Resize charts when window resizes
    handleResize() {
        Object.values(this.charts).forEach(chart => {
            chart.resize();
        });
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
    
    // Handle window resize for charts
    window.addEventListener('resize', () => {
        if (window.dashboard) {
            window.dashboard.handleResize();
        }
    });
    
    // Auto-refresh dashboard every 5 minutes (optional)
    /*
    setInterval(() => {
        if (window.dashboard) {
            window.dashboard.refreshDashboard();
        }
    }, 300000); // 5 minutes
    */
});

// Export for global access
window.Dashboard = Dashboard;
