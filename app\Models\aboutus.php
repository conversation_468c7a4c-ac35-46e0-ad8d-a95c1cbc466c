<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class aboutus extends Model
{
    use HasFactory;

    protected $fillable = [
        'aboutus_img_one',
        'aboutus_img_two',
        'tittle',
        'subtitle',
        'description',
        'short_description',
        'full_description',
        'mission',
        'vision',
        'values',
        'years_experience',
        'projects_completed',
        'happy_clients',
        'team_members',
        'button_text',
        'button_link',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'values' => 'array',
        'years_experience' => 'integer',
        'projects_completed' => 'integer',
        'happy_clients' => 'integer',
        'team_members' => 'integer',
    ];

    /**
     * Get the full URL for aboutus_img_one
     */
    public function getAboutusImgOneUrlAttribute()
    {
        if ($this->aboutus_img_one) {
            // Check if it's already a full URL
            if (filter_var($this->aboutus_img_one, FILTER_VALIDATE_URL)) {
                return $this->aboutus_img_one;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->aboutus_img_one)) {
                return Storage::url($this->aboutus_img_one);
            }
            // Fallback to asset path
            return asset($this->aboutus_img_one);
        }
        return asset('assets/img/about.jpg'); // Default image
    }

    /**
     * Get the full URL for aboutus_img_two
     */
    public function getAboutusImgTwoUrlAttribute()
    {
        if ($this->aboutus_img_two) {
            // Check if it's already a full URL
            if (filter_var($this->aboutus_img_two, FILTER_VALIDATE_URL)) {
                return $this->aboutus_img_two;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->aboutus_img_two)) {
                return Storage::url($this->aboutus_img_two);
            }
            // Fallback to asset path
            return asset($this->aboutus_img_two);
        }
        return asset('assets/img/cctv.jpg'); // Default image
    }

    /**
     * Scope to get only active about content
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get formatted values as HTML list
     */
    public function getFormattedValuesAttribute()
    {
        if (!$this->values || !is_array($this->values)) {
            return '';
        }

        $html = '<ul class="list-unstyled">';
        foreach ($this->values as $value) {
            $html .= '<li><i class="fas fa-check text-primary me-2"></i>' . htmlspecialchars($value) . '</li>';
        }
        $html .= '</ul>';

        return $html;
    }

    /**
     * Get statistics array
     */
    public function getStatisticsAttribute()
    {
        return [
            [
                'icon' => 'fas fa-calendar-alt',
                'number' => $this->years_experience,
                'label' => 'Years Experience',
                'suffix' => '+'
            ],
            [
                'icon' => 'fas fa-project-diagram',
                'number' => $this->projects_completed,
                'label' => 'Projects Completed',
                'suffix' => '+'
            ],
            [
                'icon' => 'fas fa-smile',
                'number' => $this->happy_clients,
                'label' => 'Happy Clients',
                'suffix' => '+'
            ],
            [
                'icon' => 'fas fa-users',
                'number' => $this->team_members,
                'label' => 'Team Members',
                'suffix' => ''
            ]
        ];
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Clean up files when deleting
        static::deleting(function ($about) {
            if ($about->aboutus_img_one && Storage::disk('public')->exists($about->aboutus_img_one)) {
                Storage::disk('public')->delete($about->aboutus_img_one);
            }
            if ($about->aboutus_img_two && Storage::disk('public')->exists($about->aboutus_img_two)) {
                Storage::disk('public')->delete($about->aboutus_img_two);
            }
        });
    }
}
