<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('homepage_sliders', function (Blueprint $table) {
            // Add display duration column
            if (!Schema::hasColumn('homepage_sliders', 'display_duration')) {
                $table->integer('display_duration')->default(12)->after('animation_settings')->comment('Duration in seconds for this slide');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('homepage_sliders', function (Blueprint $table) {
            if (Schema::hasColumn('homepage_sliders', 'display_duration')) {
                $table->dropColumn('display_duration');
            }
        });
    }
};
