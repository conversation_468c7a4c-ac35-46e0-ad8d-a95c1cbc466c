@extends('backend.layouts.app')

@section('title', 'About Section Management - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">About Section Management</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">About Section</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">
                    <i class="ri-information-line me-2"></i>About Content Management
                </h4>
                <div>
                    @if(!$about)
                        <a href="{{ route('admin.about.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create About Content
                        </a>
                    @else
                        <a href="{{ route('admin.about.edit', $about->id) }}" class="btn btn-primary">
                            <i class="ri-edit-line me-1"></i>Edit Content
                        </a>
                    @endif
                </div>
            </div>
            <div class="card-body">
                @if($about)
                    <div class="row">
                        <!-- Content Preview -->
                        <div class="col-lg-8">
                            <div class="about-preview">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h5 class="text-primary">{{ $about->subtitle ?? 'About Us' }}</h5>
                                        <h2 class="mb-3">{{ $about->tittle }}</h2>
                                    </div>
                                    <div class="status-badge">
                                        <span class="badge {{ $about->status === 'active' ? 'bg-success' : 'bg-secondary' }}">
                                            {{ ucfirst($about->status) }}
                                        </span>
                                    </div>
                                </div>

                                <div class="content-section mb-4">
                                    <p class="text-muted">{{ Str::limit($about->description, 200) }}</p>
                                    @if($about->short_description)
                                        <p class="text-muted">{{ Str::limit($about->short_description, 150) }}</p>
                                    @endif
                                </div>

                                <!-- Statistics -->
                                @if($about->years_experience || $about->projects_completed || $about->happy_clients || $about->team_members)
                                    <div class="statistics-section mb-4">
                                        <h6 class="mb-3">Statistics</h6>
                                        <div class="row g-3">
                                            @foreach($about->statistics as $stat)
                                                @if($stat['number'] > 0)
                                                    <div class="col-md-3 col-sm-6">
                                                        <div class="stat-card text-center p-3 border rounded">
                                                            <i class="{{ $stat['icon'] }} text-primary fs-4 mb-2"></i>
                                                            <h4 class="mb-1">{{ $stat['number'] }}{{ $stat['suffix'] }}</h4>
                                                            <small class="text-muted">{{ $stat['label'] }}</small>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                @endif

                                <!-- Values -->
                                @if($about->values && count($about->values) > 0)
                                    <div class="values-section mb-4">
                                        <h6 class="mb-3">Our Values</h6>
                                        <div class="row">
                                            @foreach($about->values as $value)
                                                <div class="col-md-6 mb-2">
                                                    <i class="fas fa-check text-primary me-2"></i>{{ $value }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif

                                <!-- Mission & Vision -->
                                @if($about->mission || $about->vision)
                                    <div class="mission-vision-section">
                                        <div class="row">
                                            @if($about->mission)
                                                <div class="col-md-6 mb-3">
                                                    <h6 class="text-primary">Our Mission</h6>
                                                    <p class="text-muted small">{{ Str::limit($about->mission, 100) }}</p>
                                                </div>
                                            @endif
                                            @if($about->vision)
                                                <div class="col-md-6 mb-3">
                                                    <h6 class="text-primary">Our Vision</h6>
                                                    <p class="text-muted small">{{ Str::limit($about->vision, 100) }}</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Images & Actions -->
                        <div class="col-lg-4">
                            <div class="images-section mb-4">
                                <h6 class="mb-3">Images</h6>
                                <div class="row g-2">
                                    <div class="col-12">
                                        <div class="image-preview">
                                            <img src="{{ $about->aboutus_img_one_url }}" 
                                                 alt="About Image 1" 
                                                 class="img-fluid rounded"
                                                 style="height: 120px; width: 100%; object-fit: cover;">
                                            <small class="text-muted d-block mt-1">Primary Image</small>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="image-preview">
                                            <img src="{{ $about->aboutus_img_two_url }}" 
                                                 alt="About Image 2" 
                                                 class="img-fluid rounded"
                                                 style="height: 120px; width: 100%; object-fit: cover;">
                                            <small class="text-muted d-block mt-1">Secondary Image</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="actions-section">
                                <div class="d-grid gap-2">
                                    <a href="{{ route('admin.about.edit', $about->id) }}" class="btn btn-primary">
                                        <i class="ri-edit-line me-1"></i>Edit Content
                                    </a>
                                    <button type="button" class="btn btn-outline-{{ $about->status === 'active' ? 'warning' : 'success' }}" 
                                            onclick="toggleStatus({{ $about->id }})">
                                        <i class="ri-toggle-line me-1"></i>
                                        {{ $about->status === 'active' ? 'Deactivate' : 'Activate' }}
                                    </button>
                                    <a href="/about" target="_blank" class="btn btn-outline-info">
                                        <i class="ri-external-link-line me-1"></i>Preview Page
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="ri-information-line display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No About Content Found</h5>
                        <p class="text-muted mb-4">Create your first about content to get started.</p>
                        <a href="{{ route('admin.about.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Create About Content
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if($about)
<script>
function toggleStatus(id) {
    fetch(`/admin/about/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating status');
    });
}
</script>
@endif

@endsection
