<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aboutuses', function (Blueprint $table) {
            // Make image fields nullable
            if (Schema::hasColumn('aboutuses', 'aboutus_img_one')) {
                $table->string('aboutus_img_one')->nullable()->change();
            }
            
            if (Schema::hasColumn('aboutuses', 'aboutus_img_two')) {
                $table->string('aboutus_img_two')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aboutuses', function (Blueprint $table) {
            // Revert image fields to non-nullable (only if safe to do so)
            if (Schema::hasColumn('aboutuses', 'aboutus_img_one')) {
                $table->string('aboutus_img_one')->nullable(false)->change();
            }
            
            if (Schema::hasColumn('aboutuses', 'aboutus_img_two')) {
                $table->string('aboutus_img_two')->nullable(false)->change();
            }
        });
    }
};
