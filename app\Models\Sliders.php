<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Sliders extends Model
{
    use HasFactory;

    protected $fillable = [
        'first_slogan_one',
        'second_slogan_one',
        'slider_one_img',
        'first_slogan_two',
        'second_slogan_two',
        'slogan_two_img',
        'status',
        'sort_order',
    ];

    /**
     * Get the full URL for slider_one_img
     */
    public function getSliderOneImgUrlAttribute()
    {
        if ($this->slider_one_img) {
            // Check if it's already a full URL
            if (filter_var($this->slider_one_img, FILTER_VALIDATE_URL)) {
                return $this->slider_one_img;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->slider_one_img)) {
                return Storage::url($this->slider_one_img);
            }
            // Fallback to asset path
            return asset($this->slider_one_img);
        }
        return null;
    }

    /**
     * Get the full URL for slogan_two_img
     */
    public function getSloganTwoImgUrlAttribute()
    {
        if ($this->slogan_two_img) {
            // Check if it's already a full URL
            if (filter_var($this->slogan_two_img, FILTER_VALIDATE_URL)) {
                return $this->slogan_two_img;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->slogan_two_img)) {
                return Storage::url($this->slogan_two_img);
            }
            // Fallback to asset path
            return asset($this->slogan_two_img);
        }
        return null;
    }

    /**
     * Scope to get only active sliders
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get sliders ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $status = $this->status ?? 'active';
        $class = $status === 'active' ? 'bg-success' : 'bg-secondary';
        $text = ucfirst($status);
        return "<span class='badge {$class}'>{$text}</span>";
    }

    /**
     * Toggle slider status
     */
    public function toggleStatus()
    {
        $currentStatus = $this->status ?? 'active';
        $this->status = $currentStatus === 'active' ? 'inactive' : 'active';
        return $this->save();
    }

    /**
     * Reorder sliders
     */
    public static function reorder(array $order)
    {
        foreach ($order as $index => $id) {
            static::where('id', $id)->update(['sort_order' => $index + 1]);
        }
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-assign sort order when creating
        static::creating(function ($slider) {
            try {
                if (is_null($slider->sort_order)) {
                    // Check if sort_order column exists before querying
                    if (\Schema::hasColumn('sliders', 'sort_order')) {
                        $slider->sort_order = static::max('sort_order') + 1;
                    } else {
                        $slider->sort_order = 1;
                    }
                }
                // Set default status if not provided
                if (is_null($slider->status)) {
                    $slider->status = 'active';
                }
            } catch (\Exception $e) {
                // If there's an error (like column doesn't exist), set defaults
                $slider->sort_order = 1;
                $slider->status = 'active';
            }
        });
    }
}
