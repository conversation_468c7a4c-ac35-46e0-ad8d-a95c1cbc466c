@if($about)
<div class="container-fluid py-5 my-5" id="aboutus">
    <div class="container pt-5">
        <div class="row g-5">
            <div class="col-lg-5 col-md-6 col-sm-12 wow fadeIn" data-wow-delay=".3s">
                <div class="h-100 position-relative">
                    <img src="{{ $about['aboutus_img_one_url'] }}"
                         class="img-fluid w-75 rounded"
                         alt="{{ $about['tittle'] ?? 'About Us' }}"
                         style="width: 500px; height: 450px; margin-bottom: 25%;">
                    <div class="position-absolute w-75" style="top: 25%; left: 25%;">
                        <img src="{{ $about['aboutus_img_two_url'] }}"
                             class="img-fluid w-75 rounded"
                             alt="Secondary Image"
                             style="width: 800px; height: 400px;">
                    </div>
                </div>
            </div>
            <div class="col-lg-7 col-md-4 col-sm-12 wow fadeIn" data-wow-delay=".5s">
                <h5 class="text-primary">{{ $about['subtitle'] ?? 'About Us' }}</h5>
                <h1 class="mb-4">{{ $about['tittle'] }}</h1>
                <p>{{ $about['description'] }}</p>

                @if($about['short_description'])
                    <p>{{ $about['short_description'] }}</p>
                @endif

                <!-- Statistics Row -->
                @if(isset($about['statistics']) && count($about['statistics']) > 0)
                    <div class="row g-3 mb-4">
                        @foreach($about['statistics'] as $stat)
                            @if($stat['number'] > 0)
                                <div class="col-6 col-md-3">
                                    <div class="text-center">
                                        <i class="{{ $stat['icon'] }} text-primary fs-4 mb-2"></i>
                                        <h4 class="text-primary mb-1">{{ $stat['number'] }}{{ $stat['suffix'] }}</h4>
                                        <small class="text-muted">{{ $stat['label'] }}</small>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                @endif

                <a href="{{ $about['button_link'] ?? '/about' }}"
                   class="btn btn-secondary rounded-pill px-5 py-3 text-white">
                    {{ $about['button_text'] ?? 'More Details' }}
                </a>
            </div>
        </div>
    </div>
</div>
@endif
