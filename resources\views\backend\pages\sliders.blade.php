@extends('backend.layouts.app')

@section('title', 'Sliders & About - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Sliders & About Section</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Sliders & About</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Homepage Sliders</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('store.sliders') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <h6 class="mb-3">First Slider</h6>
                    <div class="mb-3">
                        <label for="first_slogan_one" class="form-label">First Slogan</label>
                        <input type="text" class="form-control @error('first_slogan_one') is-invalid @enderror" 
                               id="first_slogan_one" name="first_slogan_one" 
                               placeholder="Enter first slogan" value="{{ old('first_slogan_one') }}" required>
                        @error('first_slogan_one')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="second_slogan_one" class="form-label">Second Slogan</label>
                        <input type="text" class="form-control @error('second_slogan_one') is-invalid @enderror" 
                               id="second_slogan_one" name="second_slogan_one" 
                               placeholder="Enter second slogan" value="{{ old('second_slogan_one') }}" required>
                        @error('second_slogan_one')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="slider_one_img" class="form-label">First Slider Image</label>
                        <input type="file" class="form-control @error('slider_one_img') is-invalid @enderror" 
                               id="slider_one_img" name="slider_one_img" accept="image/*" required>
                        @error('slider_one_img')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <hr>
                    <h6 class="mb-3">Second Slider</h6>
                    <div class="mb-3">
                        <label for="first_slogan_two" class="form-label">First Slogan</label>
                        <input type="text" class="form-control @error('first_slogan_two') is-invalid @enderror" 
                               id="first_slogan_two" name="first_slogan_two" 
                               placeholder="Enter first slogan" value="{{ old('first_slogan_two') }}" required>
                        @error('first_slogan_two')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="second_slogan_two" class="form-label">Second Slogan</label>
                        <input type="text" class="form-control @error('second_slogan_two') is-invalid @enderror" 
                               id="second_slogan_two" name="second_slogan_two" 
                               placeholder="Enter second slogan" value="{{ old('second_slogan_two') }}" required>
                        @error('second_slogan_two')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="slider_two_img" class="form-label">Second Slider Image</label>
                        <input type="file" class="form-control @error('slider_two_img') is-invalid @enderror" 
                               id="slider_two_img" name="slider_two_img" accept="image/*" required>
                        @error('slider_two_img')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Update Sliders
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">About Us Section</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('store.aboutus') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="aboutus_img_one" class="form-label">About Us Image 1</label>
                        <input type="file" class="form-control @error('aboutus_img_one') is-invalid @enderror" 
                               id="aboutus_img_one" name="aboutus_img_one" accept="image/*" required>
                        @error('aboutus_img_one')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="aboutus_img_two" class="form-label">About Us Image 2</label>
                        <input type="file" class="form-control @error('aboutus_img_two') is-invalid @enderror" 
                               id="aboutus_img_two" name="aboutus_img_two" accept="image/*" required>
                        @error('aboutus_img_two')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="header_tittle" class="form-label">About Us Title</label>
                        <input type="text" class="form-control @error('header_tittle') is-invalid @enderror" 
                               id="header_tittle" name="header_tittle" 
                               placeholder="Enter about us title" value="{{ old('header_tittle') }}" required>
                        @error('header_tittle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="company_description" class="form-label">Company Description</label>
                        <textarea class="form-control @error('company_description') is-invalid @enderror" 
                                  id="company_description" name="company_description" rows="4" 
                                  placeholder="Enter company description" required>{{ old('company_description') }}</textarea>
                        @error('company_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Update About Us
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Content Guidelines</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Tips:</h6>
                    <ul class="mb-0">
                        <li>Use high-quality images for sliders</li>
                        <li>Keep slogans concise and impactful</li>
                        <li>About us content should be engaging</li>
                        <li>Images should be optimized for web</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
