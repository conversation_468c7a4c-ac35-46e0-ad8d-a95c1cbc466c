@extends('auth.layouts.auth')

@section('title', 'Login - GrandTek Admin')

@section('content')
<div class="auth-card">
    <!-- Logo -->
    <div class="auth-logo">
        <img src="{{ asset('backend/images/logo-icon.png') }}" alt="GrandTek Logo">
    </div>

    <!-- Header -->
    <div class="auth-card-header">
        <h1 class="auth-title">SIGN IN</h1>
    </div>

    <!-- Body -->
    <div class="auth-card-body">
        <!-- Session Status -->
        @if (session('status'))
            <div class="alert alert-success">
                {{ session('status') }}
            </div>
        @endif

        <!-- Validation Errors -->
        @if ($errors->any())
            <div class="alert alert-danger">
                @foreach ($errors->all() as $error)
                    <div>{{ $error }}</div>
                @endforeach
            </div>
        @endif

        <form method="POST" action="{{ route('login') }}" id="loginForm">
            @csrf

            <!-- Email Address -->
            <div class="form-group">
                <input type="email"
                       id="email"
                       name="email"
                       class="form-control @error('email') is-invalid @enderror"
                       placeholder="Email"
                       value="{{ old('email') }}"
                       required
                       autofocus
                       autocomplete="username">
                @error('email')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password -->
            <div class="form-group">
                <div class="input-group">
                    <input type="password"
                           id="password"
                           name="password"
                           class="form-control @error('password') is-invalid @enderror"
                           placeholder="Password"
                           required
                           autocomplete="current-password">
                    <button type="button" id="togglePassword">
                        <i class="ri-eye-line" id="toggleIcon"></i>
                    </button>
                </div>
                @error('password')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Social Login -->
            <div class="social-login">
                <div class="social-icons">
                    <a href="#" class="social-icon">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-icon">
                        <i class="ri-twitter-fill"></i>
                    </a>
                    <a href="#" class="social-icon">
                        <i class="ri-google-fill"></i>
                    </a>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn-auth">
                <i class="ri-arrow-right-line"></i>
            </button>

            <!-- Remember Me & Forgot Password -->
            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}" class="forgot-password">
                    Forget Your Password ?
                </a>
            @endif
        </form>
    </div>

    <!-- Footer -->
    <div class="auth-footer">
        <p class="mb-0">
            Don't have an account?
            <a href="{{ route('register') }}" class="auth-link">Sign Up here</a>
        </p>
    </div>
</div>

@push('scripts')
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'ri-eye-off-line';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'ri-eye-line';
        }
    });

    // Form validation
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email || !password) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            return false;
        }
    });
</script>
@endpush
@endsection
