<?php

namespace App\Http\Controllers;

use App\Models\HomepageSlider;
use App\Models\Sliders;
use App\Models\BlogPost;
use App\Http\Controllers\AboutController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class FrontendController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        try {
            // Get active sliders ordered by sort_order from homepage_sliders table
            $sliders = HomepageSlider::active()->ordered()->get();

            // Transform sliders for frontend use with backward compatibility
            $sliders = $sliders->map(function ($slider) {
                return [
                    'id' => $slider->id,
                    // Map new fields to old field names for backward compatibility
                    'first_slogan_one' => $slider->title,
                    'second_slogan_one' => $slider->subtitle,
                    'first_slogan_two' => $slider->description,
                    'second_slogan_two' => $slider->secondary_description ?? '',
                    'slider_one_img_url' => $slider->image_url,
                    'slogan_two_img_url' => $slider->secondary_image_path ? asset('storage/' . $slider->secondary_image_path) : null,
                    // New enhanced fields
                    'title' => $slider->title,
                    'subtitle' => $slider->subtitle,
                    'description' => $slider->description,
                    'button_text' => $slider->button_text,
                    'button_link' => $slider->button_link,
                    'media_type' => $slider->media_type,
                    'image_url' => $slider->image_url,
                    'video_url' => $slider->video_url,
                    'media_url' => $slider->media_url,
                    'background_color' => $slider->background_color ?? 'transparent',
                    'text_color' => $slider->text_color ?? '#ffffff',
                    'text_alignment' => $slider->text_alignment ?? 'center',
                    'display_duration' => $slider->display_duration ?? 12,
                    'status' => $slider->status ?? 'active',
                    'sort_order' => $slider->sort_order ?? 0,
                    'animation_settings' => $this->getSliderAnimationSettings($slider),
                    'created_at' => $slider->created_at,
                    'updated_at' => $slider->updated_at,
                ];
            });
        } catch (\Exception $e) {
            // If there's an error, try fallback to old sliders table
            try {
                $sliders = Sliders::active()->ordered()->get();
                $sliders = $sliders->map(function ($slider) {
                    return [
                        'id' => $slider->id,
                        'first_slogan_one' => $slider->first_slogan_one,
                        'second_slogan_one' => $slider->second_slogan_one,
                        'slider_one_img_url' => $slider->slider_one_img_url,
                        'first_slogan_two' => $slider->first_slogan_two,
                        'second_slogan_two' => $slider->second_slogan_two,
                        'slogan_two_img_url' => $slider->slogan_two_img_url,
                        'background_color' => 'transparent',
                        'text_color' => '#ffffff',
                        'text_alignment' => 'center',
                        'status' => $slider->status ?? 'active',
                        'sort_order' => $slider->sort_order ?? 0,
                        'created_at' => $slider->created_at,
                        'updated_at' => $slider->updated_at,
                    ];
                });
            } catch (\Exception $fallbackError) {
                // If both fail, return empty collection
                $sliders = collect([]);
            }
        }

        // Get about data for homepage
        $aboutController = new AboutController();
        $about = $aboutController->getHomepageData();

        // Get services data for homepage
        $servicesController = new \App\Http\Controllers\ServicesController();
        $services = $servicesController->getHomepageData();

        // Get projects data for homepage
        $projectController = new \App\Http\Controllers\ProjectController();
        $projects = $projectController->getHomepageData();

        // Get team data for homepage
        $teamController = new \App\Http\Controllers\TeamController();
        $teams = $teamController->getHomepageData();

        // Get testimonials data for homepage
        $testimonialController = new \App\Http\Controllers\TestimonialController();
        $testimonials = $testimonialController->getHomepageData();

        // Get systems data for homepage
        $systemController = new \App\Http\Controllers\SystemController();
        $systems = $systemController->getHomepageData();

        // Get blog data for homepage
        $blogs = $this->getHomepageBlogData();

        return view('frontend.index', compact('sliders', 'about', 'services', 'projects', 'teams', 'testimonials', 'systems', 'blogs'));
    }

    /**
     * Get blog data for homepage
     */
    public function getHomepageBlogData()
    {
        // Get latest 3 published blog posts for homepage
        $blogs = BlogPost::published()->latest()->take(3)->get();

        if ($blogs->isEmpty()) {
            return null;
        }

        return $blogs->map(function ($blog) {
            return [
                'id' => $blog->id,
                'title' => $blog->title,
                'slug' => $blog->slug,
                'excerpt' => $blog->excerpt,
                'featured_image_url' => $blog->featured_image_url,
                'author_name' => $blog->author_name ?: 'Administrator',
                'formatted_published_date' => $blog->formatted_published_date,
                'published_at' => $blog->published_at,
                'categories' => $blog->categories,
                'tags' => $blog->tags,
                'views_count' => $blog->views_count,
                'reading_time' => $blog->reading_time,
                'url' => route('blog.show', $blog->slug),
            ];
        });
    }

    /**
     * Get sliders for AJAX requests
     */
    public function getSliders()
    {
        try {
            $sliders = HomepageSlider::active()->ordered()->get();

            $sliders = $sliders->map(function ($slider) {
                return [
                    'id' => $slider->id,
                    'title' => $slider->title,
                    'subtitle' => $slider->subtitle,
                    'description' => $slider->description,
                    'button_text' => $slider->button_text,
                    'button_link' => $slider->button_link,
                    'media_type' => $slider->media_type,
                    'image_url' => $slider->image_url,
                    'video_url' => $slider->video_url,
                    'media_url' => $slider->media_url,
                    'background_color' => $slider->background_color,
                    'text_color' => $slider->text_color,
                    'text_alignment' => $slider->text_alignment,
                    'animation_settings' => $this->getSliderAnimationSettings($slider),
                ];
            });
        } catch (\Exception $e) {
            // Return empty array if there's an error
            $sliders = collect([]);
        }

        return response()->json($sliders);
    }

    /**
     * Store contact form message
     */
    public function storeMessage(Request $request)
    {
        // This method can be moved from BackendController if needed
        // For now, keeping the existing route structure
        return app(BackendController::class)->storeMessage($request);
    }

    /**
     * Safely get animation settings for a slider
     */
    private function getSliderAnimationSettings($slider)
    {
        $defaults = [
            'title_animation' => 'fadeInUp',
            'subtitle_animation' => 'fadeInLeft',
            'description_animation' => 'fadeInRight',
            'button_animation' => 'fadeInUp',
            'duration' => 1000,
            'delay' => 200,
        ];

        try {
            // Get the raw attribute value
            $rawValue = $slider->getAttributes()['animation_settings'] ?? null;

            if (empty($rawValue)) {
                return $defaults;
            }

            if (is_string($rawValue)) {
                $decoded = json_decode($rawValue, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    return array_merge($defaults, $decoded);
                }
            } elseif (is_array($rawValue)) {
                return array_merge($defaults, $rawValue);
            }
        } catch (\Exception $e) {
            // Log error if needed
            Log::warning('Error parsing animation settings for slider ' . $slider->id . ': ' . $e->getMessage());
        }

        return $defaults;
    }
}
