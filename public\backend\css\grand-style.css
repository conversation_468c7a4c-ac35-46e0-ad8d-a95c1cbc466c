/* ===== Grand-Style Admin Dashboard CSS ===== */

/* CSS Variables for Theme Customization - Grand Style */
:root {
  /* Primary Colors - Grand Theme */
  --vz-primary: #556ee6;
  --vz-primary-rgb: 85, 110, 230;
  --vz-secondary: #6c757d;
  --vz-success: #34c38f;
  --vz-info: #50a5f1;
  --vz-warning: #f1b44c;
  --vz-danger: #f46a6a;
  --vz-light: #f8f9fa;
  --vz-dark: #343a40;

  /* Background Colors - Grand Style */
  --vz-body-bg: #f6f6f6;
  --vz-body-color: #495057;
  --vz-card-bg: #ffffff;
  --vz-border-color: #eff2f7;

  /* Sidebar - Light Theme (matches content area) */
  --vz-sidebar-bg: #ffffff;
  --vz-sidebar-menu-item-color: #495057;
  --vz-sidebar-menu-item-hover-color: #556ee6;
  --vz-sidebar-menu-item-active-color: #556ee6;
  --vz-sidebar-menu-item-active-bg: rgba(85, 110, 230, 0.1);
  --vz-sidebar-border: #eff2f7;

  /* Header - Grand Style */
  --vz-header-bg: #ffffff;
  --vz-header-item-color: #74788d;
  --vz-header-border: #eff2f7;

  /* Fonts - Grand Typography */
  --vz-font-sans-serif: "Poppins", sans-serif;
  --vz-font-size-base: 0.8125rem;
  --vz-font-weight-medium: 500;
  --vz-font-weight-semibold: 600;

  /* Shadows - Grand Style */
  --vz-box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);
  --vz-box-shadow-lg: 0 1rem 3rem rgba(18, 38, 63, 0.1);
  --vz-box-shadow-sm: 0 0.125rem 0.25rem rgba(18, 38, 63, 0.075);

  /* Gradients */
  --vz-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --vz-gradient-success: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Dark Theme Variables - Grand Style */
[data-bs-theme="dark"] {
  --vz-body-bg: #161c2d;
  --vz-body-color: #a6b0cf;
  --vz-card-bg: #1e2139;
  --vz-border-color: #32394e;
  --vz-header-bg: #1e2139;
  --vz-header-border: #32394e;
  --vz-sidebar-bg: #1e2139;
  --vz-sidebar-menu-item-color: #a6b0cf;
  --vz-sidebar-menu-item-hover-color: #ffffff;
  --vz-sidebar-menu-item-active-color: #ffffff;
  --vz-sidebar-menu-item-active-bg: rgba(85, 110, 230, 0.2);
  --vz-sidebar-border: #32394e;
}

/* Base Styles */
body {
  font-family: var(--vz-font-sans-serif);
  font-size: var(--vz-font-size-base);
  background-color: var(--vz-body-bg);
  color: var(--vz-body-color);
  line-height: 1.5;
}

/* Layout Wrapper */
#layout-wrapper {
  display: flex;
  min-height: 100vh;
}

/* ===== SIDEBAR STYLES - Grand Theme ===== */
.app-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 260px;
  height: 100vh;
  background: var(--vz-sidebar-bg);
  z-index: 1000;
  transition: all 0.3s ease;
  overflow-y: auto;
  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
}

.navbar-brand-box {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--vz-sidebar-border);
  background: var(--vz-sidebar-bg);
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--vz-sidebar-menu-item-color);
}

/* Logo theme visibility - show only one logo based on theme */
.logo-dark {
  display: flex;
}

.logo-light {
  display: none;
}

/* Dark theme: show light logo, hide dark logo */
[data-bs-theme="dark"] .logo-dark {
  display: none;
}

[data-bs-theme="dark"] .logo-light {
  display: flex;
}

.logo-sm {
  display: none;
}

.logo-lg {
  display: flex;
  align-items: center;
}

.logo-txt {
  margin-left: 0.75rem;
  font-size: 1.375rem;
  font-weight: var(--vz-font-weight-semibold);
  color: var(--vz-sidebar-menu-item-color);
  letter-spacing: -0.025em;
}

/* Sidebar Navigation - Grand Style */
.navbar-nav {
  padding: 1.5rem 0 2rem;
}

.menu-title {
  padding: 1rem 1.5rem 0.5rem;
  font-size: 0.6875rem;
  font-weight: var(--vz-font-weight-semibold);
  color: rgba(166, 176, 207, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.75px;
  margin-bottom: 0.5rem;
  text-align: center;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 1.5rem;
  color: var(--vz-sidebar-menu-item-color);
  text-decoration: none;
  border-radius: 0.375rem;
  margin: 0 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  font-weight: var(--vz-font-weight-medium);
  font-size: 0.8125rem;
  text-align: center;
}

.nav-link:hover {
  color: var(--vz-sidebar-menu-item-hover-color);
  background-color: rgba(85, 110, 230, 0.1);
  transform: translateX(2px);
}

.nav-link.active {
  color: var(--vz-sidebar-menu-item-active-color);
  background-color: var(--vz-sidebar-menu-item-active-bg);
  box-shadow: 0 2px 4px rgba(85, 110, 230, 0.2);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--vz-primary);
  border-radius: 0 2px 2px 0;
}

.nav-link i {
  width: 20px;
  margin-right: 0.875rem;
  font-size: 1.125rem;
  text-align: center;
}

/* Collapsible Menu - Grand Style */
.menu-dropdown {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.375rem;
  margin: 0.25rem 0.75rem;
  overflow: hidden;
}

.menu-dropdown .nav-link {
  padding: 0.625rem 1rem;
  font-size: 0.75rem;
  margin: 0;
  border-radius: 0;
  font-weight: 400;
  justify-content: center;
  text-align: center;
}

.menu-dropdown .nav-link:hover {
  background-color: rgba(85, 110, 230, 0.08);
  transform: none;
}

.menu-dropdown .nav-link.active::before {
  display: none;
}

/* ===== MAIN CONTENT ===== */
.main-content {
  margin-left: 260px;
  width: calc(100% - 260px);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

/* ===== HEADER STYLES - Grand Theme ===== */
#page-topbar {
  background: var(--vz-header-bg);
  box-shadow: var(--vz-box-shadow-sm);
  border-bottom: 1px solid var(--vz-header-border);
  position: sticky;
  top: 0;
  z-index: 999;
  backdrop-filter: blur(10px);
}

.layout-width {
  max-width: 100%;
}

.navbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  height: 70px;
  position: relative;
}

/* Hide horizontal logo on desktop - only show on mobile */
.horizontal-logo {
  display: none !important;
}

/* Ensure topbar horizontal logo is always hidden on desktop */
#page-topbar .navbar-brand-box.horizontal-logo,
#page-topbar .horizontal-logo,
.navbar-header .horizontal-logo {
  display: none !important;
}

.topnav-hamburger {
  display: none;
  background: transparent;
  border: none;
  color: var(--vz-header-item-color);
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.topnav-hamburger:hover {
  background-color: var(--vz-light);
  color: var(--vz-primary);
}

.hamburger-icon {
  display: flex;
  flex-direction: column;
  width: 18px;
  height: 14px;
  justify-content: space-between;
}

.hamburger-icon span {
  height: 2px;
  background: currentColor;
  border-radius: 1px;
  transition: all 0.3s ease;
}

/* Search - Grand Style */
.app-search {
  position: relative;
  max-width: 320px;
  width: 100%;
  margin-left: 1rem;
}

.app-search .form-control {
  padding: 0.625rem 1rem 0.625rem 2.75rem;
  border: 1px solid var(--vz-border-color);
  border-radius: 0.5rem;
  background-color: var(--vz-light);
  font-size: 0.8125rem;
  transition: all 0.3s ease;
  box-shadow: none;
}

.app-search .form-control:focus {
  border-color: var(--vz-primary);
  box-shadow: 0 0 0 0.2rem rgba(85, 110, 230, 0.15);
  background-color: white;
}

.search-widget-icon {
  position: absolute;
  left: 0.875rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--vz-header-item-color);
  font-size: 1rem;
}

.search-widget-icon-close {
  left: auto;
  right: 0.875rem;
  cursor: pointer;
}

/* Header Items - Grand Style */
.header-item {
  margin-left: 0.75rem;
}

.btn-topbar {
  width: 42px;
  height: 42px;
  border-radius: 0.5rem;
  border: none;
  background: transparent;
  color: var(--vz-header-item-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.btn-topbar:hover {
  background-color: var(--vz-light);
  color: var(--vz-primary);
  transform: translateY(-1px);
}

.topbar-badge {
  top: -8px;
  right: -8px;
  font-size: 0.625rem;
  padding: 0.25rem 0.4rem;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Better notification badge positioning */
.btn-topbar .badge {
  top: -8px !important;
  right: -8px !important;
  transform: none !important;
}

/* User Profile - Grand Style */
.topbar-user {
  background: transparent;
  border: none;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.topbar-user:hover {
  background-color: var(--vz-light);
}

.header-profile-user {
  width: 36px;
  height: 36px;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: var(--vz-font-weight-semibold);
}

.user-name-text {
  font-size: 0.875rem;
  font-weight: var(--vz-font-weight-medium);
  color: var(--vz-body-color);
}

.user-name-sub-text {
  color: var(--vz-header-item-color);
  font-size: 0.75rem;
}

/* ===== PAGE CONTENT ===== */
.page-content {
  flex: 1;
  padding: 1.5rem;
}

.container-fluid {
  max-width: 100%;
}

/* Page Title */
.page-title-box {
  margin-bottom: 1.5rem;
}

.page-title-box h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.breadcrumb {
  background: transparent;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  font-size: 0.8125rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: var(--vz-header-item-color);
}

/* ===== CARDS ===== */
.card {
  background: var(--vz-card-bg);
  border: 1px solid var(--vz-border-color);
  border-radius: 0.5rem;
  box-shadow: var(--vz-box-shadow);
  margin-bottom: 1.5rem;
}

.card-animate {
  transition: all 0.3s ease;
}

.card-animate:hover {
  transform: translateY(-2px);
  box-shadow: var(--vz-box-shadow-lg);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid var(--vz-border-color);
  padding: 1rem 1.5rem;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
}

/* ===== STATISTICS CARDS ===== */
.counter-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--vz-dark);
}

[data-bs-theme="dark"] .counter-value {
  color: var(--vz-light);
}

.avatar-sm {
  width: 3rem;
  height: 3rem;
}

.avatar-xs {
  width: 2rem;
  height: 2rem;
  font-size: 0.75rem;
}

.avatar-title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
  font-size: 1.25rem;
}

/* Success Subtle */
.bg-success-subtle {
  background-color: rgba(10, 179, 156, 0.1) !important;
}

.text-success {
  color: var(--vz-success) !important;
}

/* Info Subtle */
.bg-info-subtle {
  background-color: rgba(41, 156, 219, 0.1) !important;
}

.text-info {
  color: var(--vz-info) !important;
}

/* Warning Subtle */
.bg-warning-subtle {
  background-color: rgba(247, 184, 75, 0.1) !important;
}

.text-warning {
  color: var(--vz-warning) !important;
}

/* Primary Subtle */
.bg-primary-subtle {
  background-color: rgba(64, 81, 137, 0.1) !important;
}

.text-primary {
  color: var(--vz-primary) !important;
}

/* Danger Subtle */
.bg-danger-subtle {
  background-color: rgba(240, 101, 72, 0.1) !important;
}

.text-danger {
  color: var(--vz-danger) !important;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--vz-card-bg);
  border-top: 1px solid var(--vz-border-color);
  padding: 1rem 1.5rem;
  margin-top: auto;
}

/* ===== RESPONSIVE - Grand Style ===== */
@media (max-width: 1199.98px) {
  .user-name-text, .user-name-sub-text {
    display: none !important;
  }
}

@media (max-width: 991.98px) {
  .app-menu {
    transform: translateX(-100%);
    box-shadow: 0 0 50px rgba(18, 38, 63, 0.2);
  }

  .main-content {
    margin-left: 0;
    width: 100%;
  }

  .topnav-hamburger {
    display: flex !important;
  }

  .app-search {
    display: none !important;
  }

  /* Show horizontal logo on mobile when sidebar is hidden */
  .horizontal-logo {
    display: block !important;
  }

  .horizontal-logo .logo-lg {
    display: none !important;
  }

  .horizontal-logo .logo-sm {
    display: flex !important;
  }

  .navbar-header {
    padding: 0 1rem;
  }

  .header-item {
    margin-left: 0.5rem;
  }

  .btn-topbar {
    width: 38px;
    height: 38px;
  }
}

@media (max-width: 767.98px) {
  .page-content {
    padding: 1rem;
  }

  .navbar-header {
    height: 60px;
  }

  .card-body {
    padding: 1rem;
  }

  .counter-value {
    font-size: 1.5rem;
  }

  .navbar-brand-box {
    padding: 1rem;
  }
}

@media (max-width: 575.98px) {
  .page-title-box {
    margin-bottom: 1rem;
  }

  .page-title-box h4 {
    font-size: 1.125rem;
  }

  .breadcrumb-item {
    font-size: 0.75rem;
  }

  .header-item:not(:first-child) {
    margin-left: 0.25rem;
  }
}

/* Sidebar Toggle States */
.sidebar-enable .app-menu {
  transform: translateX(0);
}

/* Hide horizontal logo when sidebar is open on mobile */
.sidebar-enable .horizontal-logo {
  display: none !important;
}

/* Ensure proper logo display logic */
@media (min-width: 992px) {
  .horizontal-logo {
    display: none !important;
  }
}

/* Additional rule to ensure horizontal logo is always hidden on desktop */
@media (min-width: 768px) {
  .navbar-brand-box.horizontal-logo {
    display: none !important;
  }

  /* Specifically target the topbar horizontal logo */
  #page-topbar .horizontal-logo {
    display: none !important;
  }
}

/* Logo text styling */
.logo-txt {
  font-family: var(--vz-font-sans-serif);
  font-weight: var(--vz-font-weight-semibold);
  color: inherit;
  text-decoration: none;
}

/* ===== ANIMATIONS & INTERACTIVE FEATURES - Grand Style ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

/* Interactive Hover Effects */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.nav-link:hover::after {
  left: 100%;
}

/* Card Hover Effects */
.card-animate {
  position: relative;
  overflow: hidden;
}

.card-animate::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(85, 110, 230, 0.05), rgba(85, 110, 230, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.card-animate:hover::before {
  opacity: 1;
}

/* Button Ripple Effect */
.btn-topbar {
  position: relative;
  overflow: hidden;
}

.btn-topbar::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(85, 110, 230, 0.3);
  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
  transform: translate(-50%, -50%);
}

.btn-topbar:active::before {
  width: 300px;
  height: 300px;
  top: 50%;
  left: 50%;
}

/* Notification Badge Pulse */
.topbar-badge {
  animation: pulse 2s infinite;
}

/* Loading Shimmer Effect */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Smooth Scrollbar */
.app-menu::-webkit-scrollbar {
  width: 4px;
}

.app-menu::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.app-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.app-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Focus States */
.form-control:focus,
.btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(85, 110, 230, 0.25);
}

/* Dropdown Animations */
.dropdown-menu {
  animation: fadeInUp 0.3s ease-out;
  transform-origin: top;
}

/* Menu Collapse Animation */
.menu-dropdown {
  transition: all 0.3s ease;
}

.menu-dropdown.collapsing {
  transition: height 0.3s ease;
}

/* Active State Enhancements */
.nav-link.active {
  position: relative;
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: -0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--vz-primary);
  border-radius: 0 2px 2px 0;
  box-shadow: 0 2px 4px rgba(85, 110, 230, 0.3);
}

/* Ripple Effect */
.ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Enhanced Transitions */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Smooth Hover Transitions */
.nav-link,
.btn-topbar,
.card-animate,
.dropdown-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Focus Styles */
.nav-link:focus-visible,
.btn-topbar:focus-visible {
  outline: 2px solid var(--vz-primary);
  outline-offset: 2px;
}

/* ===== UTILITIES ===== */
.text-muted {
  color: var(--vz-header-item-color) !important;
}

.border-dashed {
  border-style: dashed !important;
}

.fs-22 {
  font-size: 1.375rem !important;
}

.fs-16 {
  font-size: 1rem !important;
}

.fs-14 {
  font-size: 0.875rem !important;
}

.fs-13 {
  font-size: 0.8125rem !important;
}

.fs-12 {
  font-size: 0.75rem !important;
}

.fs-11 {
  font-size: 0.6875rem !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.ff-secondary {
  font-family: var(--vz-font-sans-serif) !important;
}

/* ===== PLACEHOLDER IMAGES ===== */
.avatar-placeholder {
  background: linear-gradient(135deg, var(--vz-primary), var(--vz-info));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border-radius: 50%;
}

.flag-placeholder {
  width: 20px;
  height: 15px;
  border-radius: 2px;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.flag-us { background: linear-gradient(to bottom, #B22234 0%, #B22234 50%, #FFFFFF 50%, #FFFFFF 100%); }
.flag-spain { background: linear-gradient(to bottom, #AA151B 0%, #AA151B 33%, #F1BF00 33%, #F1BF00 66%, #AA151B 66%); }
.flag-canada { background: linear-gradient(to right, #FF0000 0%, #FF0000 25%, #FFFFFF 25%, #FFFFFF 75%, #FF0000 75%); }
.flag-french { background: linear-gradient(to right, #002395 0%, #002395 33%, #FFFFFF 33%, #FFFFFF 66%, #ED2939 66%); }
.flag-ae { background: linear-gradient(to bottom, #00732F 0%, #00732F 33%, #FFFFFF 33%, #FFFFFF 66%, #000000 66%); }
.flag-russia { background: linear-gradient(to bottom, #FFFFFF 0%, #FFFFFF 33%, #0039A6 33%, #0039A6 66%, #D52B1E 66%); }
.flag-australia { background: linear-gradient(135deg, #012169 0%, #012169 50%, #FFFFFF 50%); }

/* ===== TABLE ENHANCEMENTS ===== */
.table-striped-columns > :not(caption) > tr > :nth-child(even) {
  background-color: rgba(var(--vz-primary-rgb), 0.025);
}

.table-card {
  border-radius: 0.5rem;
  overflow: hidden;
}

.table th {
  background-color: var(--vz-light);
  border-bottom: 2px solid var(--vz-border-color);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 1rem 0.75rem;
}

.table td {
  padding: 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--vz-border-color);
}

.table tbody tr:hover {
  background-color: rgba(var(--vz-primary-rgb), 0.05);
}

/* ===== BADGES ===== */
.badge {
  font-size: 0.6875rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

.bg-success-subtle.text-success {
  background-color: rgba(10, 179, 156, 0.1) !important;
  color: var(--vz-success) !important;
}

.bg-warning-subtle.text-warning {
  background-color: rgba(247, 184, 75, 0.1) !important;
  color: var(--vz-warning) !important;
}

.bg-danger-subtle.text-danger {
  background-color: rgba(240, 101, 72, 0.1) !important;
  color: var(--vz-danger) !important;
}

/* ===== CHART CONTAINER ===== */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* ===== LOADING STATES ===== */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== NOTIFICATION DROPDOWN ===== */
.dropdown-head {
  background: linear-gradient(135deg, var(--vz-primary), var(--vz-info));
}

.notification-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--vz-border-color);
  transition: all 0.3s ease;
}

.notification-item:hover {
  background-color: var(--vz-light);
}

.notification-item:last-child {
  border-bottom: none;
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .page-content {
    padding: 1rem;
  }

  .navbar-header {
    padding: 0 1rem;
  }

  .counter-value {
    font-size: 1.5rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

/* ===== DARK THEME SPECIFIC ===== */
[data-bs-theme="dark"] .table th {
  background-color: var(--vz-card-bg);
  color: var(--vz-body-color);
}

[data-bs-theme="dark"] .table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .notification-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .btn-topbar:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--vz-light);
}

::-webkit-scrollbar-thumb {
  background: var(--vz-border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vz-secondary);
}
