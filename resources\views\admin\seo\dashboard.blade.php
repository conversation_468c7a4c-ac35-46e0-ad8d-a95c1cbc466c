@extends('admin.layouts.app')

@section('title', 'SEO Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">SEO Dashboard</h1>
        <div class="btn-group">
            <a href="{{ route('admin.seo.settings') }}" class="btn btn-outline-primary">
                <i class="fas fa-cog me-2"></i>Settings
            </a>
            <a href="{{ route('admin.seo.analysis') }}" class="btn btn-outline-info">
                <i class="fas fa-chart-line me-2"></i>Analysis
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Pages
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_pages'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Pages
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active_pages'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Blog Posts
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['blog_posts'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-blog fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                SEO Settings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['seo_settings'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cogs fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.seo.pages.create') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-plus me-2"></i>Add Page SEO
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.blog.create') }}" class="btn btn-success btn-block">
                                <i class="fas fa-edit me-2"></i>Create Blog Post
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <form action="{{ route('admin.seo.sitemap.generate') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-info btn-block">
                                    <i class="fas fa-sitemap me-2"></i>Generate Sitemap
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.seo.analysis') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-search me-2"></i>SEO Analysis
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Pages</h6>
                    <a href="{{ route('admin.seo.pages') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if($recentPages->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Page</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Updated</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentPages as $page)
                                    <tr>
                                        <td>
                                            <div class="font-weight-bold">{{ $page->page_identifier }}</div>
                                            <small class="text-muted">{{ Str::limit($page->title, 30) }}</small>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $page->page_type }}</span>
                                        </td>
                                        <td>
                                            @if($page->is_active)
                                                <span class="badge badge-success">Active</span>
                                            @else
                                                <span class="badge badge-secondary">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $page->updated_at->diffForHumans() }}</small>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted mb-0">No pages found. <a href="{{ route('admin.seo.pages.create') }}">Create your first page SEO</a>.</p>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Blog Posts</h6>
                    <a href="{{ route('admin.blog.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if($recentPosts->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Views</th>
                                        <th>Published</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentPosts as $post)
                                    <tr>
                                        <td>
                                            <div class="font-weight-bold">{{ Str::limit($post->title, 25) }}</div>
                                            <small class="text-muted">{{ Str::limit($post->excerpt, 40) }}</small>
                                        </td>
                                        <td>
                                            @if($post->status === 'published')
                                                <span class="badge badge-success">Published</span>
                                            @elseif($post->status === 'draft')
                                                <span class="badge badge-warning">Draft</span>
                                            @else
                                                <span class="badge badge-secondary">Archived</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $post->views_count }}</span>
                                        </td>
                                        <td>
                                            <small>{{ $post->published_at ? $post->published_at->diffForHumans() : 'Not published' }}</small>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted mb-0">No blog posts found. <a href="{{ route('admin.blog.create') }}">Create your first blog post</a>.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Tips -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">SEO Tips for Kenya Market</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-primary">Local Keywords</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Include "Kenya" in titles</li>
                                <li><i class="fas fa-check text-success me-2"></i>Target "Nairobi" for local searches</li>
                                <li><i class="fas fa-check text-success me-2"></i>Use "software development Kenya"</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">Content Strategy</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Write about local tech trends</li>
                                <li><i class="fas fa-check text-success me-2"></i>Include M-Pesa integration topics</li>
                                <li><i class="fas fa-check text-success me-2"></i>Cover Kenya business solutions</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-primary">Technical SEO</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Optimize for mobile users</li>
                                <li><i class="fas fa-check text-success me-2"></i>Use local business schema</li>
                                <li><i class="fas fa-check text-success me-2"></i>Submit to Google My Business</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
