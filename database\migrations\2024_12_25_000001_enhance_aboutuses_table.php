<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aboutuses', function (Blueprint $table) {
            // Fix existing column issues
            if (Schema::hasColumn('aboutuses', 'description')) {
                $table->text('description')->change();
            }
            
            // Add new comprehensive fields
            if (!Schema::hasColumn('aboutuses', 'subtitle')) {
                $table->string('subtitle')->nullable()->after('tittle');
            }
            
            if (!Schema::hasColumn('aboutuses', 'short_description')) {
                $table->text('short_description')->nullable()->after('subtitle');
            }
            
            if (!Schema::hasColumn('aboutuses', 'full_description')) {
                $table->longText('full_description')->nullable()->after('description');
            }
            
            if (!Schema::hasColumn('aboutuses', 'mission')) {
                $table->text('mission')->nullable()->after('full_description');
            }
            
            if (!Schema::hasColumn('aboutuses', 'vision')) {
                $table->text('vision')->nullable()->after('mission');
            }
            
            if (!Schema::hasColumn('aboutuses', 'values')) {
                $table->json('values')->nullable()->after('vision');
            }
            
            if (!Schema::hasColumn('aboutuses', 'years_experience')) {
                $table->integer('years_experience')->default(0)->after('values');
            }
            
            if (!Schema::hasColumn('aboutuses', 'projects_completed')) {
                $table->integer('projects_completed')->default(0)->after('years_experience');
            }
            
            if (!Schema::hasColumn('aboutuses', 'happy_clients')) {
                $table->integer('happy_clients')->default(0)->after('projects_completed');
            }
            
            if (!Schema::hasColumn('aboutuses', 'team_members')) {
                $table->integer('team_members')->default(0)->after('happy_clients');
            }
            
            if (!Schema::hasColumn('aboutuses', 'button_text')) {
                $table->string('button_text')->default('More Details')->after('team_members');
            }
            
            if (!Schema::hasColumn('aboutuses', 'button_link')) {
                $table->string('button_link')->default('/about')->after('button_text');
            }
            
            if (!Schema::hasColumn('aboutuses', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('button_link');
            }
            
            if (!Schema::hasColumn('aboutuses', 'meta_title')) {
                $table->string('meta_title')->nullable()->after('status');
            }
            
            if (!Schema::hasColumn('aboutuses', 'meta_description')) {
                $table->text('meta_description')->nullable()->after('meta_title');
            }
            
            if (!Schema::hasColumn('aboutuses', 'meta_keywords')) {
                $table->text('meta_keywords')->nullable()->after('meta_description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aboutuses', function (Blueprint $table) {
            $columnsToRemove = [
                'subtitle',
                'short_description', 
                'full_description',
                'mission',
                'vision',
                'values',
                'years_experience',
                'projects_completed',
                'happy_clients',
                'team_members',
                'button_text',
                'button_link',
                'status',
                'meta_title',
                'meta_description',
                'meta_keywords'
            ];
            
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('aboutuses', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
