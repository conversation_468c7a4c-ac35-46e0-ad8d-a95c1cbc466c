@extends('backend.layouts.app')

@section('title', 'View Slider - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">View Slider</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('sliders.index') }}">Sliders</a></li>
                    <li class="breadcrumb-item active">View</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">Slider Details</h4>
                <div>
                    {!! $slider->status_badge !!}
                    <span class="badge bg-light text-dark ms-2">#{{ $slider->sort_order ?? $slider->id }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Main Content -->
                    <div class="col-md-8">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted">Main Title</h6>
                                <p class="h5">{{ $slider->first_slogan_one }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Subtitle</h6>
                                <p class="h6">{{ $slider->second_slogan_one }}</p>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted">Description Line 1</h6>
                                <p>{{ $slider->first_slogan_two }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Description Line 2</h6>
                                <p>{{ $slider->second_slogan_two }}</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">Status</h6>
                                <p>{{ ucfirst($slider->status ?? 'active') }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Sort Order</h6>
                                <p>{{ $slider->sort_order ?? 'Not set' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Images -->
                    <div class="col-md-4">
                        <div class="mb-4">
                            <h6 class="text-muted">Main Slider Image</h6>
                            @if($slider->slider_one_img)
                                <img src="{{ $slider->slider_one_img_url }}" alt="{{ $slider->first_slogan_one }}" 
                                     class="img-fluid rounded border" style="max-height: 200px;">
                            @else
                                <div class="border rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <span class="text-muted">No image</span>
                                </div>
                            @endif
                        </div>

                        <div class="mb-4">
                            <h6 class="text-muted">Secondary Image</h6>
                            @if($slider->slogan_two_img)
                                <img src="{{ $slider->slogan_two_img_url }}" alt="Secondary Image" 
                                     class="img-fluid rounded border" style="max-height: 200px;">
                            @else
                                <div class="border rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <span class="text-muted">No image</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Metadata -->
                <div class="row mt-4 pt-4 border-top">
                    <div class="col-md-6">
                        <h6 class="text-muted">Created</h6>
                        <p>{{ $slider->created_at ? $slider->created_at->format('M d, Y \a\t g:i A') : 'Not available' }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Last Updated</h6>
                        <p>{{ $slider->updated_at ? $slider->updated_at->format('M d, Y \a\t g:i A') : 'Not available' }}</p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="d-flex justify-content-between align-items-center mt-4 pt-4 border-top">
                    <a href="{{ route('sliders.index') }}" class="btn btn-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Back to List
                    </a>
                    
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-{{ ($slider->status ?? 'active') === 'active' ? 'warning' : 'success' }}" 
                                onclick="toggleStatus({{ $slider->id }})" title="Toggle Status">
                            <i class="ri-{{ ($slider->status ?? 'active') === 'active' ? 'pause' : 'play' }}-line me-1"></i>
                            {{ ($slider->status ?? 'active') === 'active' ? 'Deactivate' : 'Activate' }}
                        </button>
                        
                        <a href="{{ route('sliders.edit', $slider) }}" class="btn btn-primary">
                            <i class="ri-edit-line me-1"></i>Edit
                        </a>
                        
                        <button type="button" class="btn btn-outline-secondary" 
                                onclick="duplicateSlider({{ $slider->id }})" title="Duplicate">
                            <i class="ri-file-copy-line me-1"></i>Duplicate
                        </button>
                        
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="deleteSlider({{ $slider->id }})" title="Delete">
                            <i class="ri-delete-bin-line me-1"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Slider Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="preview-container" style="height: 500px; background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('{{ $slider->slider_one_img_url }}') center/cover;">
                    <div class="d-flex align-items-center justify-content-center h-100 text-white text-center">
                        <div>
                            <h4 class="mb-3">{{ $slider->second_slogan_one }}</h4>
                            <h1 class="mb-3">{{ $slider->first_slogan_one }}</h1>
                            <p class="mb-3">{{ $slider->first_slogan_two }}</p>
                            <p class="mb-4">{{ $slider->second_slogan_two }}</p>
                            <button class="btn btn-primary">Learn More</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Toggle status
function toggleStatus(id) {
    fetch(`/admin/sliders/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to toggle status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to toggle status');
    });
}

// Duplicate slider
function duplicateSlider(id) {
    if (confirm('Are you sure you want to duplicate this slider?')) {
        fetch(`/admin/sliders/${id}/duplicate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => {
            if (response.ok) {
                window.location.href = '{{ route("sliders.index") }}';
            } else {
                alert('Failed to duplicate slider');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to duplicate slider');
        });
    }
}

// Delete slider
function deleteSlider(id) {
    if (confirm('Are you sure you want to delete this slider? This action cannot be undone.')) {
        fetch(`/admin/sliders/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => {
            if (response.ok) {
                window.location.href = '{{ route("sliders.index") }}';
            } else {
                alert('Failed to delete slider');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete slider');
        });
    }
}

// Preview slider
function previewSlider() {
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}
</script>
@endpush
