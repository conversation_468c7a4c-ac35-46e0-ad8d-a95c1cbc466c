@extends('admin.layouts.app')

@section('title', 'SEO Analysis')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">SEO Analysis</h1>
        <div class="btn-group">
            <a href="{{ route('admin.seo.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
            <button type="button" class="btn btn-primary" onclick="refreshAnalysis()">
                <i class="fas fa-sync me-2"></i>Refresh Analysis
            </button>
        </div>
    </div>

    <!-- SEO Health Overview -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Missing Titles
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $analysis['pages_without_title'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Missing Descriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $analysis['pages_without_description'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Long Titles
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $analysis['long_titles'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-text-width fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Long Descriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $analysis['long_descriptions'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-align-left fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Issues -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Duplicate Titles</h6>
                </div>
                <div class="card-body">
                    @if($analysis['duplicate_titles']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Count</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($analysis['duplicate_titles'] as $duplicate)
                                    <tr>
                                        <td>
                                            <small>{{ Str::limit($duplicate->title, 40) }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $duplicate->count }}</span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="findDuplicates('title', '{{ addslashes($duplicate->title) }}')">
                                                View
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">No duplicate titles found!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Duplicate Descriptions</h6>
                </div>
                <div class="card-body">
                    @if($analysis['duplicate_descriptions']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Count</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($analysis['duplicate_descriptions'] as $duplicate)
                                    <tr>
                                        <td>
                                            <small>{{ Str::limit($duplicate->description, 40) }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $duplicate->count }}</span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="findDuplicates('description', '{{ addslashes($duplicate->description) }}')">
                                                View
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">No duplicate descriptions found!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Recommendations -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">SEO Recommendations for Kenya Market</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="text-success">✅ Good Practices</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Include "Kenya" in page titles</li>
                                <li><i class="fas fa-check text-success me-2"></i>Target local keywords like "Nairobi"</li>
                                <li><i class="fas fa-check text-success me-2"></i>Use "software development Kenya"</li>
                                <li><i class="fas fa-check text-success me-2"></i>Add local business schema</li>
                                <li><i class="fas fa-check text-success me-2"></i>Optimize for mobile users</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-warning">⚠️ Improvements Needed</h6>
                            <ul class="list-unstyled">
                                @if($analysis['pages_without_title'] > 0)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>{{ $analysis['pages_without_title'] }} pages missing titles</li>
                                @endif
                                @if($analysis['pages_without_description'] > 0)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>{{ $analysis['pages_without_description'] }} pages missing descriptions</li>
                                @endif
                                @if($analysis['pages_without_keywords'] > 0)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>{{ $analysis['pages_without_keywords'] }} pages missing keywords</li>
                                @endif
                                @if($analysis['long_titles'] > 0)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>{{ $analysis['long_titles'] }} titles too long (>60 chars)</li>
                                @endif
                                @if($analysis['long_descriptions'] > 0)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>{{ $analysis['long_descriptions'] }} descriptions too long (>160 chars)</li>
                                @endif
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-info">💡 Kenya-Specific Tips</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-lightbulb text-info me-2"></i>Target "M-Pesa integration" keywords</li>
                                <li><i class="fas fa-lightbulb text-info me-2"></i>Include "East Africa" in content</li>
                                <li><i class="fas fa-lightbulb text-info me-2"></i>Mention "Kenyan businesses"</li>
                                <li><i class="fas fa-lightbulb text-info me-2"></i>Add Swahili keywords where relevant</li>
                                <li><i class="fas fa-lightbulb text-info me-2"></i>Focus on local success stories</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.seo.pages') }}?search=" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-edit me-2"></i>Fix Missing Titles
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.seo.pages') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-align-left me-2"></i>Add Descriptions
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.blog.create') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-plus me-2"></i>Create Kenya Content
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('admin.seo.settings') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-cog me-2"></i>Update Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function refreshAnalysis() {
    location.reload();
}

function findDuplicates(field, value) {
    // This would open a modal or redirect to show pages with duplicate content
    const searchUrl = `{{ route('admin.seo.pages') }}?search=${encodeURIComponent(value)}`;
    window.open(searchUrl, '_blank');
}
</script>
@endpush
