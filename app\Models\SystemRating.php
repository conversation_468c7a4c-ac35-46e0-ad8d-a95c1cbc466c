<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemRating extends Model
{
    use HasFactory;

    protected $fillable = [
        'system_id',
        'user_id',
        'user_name',
        'user_email',
        'rating',
        'review',
        'ip_address',
        'is_verified',
        'is_featured',
        'status',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_verified' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * Scope to get only active ratings
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get only featured ratings
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get only verified ratings
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get ratings ordered by creation date
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Scope to get ratings by rating value
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Relationship with system
     */
    public function system()
    {
        return $this->belongsTo(System::class);
    }

    /**
     * Relationship with user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the display name for the rating
     */
    public function getDisplayNameAttribute()
    {
        if ($this->user) {
            return $this->user->name;
        }
        return $this->user_name ?? 'Anonymous';
    }

    /**
     * Get the display email for the rating
     */
    public function getDisplayEmailAttribute()
    {
        if ($this->user) {
            return $this->user->email;
        }
        return $this->user_email;
    }

    /**
     * Check if rating is from registered user
     */
    public function getIsRegisteredUserAttribute()
    {
        return !is_null($this->user_id);
    }

    /**
     * Get star rating as HTML
     */
    public function getStarRatingHtmlAttribute()
    {
        $html = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $html .= '<i class="fas fa-star text-warning"></i>';
            } else {
                $html .= '<i class="far fa-star text-muted"></i>';
            }
        }
        return $html;
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Validate rating value before saving
        static::saving(function ($rating) {
            if ($rating->rating < 1 || $rating->rating > 5) {
                throw new \InvalidArgumentException('Rating must be between 1 and 5');
            }
        });

        // Update system average rating when rating is created, updated, or deleted
        static::created(function ($rating) {
            $rating->system->updateAverageRating();
        });

        static::updated(function ($rating) {
            $rating->system->updateAverageRating();
        });

        static::deleted(function ($rating) {
            $rating->system->updateAverageRating();
        });
    }
}
