<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\System;
use App\Models\SystemRating;
use App\Models\User;

class SystemsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample systems
        $systems = [
            [
                'title' => 'E-Commerce Management System',
                'slug' => 'e-commerce-management-system',
                'description' => 'A comprehensive e-commerce platform with inventory management, order processing, and customer analytics.',
                'short_description' => 'Complete e-commerce solution with advanced features',
                'full_description' => 'Our E-Commerce Management System is a robust platform designed to handle all aspects of online retail. It features advanced inventory management, real-time order processing, customer relationship management, and comprehensive analytics dashboard. The system supports multiple payment gateways, automated email notifications, and mobile-responsive design.',
                'category' => 'Web Application',
                'technologies' => ['Laravel', 'Vue.js', 'MySQL', 'Redis', 'Stripe API'],
                'system_url' => 'https://demo-ecommerce.grandtek.com',
                'github_url' => 'https://github.com/grandtek/ecommerce-system',
                'documentation_url' => 'https://docs.grandtek.com/ecommerce',
                'features' => [
                    'Multi-vendor support',
                    'Real-time inventory tracking',
                    'Advanced analytics dashboard',
                    'Mobile-responsive design',
                    'Payment gateway integration',
                    'Automated email notifications'
                ],
                'status' => 'active',
                'featured' => true,
                'sort_order' => 1,
                'launch_date' => '2024-01-15',
                'version' => '2.1.0',
                'meta_title' => 'E-Commerce Management System - GrandTek Solutions',
                'meta_description' => 'Comprehensive e-commerce platform with inventory management and analytics',
            ],
            [
                'title' => 'Hospital Management System',
                'slug' => 'hospital-management-system',
                'description' => 'Complete healthcare management solution with patient records, appointment scheduling, and billing.',
                'short_description' => 'Healthcare management with patient records and scheduling',
                'full_description' => 'Our Hospital Management System streamlines healthcare operations with comprehensive patient record management, appointment scheduling, billing integration, and staff management. The system ensures HIPAA compliance and provides real-time reporting for healthcare administrators.',
                'category' => 'Healthcare',
                'technologies' => ['PHP', 'React', 'PostgreSQL', 'Docker'],
                'system_url' => 'https://demo-hospital.grandtek.com',
                'github_url' => 'https://github.com/grandtek/hospital-system',
                'features' => [
                    'Patient record management',
                    'Appointment scheduling',
                    'Billing and insurance',
                    'Staff management',
                    'HIPAA compliance',
                    'Real-time reporting'
                ],
                'status' => 'active',
                'featured' => true,
                'sort_order' => 2,
                'launch_date' => '2023-11-20',
                'version' => '1.8.5',
            ],
            [
                'title' => 'School Management Portal',
                'slug' => 'school-management-portal',
                'description' => 'Educational institution management with student records, grades, and parent communication.',
                'short_description' => 'Complete school management with student tracking',
                'full_description' => 'The School Management Portal is designed to digitize educational institutions. It manages student enrollment, academic records, grade tracking, attendance monitoring, and facilitates communication between teachers, students, and parents.',
                'category' => 'Education',
                'technologies' => ['Django', 'Python', 'SQLite', 'Bootstrap'],
                'system_url' => 'https://demo-school.grandtek.com',
                'features' => [
                    'Student enrollment',
                    'Grade management',
                    'Attendance tracking',
                    'Parent-teacher communication',
                    'Report generation',
                    'Fee management'
                ],
                'status' => 'active',
                'featured' => false,
                'sort_order' => 3,
                'launch_date' => '2024-03-10',
                'version' => '1.2.0',
            ],
            [
                'title' => 'Inventory Tracking API',
                'slug' => 'inventory-tracking-api',
                'description' => 'RESTful API for real-time inventory management across multiple warehouses.',
                'short_description' => 'Real-time inventory API for warehouses',
                'full_description' => 'A powerful RESTful API designed for enterprise-level inventory management. Supports real-time stock tracking, automated reorder points, multi-warehouse management, and integration with popular e-commerce platforms.',
                'category' => 'API',
                'technologies' => ['Node.js', 'Express', 'MongoDB', 'Redis'],
                'github_url' => 'https://github.com/grandtek/inventory-api',
                'documentation_url' => 'https://docs.grandtek.com/inventory-api',
                'features' => [
                    'Real-time stock tracking',
                    'Multi-warehouse support',
                    'Automated reorder alerts',
                    'E-commerce integration',
                    'Comprehensive reporting',
                    'RESTful endpoints'
                ],
                'status' => 'development',
                'featured' => false,
                'sort_order' => 4,
                'launch_date' => '2024-06-01',
                'version' => '0.9.0',
            ],
            [
                'title' => 'Task Management Mobile App',
                'slug' => 'task-management-mobile-app',
                'description' => 'Cross-platform mobile application for team task management and collaboration.',
                'short_description' => 'Mobile app for team task management',
                'full_description' => 'A feature-rich mobile application built with React Native for seamless task management and team collaboration. Includes real-time notifications, file sharing, time tracking, and project analytics.',
                'category' => 'Mobile App',
                'technologies' => ['React Native', 'Firebase', 'Redux', 'TypeScript'],
                'system_url' => 'https://play.google.com/store/apps/details?id=com.grandtek.taskmanager',
                'github_url' => 'https://github.com/grandtek/task-manager-app',
                'features' => [
                    'Real-time collaboration',
                    'Push notifications',
                    'File sharing',
                    'Time tracking',
                    'Offline support',
                    'Cross-platform compatibility'
                ],
                'status' => 'active',
                'featured' => true,
                'sort_order' => 5,
                'launch_date' => '2024-02-28',
                'version' => '1.5.2',
            ],
            [
                'title' => 'Financial Analytics Dashboard',
                'slug' => 'financial-analytics-dashboard',
                'description' => 'Advanced financial data visualization and analytics platform for businesses.',
                'short_description' => 'Business financial analytics and reporting',
                'full_description' => 'A comprehensive financial analytics platform that provides real-time insights into business performance. Features advanced charting, predictive analytics, automated reporting, and integration with popular accounting software.',
                'category' => 'Analytics',
                'technologies' => ['Angular', 'D3.js', 'Python', 'PostgreSQL', 'Docker'],
                'system_url' => 'https://demo-analytics.grandtek.com',
                'features' => [
                    'Real-time data visualization',
                    'Predictive analytics',
                    'Automated reporting',
                    'Multi-currency support',
                    'Custom dashboards',
                    'Export capabilities'
                ],
                'status' => 'maintenance',
                'featured' => false,
                'sort_order' => 6,
                'launch_date' => '2023-09-15',
                'version' => '2.0.1',
            ]
        ];

        foreach ($systems as $systemData) {
            $system = System::create($systemData);
            
            // Add some sample ratings for each system
            $this->addSampleRatings($system);
        }
    }

    /**
     * Add sample ratings for a system
     */
    private function addSampleRatings(System $system)
    {
        $sampleRatings = [
            [
                'user_name' => 'John Doe',
                'user_email' => '<EMAIL>',
                'rating' => 5,
                'review' => 'Excellent system! Very user-friendly and feature-rich.',
                'is_featured' => true,
            ],
            [
                'user_name' => 'Sarah Johnson',
                'user_email' => '<EMAIL>',
                'rating' => 4,
                'review' => 'Great functionality, though the interface could be more intuitive.',
                'is_featured' => false,
            ],
            [
                'user_name' => 'Mike Wilson',
                'user_email' => '<EMAIL>',
                'rating' => 5,
                'review' => 'Outstanding performance and reliability. Highly recommended!',
                'is_featured' => true,
            ],
            [
                'user_name' => 'Emily Chen',
                'user_email' => '<EMAIL>',
                'rating' => 4,
                'review' => 'Solid system with good documentation and support.',
                'is_featured' => false,
            ],
            [
                'user_name' => 'David Brown',
                'user_email' => '<EMAIL>',
                'rating' => 3,
                'review' => 'Good system overall, but needs some improvements in speed.',
                'is_featured' => false,
            ]
        ];

        // Randomly select 2-4 ratings for each system
        $selectedRatings = collect($sampleRatings)->random(rand(2, 4));

        foreach ($selectedRatings as $ratingData) {
            $ratingData['system_id'] = $system->id;
            $ratingData['status'] = 'active';
            $ratingData['ip_address'] = '127.0.0.1';
            
            SystemRating::create($ratingData);
        }
    }
}
