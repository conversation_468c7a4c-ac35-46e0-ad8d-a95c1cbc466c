<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\HomepageSlider;
use Database\Seeders\HomepageSlidersSeeder;

class TestSliderDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'slider:test-db';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test slider database connection and seed data if needed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing slider database connection...');

        try {
            // Test database connection
            $count = HomepageSlider::count();
            $this->info("✓ Database connection successful!");
            $this->info("Current slider count: {$count}");

            // List existing sliders
            if ($count > 0) {
                $this->info("\nExisting sliders:");
                $sliders = HomepageSlider::orderBy('sort_order')->get();
                foreach ($sliders as $slider) {
                    $status = $slider->status === 'active' ? '✓' : '✗';
                    $this->line("  {$status} [{$slider->sort_order}] {$slider->title} ({$slider->status})");
                }
            } else {
                $this->warn("No sliders found in database.");
                
                if ($this->confirm('Would you like to seed sample data?')) {
                    $this->info('Seeding sample slider data...');
                    $seeder = new HomepageSlidersSeeder();
                    $seeder->run();
                    $this->info('✓ Sample data seeded successfully!');
                }
            }

            // Test API endpoint
            $this->info("\nTesting API endpoint...");
            $activeSliders = HomepageSlider::active()->ordered()->get();
            $this->info("Active sliders for frontend: {$activeSliders->count()}");

            $this->info("\n✓ All tests passed! Slider system is ready.");

        } catch (\Exception $e) {
            $this->error("✗ Database test failed: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
