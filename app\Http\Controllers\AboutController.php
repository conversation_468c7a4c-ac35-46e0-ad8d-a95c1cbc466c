<?php

namespace App\Http\Controllers;

use App\Models\aboutus;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class AboutController extends Controller
{
    /**
     * Display the about page
     */
    public function index()
    {
        $about = aboutus::active()->first();
        $team = Team::take(8)->get(); // Get first 8 team members for about page
        
        return view('frontend.pages.about', compact('about', 'team'));
    }

    /**
     * Display a listing of about content for admin
     */
    public function adminIndex()
    {
        $about = aboutus::first();
        return view('backend.pages.about.index', compact('about'));
    }

    /**
     * Show the form for creating new about content
     */
    public function create()
    {
        return view('backend.pages.about.create');
    }

    /**
     * Store a newly created about content
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tittle' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string',
            'full_description' => 'nullable|string',
            'mission' => 'nullable|string',
            'vision' => 'nullable|string',
            'values' => 'nullable|array',
            'values.*' => 'string|max:255',
            'years_experience' => 'nullable|integer|min:0',
            'projects_completed' => 'nullable|integer|min:0',
            'happy_clients' => 'nullable|integer|min:0',
            'team_members' => 'nullable|integer|min:0',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|string|max:255',
            'aboutus_img_one' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'aboutus_img_two' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle image uploads
        if ($request->hasFile('aboutus_img_one')) {
            $data['aboutus_img_one'] = $request->file('aboutus_img_one')->store('about', 'public');
        }

        if ($request->hasFile('aboutus_img_two')) {
            $data['aboutus_img_two'] = $request->file('aboutus_img_two')->store('about', 'public');
        }

        // Filter out null values array
        if (isset($data['values'])) {
            $data['values'] = array_filter($data['values'], function($value) {
                return !empty(trim($value));
            });
        }

        aboutus::create($data);

        return redirect()->route('admin.about.index')
            ->with('success', 'About content created successfully!');
    }

    /**
     * Show the form for editing about content
     */
    public function edit($id)
    {
        $about = aboutus::findOrFail($id);
        return view('backend.pages.about.edit', compact('about'));
    }

    /**
     * Update the specified about content
     */
    public function update(Request $request, $id)
    {
        $about = aboutus::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'tittle' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string',
            'full_description' => 'nullable|string',
            'mission' => 'nullable|string',
            'vision' => 'nullable|string',
            'values' => 'nullable|array',
            'values.*' => 'string|max:255',
            'years_experience' => 'nullable|integer|min:0',
            'projects_completed' => 'nullable|integer|min:0',
            'happy_clients' => 'nullable|integer|min:0',
            'team_members' => 'nullable|integer|min:0',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|string|max:255',
            'aboutus_img_one' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'aboutus_img_two' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle image uploads
        if ($request->hasFile('aboutus_img_one')) {
            // Delete old image
            if ($about->aboutus_img_one && Storage::disk('public')->exists($about->aboutus_img_one)) {
                Storage::disk('public')->delete($about->aboutus_img_one);
            }
            $data['aboutus_img_one'] = $request->file('aboutus_img_one')->store('about', 'public');
        }

        if ($request->hasFile('aboutus_img_two')) {
            // Delete old image
            if ($about->aboutus_img_two && Storage::disk('public')->exists($about->aboutus_img_two)) {
                Storage::disk('public')->delete($about->aboutus_img_two);
            }
            $data['aboutus_img_two'] = $request->file('aboutus_img_two')->store('about', 'public');
        }

        // Filter out null values array
        if (isset($data['values'])) {
            $data['values'] = array_filter($data['values'], function($value) {
                return !empty(trim($value));
            });
        }

        $about->update($data);

        return redirect()->route('admin.about.index')
            ->with('success', 'About content updated successfully!');
    }

    /**
     * Remove the specified about content
     */
    public function destroy($id)
    {
        $about = aboutus::findOrFail($id);
        $about->delete();

        return redirect()->route('admin.about.index')
            ->with('success', 'About content deleted successfully!');
    }

    /**
     * Toggle status of about content
     */
    public function toggleStatus($id)
    {
        $about = aboutus::findOrFail($id);
        $about->status = $about->status === 'active' ? 'inactive' : 'active';
        $about->save();

        return response()->json([
            'success' => true,
            'status' => $about->status,
            'message' => 'Status updated successfully!'
        ]);
    }

    /**
     * Get about data for homepage
     */
    public function getHomepageData()
    {
        $about = aboutus::active()->first();
        
        if (!$about) {
            // Return null if no about content exists - no fallback data
            return null;
        }

        return [
            'tittle' => $about->tittle,
            'subtitle' => $about->subtitle ?? 'About Us',
            'description' => $about->description,
            'short_description' => $about->short_description,
            'aboutus_img_one_url' => $about->aboutus_img_one_url,
            'aboutus_img_two_url' => $about->aboutus_img_two_url,
            'button_text' => $about->button_text ?? 'More Details',
            'button_link' => $about->button_link ?? '/about',
            'statistics' => $about->statistics
        ];
    }
}
