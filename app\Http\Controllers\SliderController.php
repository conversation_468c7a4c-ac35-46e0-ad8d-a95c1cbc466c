<?php

namespace App\Http\Controllers;

use App\Models\HomepageSlider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SliderController extends Controller
{
    /**
     * Display a listing of sliders
     */
    public function index()
    {
        try {
            $sliders = HomepageSlider::ordered()->get();
            return view('backend.pages.sliders.index', compact('sliders'));
        } catch (\Exception $e) {
            Log::error('Error loading sliders: ' . $e->getMessage());
            return view('backend.pages.sliders.index', ['sliders' => collect()]);
        }
    }

    /**
     * Show the form for creating a new slider
     */
    public function create()
    {
        return view('backend.pages.sliders.create');
    }

    /**
     * Store a newly created slider
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|url|max:255',
            'media_type' => 'required|in:image,video',
            'image' => 'required_if:media_type,image|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'video' => 'required_if:media_type,video|mimes:mp4,webm,ogg|max:51200',
            'status' => 'required|in:active,inactive',
            'background_color' => 'nullable|string|max:7',
            'text_color' => 'required|string|max:7',
            'text_alignment' => 'required|in:left,center,right',
            'display_duration' => 'required|integer|min:3|max:60',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'title', 'subtitle', 'description', 'button_text', 'button_link',
            'media_type', 'status', 'background_color', 'text_color', 'text_alignment', 'display_duration'
        ]);

        // Handle file upload
        if ($request->media_type === 'image' && $request->hasFile('image')) {
            $data['image_path'] = $request->file('image')->store('sliders/images', 'public');
        } elseif ($request->media_type === 'video' && $request->hasFile('video')) {
            $data['video_path'] = $request->file('video')->store('sliders/videos', 'public');
        }

        // Handle animation settings
        $data['animation_settings'] = json_encode([
            'title_animation' => $request->input('title_animation', 'fadeInUp'),
            'subtitle_animation' => $request->input('subtitle_animation', 'fadeInLeft'),
            'description_animation' => $request->input('description_animation', 'fadeInRight'),
            'button_animation' => $request->input('button_animation', 'fadeInUp'),
            'duration' => $request->input('animation_duration', 1000),
            'delay' => $request->input('animation_delay', 200),
        ]);

        HomepageSlider::create($data);

        return redirect()->route('sliders.index')
            ->with('success', 'Slider created successfully!');
    }

    /**
     * Display the specified slider
     */
    public function show(HomepageSlider $slider)
    {
        return view('backend.pages.sliders.show', compact('slider'));
    }

    /**
     * Show the form for editing the specified slider
     */
    public function edit(HomepageSlider $slider)
    {
        return view('backend.pages.sliders.edit', compact('slider'));
    }

    /**
     * Update the specified slider
     */
    public function update(Request $request, HomepageSlider $slider)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'button_text' => 'nullable|string|max:100',
            'button_link' => 'nullable|url|max:255',
            'media_type' => 'required|in:image,video',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'video' => 'nullable|mimes:mp4,webm,ogg|max:51200',
            'status' => 'required|in:active,inactive',
            'background_color' => 'nullable|string|max:7',
            'text_color' => 'required|string|max:7',
            'text_alignment' => 'required|in:left,center,right',
            'display_duration' => 'required|integer|min:3|max:60',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'title', 'subtitle', 'description', 'button_text', 'button_link',
            'media_type', 'status', 'background_color', 'text_color', 'text_alignment', 'display_duration'
        ]);

        // Handle file upload and cleanup
        if ($request->media_type === 'image' && $request->hasFile('image')) {
            // Delete old image
            if ($slider->image_path && Storage::disk('public')->exists($slider->image_path)) {
                Storage::disk('public')->delete($slider->image_path);
            }
            $data['image_path'] = $request->file('image')->store('sliders/images', 'public');
            $data['video_path'] = null; // Clear video path when switching to image
        } elseif ($request->media_type === 'video' && $request->hasFile('video')) {
            // Delete old video
            if ($slider->video_path && Storage::disk('public')->exists($slider->video_path)) {
                Storage::disk('public')->delete($slider->video_path);
            }
            $data['video_path'] = $request->file('video')->store('sliders/videos', 'public');
            $data['image_path'] = null; // Clear image path when switching to video
        }

        // Handle animation settings
        $data['animation_settings'] = json_encode([
            'title_animation' => $request->input('title_animation', 'fadeInUp'),
            'subtitle_animation' => $request->input('subtitle_animation', 'fadeInLeft'),
            'description_animation' => $request->input('description_animation', 'fadeInRight'),
            'button_animation' => $request->input('button_animation', 'fadeInUp'),
            'duration' => $request->input('animation_duration', 1000),
            'delay' => $request->input('animation_delay', 200),
        ]);

        $slider->update($data);

        return redirect()->route('sliders.index')
            ->with('success', 'Slider updated successfully!');
    }

    /**
     * Remove the specified slider
     */
    public function destroy(HomepageSlider $slider)
    {
        $slider->delete();

        return redirect()->route('sliders.index')
            ->with('success', 'Slider deleted successfully!');
    }

    /**
     * Toggle slider status
     */
    public function toggleStatus(HomepageSlider $slider)
    {
        try {
            $slider->toggleStatus();

            return response()->json([
                'success' => true,
                'status' => $slider->status,
                'message' => 'Slider status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling slider status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update slider status'
            ], 500);
        }
    }

    /**
     * Reorder sliders
     */
    public function reorder(Request $request)
    {
        try {
            $order = $request->input('order', []);

            if (empty($order)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No order data provided'
                ], 400);
            }

            HomepageSlider::reorder($order);

            return response()->json([
                'success' => true,
                'message' => 'Sliders reordered successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error reordering sliders: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder sliders'
            ], 500);
        }
    }

    /**
     * Get sliders for API
     */
    public function api()
    {
        try {
            $sliders = HomepageSlider::active()->ordered()->get();

            $sliders = $sliders->map(function ($slider) {
                return [
                    'id' => $slider->id,
                    'title' => $slider->title,
                    'subtitle' => $slider->subtitle,
                    'description' => $slider->description,
                    'secondary_description' => $slider->secondary_description,
                    'button_text' => $slider->button_text,
                    'button_link' => $slider->button_link,
                    'media_type' => $slider->media_type,
                    'image_url' => $slider->image_url,
                    'video_url' => $slider->video_url,
                    'background_color' => $slider->background_color,
                    'text_color' => $slider->text_color,
                    'text_alignment' => $slider->text_alignment,
                    'display_duration' => $slider->display_duration,
                    'animation_settings' => $slider->animation_settings,
                    'status' => $slider->status,
                    'sort_order' => $slider->sort_order,
                ];
            });

            return response()->json($sliders);
        } catch (\Exception $e) {
            Log::error('Error fetching sliders API: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Duplicate a slider
     */
    public function duplicate(HomepageSlider $slider)
    {
        $newSlider = $slider->replicate();
        $newSlider->title = $slider->title . ' (Copy)';
        $newSlider->status = 'inactive';
        $newSlider->save();

        return redirect()->route('sliders.index')
            ->with('success', 'Slider duplicated successfully!');
    }
}
