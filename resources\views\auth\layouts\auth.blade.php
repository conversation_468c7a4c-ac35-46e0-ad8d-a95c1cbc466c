<!DOCTYPE html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable" data-theme="default" data-theme-colors="default">

<head>
    <meta charset="utf-8" />
    <title>@yield('title', 'GrandTek Admin | Authentication')</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="GrandTek IT Solutions Admin Authentication" name="description" />
    <meta content="GrandTek" name="author" />
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('backend/images/logo.png') }}">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Grand-Style CSS -->
    <link href="{{ asset('backend/css/grand-style.css') }}" rel="stylesheet" type="text/css" />

    <!-- Custom Authentication CSS -->
    <link href="{{ asset('backend/css/auth-styles.css') }}" rel="stylesheet" type="text/css" />

    <!-- Custom Authentication CSS -->
    <style>
        :root {
            /* Light mode colors */
            --bg-color: #f8f9fa;
            --card-bg: #f8f9fa;
            --text-color: #333;
            --border-color: #ddd;
            --input-bg: transparent;
            --placeholder-color: #999;
            --button-bg: #333;
            --button-text: #fff;
            --link-color: #333;
            --toggle-border: #ddd;
        }

        [data-theme="dark"] {
            /* Dark mode colors */
            --bg-color: #1a1a1a;
            --card-bg: #1a1a1a;
            --text-color: #fff;
            --border-color: #444;
            --input-bg: transparent;
            --placeholder-color: #888;
            --button-bg: #fff;
            --button-text: #333;
            --link-color: #fff;
            --toggle-border: #444;
        }

        body {
            background: var(--bg-color);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .auth-page-wrapper {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--bg-color);
            border: 2px solid var(--toggle-border);
            border-radius: 50px;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-color);
        }

        .theme-toggle:hover {
            border-color: var(--text-color);
            transform: scale(1.1);
        }

        .auth-page-content {
            width: 100%;
            max-width: 400px;
        }

        .auth-card {
            background: var(--card-bg);
            padding: 0;
            border: none;
            box-shadow: none;
        }

        .auth-logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-logo img {
            width: 60px;
            height: 60px;
            object-fit: contain;
            opacity: 0.8;
        }

        .auth-card-header {
            background: none;
            color: var(--text-color);
            padding: 0 0 3rem 0;
            text-align: left;
            border: none;
        }

        .auth-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            color: var(--text-color);
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .auth-subtitle {
            display: none;
        }

        .auth-card-body {
            padding: 0;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: none;
        }

        .form-control {
            border: none;
            border-bottom: 2px solid var(--border-color);
            border-radius: 0;
            padding: 1rem 0;
            font-size: 1rem;
            background: var(--input-bg);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-bottom-color: var(--text-color);
            box-shadow: none;
            background: var(--input-bg);
            outline: none;
        }

        .form-control::placeholder {
            color: var(--placeholder-color);
            font-size: 1rem;
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            display: none;
        }

        .input-group .form-control {
            border-bottom: 2px solid var(--border-color);
            padding-right: 40px;
        }

        .input-group button {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--placeholder-color);
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: color 0.3s ease;
        }

        .input-group button:hover {
            color: var(--text-color);
        }

        .btn-auth {
            background: var(--button-bg);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: var(--button-text);
            width: auto;
            float: right;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .btn-auth:hover {
            opacity: 0.8;
            transform: none;
            box-shadow: none;
        }

        .btn-auth i {
            margin-left: 0.5rem;
        }

        .auth-footer {
            text-align: left;
            padding: 2rem 0 0 0;
            background: none;
            border: none;
        }

        .auth-link {
            color: var(--link-color);
            text-decoration: underline;
            font-weight: normal;
            transition: opacity 0.3s ease;
        }

        .auth-link:hover {
            opacity: 0.7;
        }

        .social-login {
            margin: 2rem 0;
        }

        .social-icons {
            display: flex;
            gap: 1rem;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-size: 1.2rem;
            text-decoration: none;
            transition: opacity 0.3s ease;
        }

        .social-icon:hover {
            opacity: 0.7;
        }

        .forgot-password {
            color: var(--link-color);
            text-decoration: underline;
            font-size: 0.9rem;
            margin-top: 1rem;
            display: inline-block;
            transition: opacity 0.3s ease;
        }

        .forgot-password:hover {
            opacity: 0.7;
        }

        .form-check {
            margin: 1.5rem 0;
        }

        .form-check-input {
            margin-right: 0.5rem;
            accent-color: var(--button-bg);
        }

        .form-check-label {
            font-size: 0.9rem;
            color: var(--placeholder-color);
        }

        .alert {
            border: none;
            border-radius: 0;
            padding: 1rem 0;
            margin-bottom: 1.5rem;
            background: none;
        }

        .alert-danger {
            color: #d32f2f;
            border-bottom: 2px solid #d32f2f;
        }

        .alert-success {
            color: #388e3c;
            border-bottom: 2px solid #388e3c;
        }

        .invalid-feedback {
            color: #d32f2f;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .password-strength {
            margin-top: 0.5rem;
        }

        .password-strength .progress {
            height: 2px;
            background-color: var(--border-color);
        }

        .password-strength small {
            font-size: 0.8rem;
            color: var(--placeholder-color);
        }

        #passwordMatch small {
            color: #388e3c;
        }

        #passwordMismatch small {
            color: #d32f2f;
        }

        /* Responsive Design */
        @media (max-width: 576px) {
            .auth-page-wrapper {
                padding: 15px;
            }

            .auth-title {
                font-size: 2rem;
            }

            .btn-auth {
                width: 100%;
                float: none;
                text-align: center;
            }
        }
    </style>

    @stack('styles')
</head>

<body>
    <div class="auth-page-wrapper">
        <!-- Theme Toggle -->
        <button class="theme-toggle" id="theme-toggle" title="Toggle Theme">
            <i class="ri-moon-line" id="theme-icon"></i>
        </button>

        <div class="auth-page-content">
            @yield('content')
        </div>
    </div>

    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Authentication JavaScript -->
    <script>
        // Theme management
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        }

        function updateThemeIcon(theme) {
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.className = theme === 'dark' ? 'ri-sun-line' : 'ri-moon-line';
            }
        }

        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme
            initTheme();

            // Theme toggle event
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleTheme);
            }

            // Add loading state to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.classList.add('loading');
                        submitBtn.disabled = true;
                    }
                });
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });

            // Add focus effects to input groups
            const inputGroups = document.querySelectorAll('.input-group');
            inputGroups.forEach(group => {
                const input = group.querySelector('.form-control');
                if (input) {
                    input.addEventListener('focus', () => {
                        group.classList.add('focused');
                    });
                    input.addEventListener('blur', () => {
                        group.classList.remove('focused');
                    });
                }
            });
        });
    </script>

    @stack('scripts')
</body>

</html>
