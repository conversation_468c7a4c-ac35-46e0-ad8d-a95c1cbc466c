<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_ratings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('system_id')->constrained('systems')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('user_name')->nullable(); // For anonymous ratings
            $table->string('user_email')->nullable(); // For anonymous ratings
            $table->tinyInteger('rating')->unsigned(); // 1-5 stars (using tinyInteger for better validation)
            $table->text('review')->nullable(); // Optional review text
            $table->string('ip_address')->nullable(); // Track IP for anonymous ratings
            $table->boolean('is_verified')->default(false); // For verified ratings
            $table->boolean('is_featured')->default(false); // Featured reviews
            $table->enum('status', ['active', 'inactive', 'pending'])->default('active');
            $table->timestamps();
            
            // Indexes
            $table->index(['system_id', 'status']);
            $table->index(['user_id', 'system_id']);
            $table->index('rating');
            $table->index('is_featured');
            
            // Unique constraint to prevent duplicate ratings from same user
            $table->unique(['system_id', 'user_id'], 'unique_user_system_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_ratings');
    }
};
