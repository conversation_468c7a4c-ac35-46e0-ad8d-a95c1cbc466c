{{-- Local Business Schema for SEO --}}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "@id": "{{ config('seo.site_url') }}#organization",
  "name": "{{ config('seo.business.name') }}",
  "alternateName": "GrandTek",
  "description": "{{ config('seo.business.description') }}",
  "url": "{{ config('seo.business.url') }}",
  "logo": {
    "@type": "ImageObject",
    "url": "{{ asset(config('seo.business.logo')) }}",
    "width": 300,
    "height": 100
  },
  "image": {
    "@type": "ImageObject", 
    "url": "{{ asset(config('seo.business.image')) }}",
    "width": 1200,
    "height": 630
  },
  "telephone": "{{ config('seo.business.telephone') }}",
  "email": "{{ config('seo.business.email') }}",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "{{ config('seo.business.address.street_address') }}",
    "addressLocality": "{{ config('seo.business.address.locality') }}",
    "addressRegion": "{{ config('seo.business.address.region') }}",
    "postalCode": "{{ config('seo.business.address.postal_code') }}",
    "addressCountry": "{{ config('seo.business.address.country') }}"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": {{ config('seo.business.geo.latitude') }},
    "longitude": {{ config('seo.business.geo.longitude') }}
  },
  "openingHours": "{{ config('seo.business.opening_hours') }}",
  "priceRange": "{{ config('seo.business.price_range') }}",
  "areaServed": [
    @foreach(config('seo.business.areas_served') as $area)
    {
      "@type": "{{ $loop->first ? 'Country' : 'City' }}",
      "name": "{{ $area }}"
    }{{ !$loop->last ? ',' : '' }}
    @endforeach
  ],
  "serviceArea": {
    "@type": "GeoCircle",
    "geoMidpoint": {
      "@type": "GeoCoordinates",
      "latitude": {{ config('seo.business.geo.latitude') }},
      "longitude": {{ config('seo.business.geo.longitude') }}
    },
    "geoRadius": "1000000"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Software Development Services",
    "itemListElement": [
      @foreach(config('seo.service_categories') as $category => $services)
      @foreach($services as $service)
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "{{ $service }}",
          "description": "Professional {{ strtolower($service) }} services in Kenya",
          "category": "{{ $category }}",
          "areaServed": {
            "@type": "Country",
            "name": "Kenya"
          }
        }
      }{{ !($loop->parent->last && $loop->last) ? ',' : '' }}
      @endforeach
      @endforeach
    ]
  },
  "sameAs": [
    @foreach(config('seo.social_media') as $platform => $url)
    "{{ $url }}"{{ !$loop->last ? ',' : '' }}
    @endforeach
  ],
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "reviewCount": "127",
    "bestRating": "5",
    "worstRating": "1"
  },
  "review": [
    {
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": "John Kamau"
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5",
        "bestRating": "5"
      },
      "reviewBody": "Excellent software development services. GrandTek delivered our e-commerce platform on time and within budget. Highly recommended for businesses in Kenya."
    },
    {
      "@type": "Review", 
      "author": {
        "@type": "Person",
        "name": "Sarah Wanjiku"
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5",
        "bestRating": "5"
      },
      "reviewBody": "Professional team with deep expertise in Laravel and React. They understood our business requirements and delivered a perfect solution."
    },
    {
      "@type": "Review",
      "author": {
        "@type": "Person", 
        "name": "David Ochieng"
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "4",
        "bestRating": "5"
      },
      "reviewBody": "Great mobile app development services. The team was responsive and delivered quality work. Will definitely work with them again."
    }
  ]
}
</script>

{{-- FAQ Schema for Local SEO --}}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Do you provide software development services in Nairobi?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, we are based in Nairobi and provide comprehensive software development services throughout Kenya including custom web applications, mobile apps, and enterprise solutions."
      }
    },
    {
      "@type": "Question",
      "name": "What programming languages and frameworks do you use?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We specialize in modern technologies including Laravel, React, Vue.js, Node.js, React Native, Flutter, MySQL, PostgreSQL, and cloud platforms like AWS and Google Cloud."
      }
    },
    {
      "@type": "Question",
      "name": "How much does custom software development cost in Kenya?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Software development costs vary based on project complexity, features, and timeline. We offer competitive rates and provide detailed quotes after understanding your specific requirements. Contact us for a free consultation."
      }
    },
    {
      "@type": "Question",
      "name": "Do you integrate with M-Pesa and other Kenyan payment systems?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, we have extensive experience integrating M-Pesa, Airtel Money, and other local payment systems into web and mobile applications for Kenyan businesses."
      }
    },
    {
      "@type": "Question",
      "name": "What industries do you serve in Kenya?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We serve various industries including fintech, e-commerce, healthcare, education, agriculture, logistics, and government sectors across Kenya."
      }
    }
  ]
}
</script>

{{-- Breadcrumb Schema --}}
@if(isset($breadcrumbs) && count($breadcrumbs) > 1)
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    @foreach($breadcrumbs as $index => $breadcrumb)
    {
      "@type": "ListItem",
      "position": {{ $index + 1 }},
      "name": "{{ $breadcrumb['name'] }}",
      "item": "{{ $breadcrumb['url'] }}"
    }{{ !$loop->last ? ',' : '' }}
    @endforeach
  ]
}
</script>
@endif
