@extends('backend.layouts.app')

@section('title', 'Team Management')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Team Management</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Team</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Team Management Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">Team Members ({{ $teams->count() }})</h5>
                        <p class="text-muted mb-0">Manage your team members and their information</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" id="reorderBtn">
                            <i class="ri-drag-move-line me-1"></i>Reorder
                        </button>
                        <a href="{{ route('teams.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Team Member
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Team Grid -->
<div class="row" id="teamsContainer">
    @forelse($teams as $team)
        <div class="col-xl-4 col-lg-6 col-md-6 team-item" data-id="{{ $team->id }}">
            <div class="card team-card h-100">
                <!-- Team Member Image -->
                <div class="team-media-preview position-relative">
                    @if($team->team_image_url)
                        <img src="{{ $team->team_image_url }}" class="card-img-top" 
                             style="height: 200px; object-fit: cover;" alt="{{ $team->name }}">
                    @else
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <div class="text-center">
                                <i class="ri-user-line text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">No Image</p>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Status Badge -->
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge {{ $team->status === 'active' ? 'bg-success' : 'bg-secondary' }}">
                            {{ ucfirst($team->status) }}
                        </span>
                    </div>
                    
                    <!-- Drag Handle (hidden by default) -->
                    <div class="drag-handle position-absolute top-50 start-50 translate-middle" style="display: none;">
                        <i class="ri-drag-move-2-line text-white bg-dark rounded p-2"></i>
                    </div>
                </div>

                <div class="card-body">
                    <h5 class="card-title">{{ $team->name }}</h5>
                    <p class="text-primary fw-medium">{{ $team->designation }}</p>
                    
                    @if($team->bio)
                        <p class="card-text text-muted small">{{ Str::limit($team->bio, 100) }}</p>
                    @endif
                    
                    @if($team->email || $team->phone)
                        <div class="contact-info mb-3">
                            @if($team->email)
                                <small class="text-muted d-block">
                                    <i class="ri-mail-line me-1"></i>{{ $team->email }}
                                </small>
                            @endif
                            @if($team->phone)
                                <small class="text-muted d-block">
                                    <i class="ri-phone-line me-1"></i>{{ $team->phone }}
                                </small>
                            @endif
                        </div>
                    @endif
                    
                    <!-- Social Media Links -->
                    @if($team->facebook || $team->twitter || $team->instagram || $team->linkedin)
                        <div class="social-links mb-3">
                            @if($team->facebook)
                                <a href="{{ $team->facebook }}" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                    <i class="ri-facebook-line"></i>
                                </a>
                            @endif
                            @if($team->twitter)
                                <a href="{{ $team->twitter }}" target="_blank" class="btn btn-sm btn-outline-info me-1">
                                    <i class="ri-twitter-line"></i>
                                </a>
                            @endif
                            @if($team->instagram)
                                <a href="{{ $team->instagram }}" target="_blank" class="btn btn-sm btn-outline-danger me-1">
                                    <i class="ri-instagram-line"></i>
                                </a>
                            @endif
                            @if($team->linkedin)
                                <a href="{{ $team->linkedin }}" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                    <i class="ri-linkedin-line"></i>
                                </a>
                            @endif
                        </div>
                    @endif
                </div>

                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            Order: {{ $team->sort_order }}
                        </small>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                Actions
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('teams.show', $team) }}">
                                    <i class="ri-eye-line me-2"></i>View
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('teams.edit', $team) }}">
                                    <i class="ri-edit-line me-2"></i>Edit
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item toggle-status" href="#" data-id="{{ $team->id }}" 
                                       data-status="{{ $team->status }}">
                                    <i class="ri-toggle-line me-2"></i>
                                    {{ $team->status === 'active' ? 'Deactivate' : 'Activate' }}
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="duplicateTeam({{ $team->id }})">
                                    <i class="ri-file-copy-line me-2"></i>Duplicate
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteTeam({{ $team->id }})">
                                    <i class="ri-delete-bin-line me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-team-line text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 mb-3">No Team Members Found</h4>
                    <p class="text-muted mb-4">Start building your team by adding your first team member.</p>
                    <a href="{{ route('teams.create') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Add First Team Member
                    </a>
                </div>
            </div>
        </div>
    @endforelse
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this team member? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Duplicate Form -->
<form id="duplicateForm" method="POST" style="display: none;">
    @csrf
</form>
@endsection

@push('styles')
<style>
.team-card {
    transition: transform 0.2s ease-in-out;
    border: 1px solid #e9ecef;
}

.team-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.team-item.reorder-mode .team-card {
    cursor: move;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    transform: scale(1.05);
}

.social-links .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.contact-info {
    font-size: 0.875rem;
}

.team-media-preview {
    position: relative;
    overflow: hidden;
}

.team-media-preview img {
    transition: transform 0.3s ease;
}

.team-card:hover .team-media-preview img {
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .team-card .card-img-top {
        height: 180px !important;
    }
}
</style>
@endpush

@push('scripts')
<!-- Sortable.js for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
let sortable = null;
let isReorderMode = false;

document.addEventListener('DOMContentLoaded', function() {
    initializeReorderMode();
    initializeStatusToggle();
});

function initializeReorderMode() {
    const reorderBtn = document.getElementById('reorderBtn');
    if (reorderBtn) {
        reorderBtn.addEventListener('click', function() {
            if (isReorderMode) {
                disableReorderMode();
            } else {
                enableReorderMode();
            }
        });
    }
}

function enableReorderMode() {
    const container = document.getElementById('teamsContainer');
    const items = container.querySelectorAll('.team-item');

    // Show drag handles
    items.forEach(item => {
        item.classList.add('reorder-mode');
        const dragHandle = item.querySelector('.drag-handle');
        if (dragHandle) {
            dragHandle.style.display = 'block';
        }
    });

    // Initialize sortable
    sortable = Sortable.create(container, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        handle: '.team-card',
        onEnd: function(evt) {
            // Auto-save order when item is moved
            saveOrder();
        }
    });

    // Update button
    const reorderBtn = document.getElementById('reorderBtn');
    reorderBtn.innerHTML = '<i class="ri-save-line me-1"></i>Save Order';
    reorderBtn.classList.remove('btn-outline-primary');
    reorderBtn.classList.add('btn-success');

    isReorderMode = true;
}

function disableReorderMode() {
    const container = document.getElementById('teamsContainer');
    const items = container.querySelectorAll('.team-item');

    // Hide drag handles
    items.forEach(item => {
        item.classList.remove('reorder-mode');
        const dragHandle = item.querySelector('.drag-handle');
        if (dragHandle) {
            dragHandle.style.display = 'none';
        }
    });

    // Destroy sortable
    if (sortable) {
        sortable.destroy();
        sortable = null;
    }

    // Update button
    const reorderBtn = document.getElementById('reorderBtn');
    reorderBtn.innerHTML = '<i class="ri-drag-move-line me-1"></i>Reorder';
    reorderBtn.classList.remove('btn-success');
    reorderBtn.classList.add('btn-outline-primary');

    isReorderMode = false;
}

function saveOrder() {
    const container = document.getElementById('teamsContainer');
    const items = container.querySelectorAll('.team-item');
    const order = Array.from(items).map(item => item.dataset.id);

    fetch('{{ route("teams.reorder") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ order: order })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showToast('success', data.message);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to save order');
    });
}

function initializeStatusToggle() {
    document.querySelectorAll('.toggle-status').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const teamId = this.dataset.id;
            const currentStatus = this.dataset.status;

            toggleTeamStatus(teamId, currentStatus);
        });
    });
}

function toggleTeamStatus(teamId, currentStatus) {
    fetch(`/admin/teams/${teamId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload page to update UI
            location.reload();
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to update status');
    });
}

function deleteTeam(teamId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/teams/${teamId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function duplicateTeam(teamId) {
    const duplicateForm = document.getElementById('duplicateForm');
    duplicateForm.action = `/admin/teams/${teamId}/duplicate`;
    duplicateForm.submit();
}

function showToast(type, message) {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="ri-${type === 'success' ? 'check' : 'error'}-warning-line me-2"></i>
            ${message}
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
</script>
@endpush
