@extends('frontend.layouts.app')

@section('title', 'Blog - Latest Articles & News | GrandTek IT Solutions')
@section('description', 'Read our latest blog posts about software development, technology trends, and IT solutions in Kenya. Expert insights and tutorials from GrandTek IT Solutions.')
@section('keywords', 'blog, software development blog, technology news Kenya, IT articles, programming tutorials, web development blog, mobile app development blog')

@section('og_title', 'Blog - Latest Articles & News | GrandTek IT Solutions')
@section('og_description', 'Read our latest blog posts about software development, technology trends, and IT solutions in Kenya.')

@section('content')
<div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
    <div class="container py-5">
        <h1 class="display-3 text-white mb-3 animated slideInDown">Our Blog</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb text-uppercase mb-0">
                <li class="breadcrumb-item"><a class="text-white" href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item text-primary active" aria-current="page">Blog</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container-fluid py-5">
    <div class="container">
        <!-- Search and Filter Section -->
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto">
                <form action="{{ route('blog.index') }}" method="GET" class="d-flex gap-3">
                    <input type="text" name="search" class="form-control" placeholder="Search blog posts..." value="{{ request('search') }}">
                    <select name="category" class="form-select" style="min-width: 150px;">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>{{ $category }}</option>
                        @endforeach
                    </select>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>

        <!-- Featured Posts Section -->
        @if($featuredPosts && $featuredPosts->count() > 0)
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="mb-4">Featured Posts</h3>
                <div class="row">
                    @foreach($featuredPosts as $post)
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <img src="{{ $post->featured_image_url }}" class="card-img-top" alt="{{ $post->title }}" style="height: 200px; object-fit: cover;">
                            <div class="card-body d-flex flex-column">
                                <span class="badge bg-primary mb-2 align-self-start">Featured</span>
                                <h5 class="card-title">{{ $post->title }}</h5>
                                <p class="card-text flex-grow-1">{{ Str::limit($post->excerpt, 100) }}</p>
                                <div class="mt-auto">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>{{ $post->author_name ?: 'Administrator' }}
                                        <i class="fas fa-calendar ms-3 me-1"></i>{{ $post->formatted_published_date }}
                                    </small>
                                    <div class="mt-2">
                                        <a href="{{ route('blog.show', $post->slug) }}" class="btn btn-primary btn-sm">Read More</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- All Posts Section -->
        <div class="row">
            <div class="col-12">
                <h3 class="mb-4">
                    @if(request('search'))
                        Search Results for "{{ request('search') }}"
                    @elseif(request('category'))
                        Posts in "{{ request('category') }}"
                    @else
                        All Posts
                    @endif
                </h3>

                @if($posts->count() > 0)
                    <div class="row">
                        @foreach($posts as $post)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm">
                                <img src="{{ $post->featured_image_url }}" class="card-img-top" alt="{{ $post->title }}" style="height: 200px; object-fit: cover;">
                                <div class="card-body d-flex flex-column">
                                    @if($post->categories && count($post->categories) > 0)
                                        <span class="badge bg-secondary mb-2 align-self-start">{{ $post->categories[0] }}</span>
                                    @endif
                                    <h5 class="card-title">{{ $post->title }}</h5>
                                    <p class="card-text flex-grow-1">{{ Str::limit($post->excerpt, 120) }}</p>
                                    <div class="mt-auto">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ $post->author_name ?: 'Administrator' }}
                                            <i class="fas fa-calendar ms-3 me-1"></i>{{ $post->formatted_published_date }}
                                        </small>
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <a href="{{ route('blog.show', $post->slug) }}" class="btn btn-primary btn-sm">Read More</a>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i>{{ $post->views_count }}
                                                <i class="fas fa-clock ms-2 me-1"></i>{{ $post->reading_time }}min
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="row mt-5">
                        <div class="col-12 d-flex justify-content-center">
                            {{ $posts->appends(request()->query())->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>No posts found</h4>
                        <p class="text-muted">Try adjusting your search criteria or browse all posts.</p>
                        <a href="{{ route('blog.index') }}" class="btn btn-primary">View All Posts</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
