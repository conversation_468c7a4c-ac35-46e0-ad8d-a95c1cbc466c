<?php $__env->startSection('title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions'); ?>
<?php $__env->startSection('description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions. Expert Laravel, React, and database development services in Nairobi and across Kenya.'); ?>
<?php $__env->startSection('keywords', 'software development Kenya, custom software solutions, web development Nairobi, mobile app development, IT consulting Kenya, software company Kenya, Laravel development, React development, database solutions Kenya, business software Kenya'); ?>

<?php $__env->startSection('og_type', 'website'); ?>
<?php $__env->startSection('og_title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions'); ?>
<?php $__env->startSection('og_description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions. Expert Laravel, React, and database development services in Nairobi and across Kenya.'); ?>
<?php $__env->startSection('og_image', asset('assets/img/grandtek-homepage-og.jpg')); ?>

<?php $__env->startSection('twitter_title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions'); ?>
<?php $__env->startSection('twitter_description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions.'); ?>

<?php $__env->startPush('structured_data'); ?>
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "GrandTek IT Solutions",
  "alternateName": "GrandTek Kenya",
  "url": "<?php echo e(url('/')); ?>",
  "description": "Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions.",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "<?php echo e(url('/')); ?>?search={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GrandTek IT Solutions Kenya Limited",
    "url": "<?php echo e(url('/')); ?>",
    "logo": {
      "@type": "ImageObject",
      "url": "<?php echo e(asset('assets/img/grandtek-logo.png')); ?>"
    }
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What software development services do you offer in Kenya?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We offer comprehensive software development services including custom web applications, mobile app development, database solutions, e-commerce platforms, and IT consulting services across Kenya."
      }
    },
    {
      "@type": "Question",
      "name": "Do you provide software development services in Nairobi?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, we are based in Nairobi and provide software development services throughout Kenya including Nairobi, Mombasa, Kisumu, and other major cities."
      }
    },
    {
      "@type": "Question",
      "name": "What technologies do you use for software development?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We specialize in modern technologies including Laravel, React, Vue.js, Node.js, MySQL, PostgreSQL, and mobile development frameworks for creating robust software solutions."
      }
    }
  ]
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    
    <?php echo $__env->make('frontend.include.sliders', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.aboutus', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.services', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.projects', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.team', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.testimonial', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.systems', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.blog', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php echo $__env->make('frontend.include.contact', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/index.blade.php ENDPATH**/ ?>