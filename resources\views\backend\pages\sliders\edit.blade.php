@extends('backend.layouts.app')

@section('title', 'Edit Slider - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Edit Slider</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('sliders.index') }}">Sliders</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<form action="{{ route('sliders.update', $slider) }}" method="POST" enctype="multipart/form-data" id="sliderForm">
    @csrf
    @method('PUT')
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Slider Content</h5>
                </div>
                <div class="card-body">
                    <!-- Title -->
                    <div class="mb-3">
                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title', $slider->title) }}" 
                               placeholder="Enter slider title" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Subtitle -->
                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subtitle</label>
                        <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" name="subtitle" value="{{ old('subtitle', $slider->subtitle) }}" 
                               placeholder="Enter slider subtitle">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="4" 
                                  placeholder="Enter slider description">{{ old('description', $slider->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Button Settings -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                       id="button_text" name="button_text" value="{{ old('button_text', $slider->button_text) }}" 
                                       placeholder="e.g., Learn More">
                                @error('button_text')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="button_link" class="form-label">Button Link</label>
                                <input type="url" class="form-control @error('button_link') is-invalid @enderror" 
                                       id="button_link" name="button_link" value="{{ old('button_link', $slider->button_link) }}" 
                                       placeholder="https://example.com">
                                @error('button_link')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Upload -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Media Upload</h5>
                </div>
                <div class="card-body">
                    <!-- Current Media Preview -->
                    @if($slider->hasMedia())
                        <div class="mb-3">
                            <label class="form-label">Current Media</label>
                            <div class="current-media-preview">
                                @if($slider->media_type === 'video' && $slider->video_path)
                                    <video controls style="max-height: 200px; max-width: 100%;">
                                        <source src="{{ Storage::url($slider->video_path) }}" type="video/mp4">
                                    </video>
                                @elseif($slider->image_path)
                                    <img src="{{ Storage::url($slider->image_path) }}" class="img-thumbnail" 
                                         style="max-height: 200px;" alt="{{ $slider->title }}">
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Media Type Selection -->
                    <div class="mb-3">
                        <label class="form-label">Media Type <span class="text-danger">*</span></label>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="media_type" 
                                       id="media_image" value="image" 
                                       {{ old('media_type', $slider->media_type) === 'image' ? 'checked' : '' }}>
                                <label class="form-check-label" for="media_image">
                                    <i class="ri-image-line me-1"></i>Image
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="media_type" 
                                       id="media_video" value="video" 
                                       {{ old('media_type', $slider->media_type) === 'video' ? 'checked' : '' }}>
                                <label class="form-check-label" for="media_video">
                                    <i class="ri-video-line me-1"></i>Video
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div class="mb-3" id="imageUpload">
                        <label for="image" class="form-label">Slider Image</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        <div class="form-text">Leave empty to keep current image. Recommended size: 1920x1080px. Max size: 5MB</div>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div id="imagePreview" class="mt-2"></div>
                    </div>

                    <!-- Video Upload -->
                    <div class="mb-3" id="videoUpload" style="display: none;">
                        <label for="video" class="form-label">Slider Video</label>
                        <input type="file" class="form-control @error('video') is-invalid @enderror" 
                               id="video" name="video" accept="video/*">
                        <div class="form-text">Leave empty to keep current video. Supported formats: MP4, WebM, OGG. Max size: 50MB</div>
                        @error('video')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div id="videoPreview" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Settings -->
        <div class="col-lg-4">
            <!-- Status & Visibility -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Status & Visibility</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                            <option value="active" {{ old('status', $slider->status) === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status', $slider->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Design Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Design Settings</h5>
                </div>
                <div class="card-body">
                    <!-- Text Alignment -->
                    <div class="mb-3">
                        <label for="text_alignment" class="form-label">Text Alignment</label>
                        <select class="form-select @error('text_alignment') is-invalid @enderror"
                                id="text_alignment" name="text_alignment">
                            <option value="left" {{ old('text_alignment', $slider->text_alignment) === 'left' ? 'selected' : '' }}>Left</option>
                            <option value="center" {{ old('text_alignment', $slider->text_alignment) === 'center' ? 'selected' : '' }}>Center</option>
                            <option value="right" {{ old('text_alignment', $slider->text_alignment) === 'right' ? 'selected' : '' }}>Right</option>
                        </select>
                        @error('text_alignment')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Display Duration -->
                    <div class="mb-3">
                        <label for="display_duration" class="form-label">Display Duration <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control @error('display_duration') is-invalid @enderror"
                                   id="display_duration" name="display_duration"
                                   value="{{ old('display_duration', $slider->display_duration ?? 12) }}"
                                   min="3" max="60" step="1" required>
                            <span class="input-group-text">seconds</span>
                        </div>
                        <div class="form-text">
                            <i class="ri-information-line me-1"></i>
                            How long this slide will be displayed (3-60 seconds). Recommended: 8-15 seconds.
                        </div>
                        @error('display_duration')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Text Color -->
                    <div class="mb-3">
                        <label for="text_color" class="form-label">Text Color</label>
                        <input type="color" class="form-control form-control-color @error('text_color') is-invalid @enderror" 
                               id="text_color" name="text_color" value="{{ old('text_color', $slider->text_color) }}">
                        @error('text_color')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Background Color -->
                    <div class="mb-3">
                        <label for="background_color" class="form-label">Background Overlay Color</label>
                        <input type="color" class="form-control form-control-color @error('background_color') is-invalid @enderror" 
                               id="background_color" name="background_color" value="{{ old('background_color', $slider->background_color) }}">
                        <div class="form-text">Optional overlay color for better text readability</div>
                        @error('background_color')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Animation Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Animation Settings</h5>
                </div>
                <div class="card-body">
                    @php
                        $animations = $slider->animation_settings;
                    @endphp
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="title_animation" class="form-label">Title Animation</label>
                                <select class="form-select" id="title_animation" name="title_animation">
                                    <option value="fadeInUp" {{ ($animations['title_animation'] ?? 'fadeInUp') === 'fadeInUp' ? 'selected' : '' }}>Fade In Up</option>
                                    <option value="fadeInDown" {{ ($animations['title_animation'] ?? '') === 'fadeInDown' ? 'selected' : '' }}>Fade In Down</option>
                                    <option value="fadeInLeft" {{ ($animations['title_animation'] ?? '') === 'fadeInLeft' ? 'selected' : '' }}>Fade In Left</option>
                                    <option value="fadeInRight" {{ ($animations['title_animation'] ?? '') === 'fadeInRight' ? 'selected' : '' }}>Fade In Right</option>
                                    <option value="zoomIn" {{ ($animations['title_animation'] ?? '') === 'zoomIn' ? 'selected' : '' }}>Zoom In</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="subtitle_animation" class="form-label">Subtitle Animation</label>
                                <select class="form-select" id="subtitle_animation" name="subtitle_animation">
                                    <option value="fadeInLeft" {{ ($animations['subtitle_animation'] ?? 'fadeInLeft') === 'fadeInLeft' ? 'selected' : '' }}>Fade In Left</option>
                                    <option value="fadeInRight" {{ ($animations['subtitle_animation'] ?? '') === 'fadeInRight' ? 'selected' : '' }}>Fade In Right</option>
                                    <option value="fadeInUp" {{ ($animations['subtitle_animation'] ?? '') === 'fadeInUp' ? 'selected' : '' }}>Fade In Up</option>
                                    <option value="fadeInDown" {{ ($animations['subtitle_animation'] ?? '') === 'fadeInDown' ? 'selected' : '' }}>Fade In Down</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="animation_duration" class="form-label">Duration (ms)</label>
                                <input type="number" class="form-control" id="animation_duration" 
                                       name="animation_duration" value="{{ $animations['duration'] ?? 1000 }}" min="100" max="5000">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="animation_delay" class="form-label">Delay (ms)</label>
                                <input type="number" class="form-control" id="animation_delay" 
                                       name="animation_delay" value="{{ $animations['delay'] ?? 200 }}" min="0" max="2000">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Update Slider
                        </button>
                        <a href="{{ route('sliders.show', $slider) }}" class="btn btn-outline-info">
                            <i class="ri-eye-line me-1"></i>Preview Slider
                        </a>
                        <a href="{{ route('sliders.index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Sliders
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

@endsection

@push('scripts')
<script>
// Media type toggle
document.querySelectorAll('input[name="media_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        toggleMediaUpload();
    });
});

function toggleMediaUpload() {
    const mediaType = document.querySelector('input[name="media_type"]:checked').value;
    const imageUpload = document.getElementById('imageUpload');
    const videoUpload = document.getElementById('videoUpload');
    
    if (mediaType === 'image') {
        imageUpload.style.display = 'block';
        videoUpload.style.display = 'none';
    } else {
        imageUpload.style.display = 'none';
        videoUpload.style.display = 'block';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleMediaUpload();
});

// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('imagePreview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-height: 200px;">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
});

// Video preview
document.getElementById('video').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('videoPreview');
    
    if (file) {
        const url = URL.createObjectURL(file);
        preview.innerHTML = `<video controls style="max-height: 200px; max-width: 100%;">
                                <source src="${url}" type="${file.type}">
                            </video>`;
    } else {
        preview.innerHTML = '';
    }
});
</script>
@endpush
