@extends('backend.layouts.app')

@section('title', 'Testimonial Management')

@section('content')
<!-- <PERSON>er -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Testimonial Management</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Testimonials</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Testimonial Management Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">Client Testimonials ({{ $testimonials->count() }})</h5>
                        <p class="text-muted mb-0">Manage client testimonials and reviews</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" id="reorderBtn">
                            <i class="ri-drag-move-line me-1"></i>Reorder
                        </button>
                        <a href="{{ route('testimonials.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add Testimonial
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Testimonials Grid -->
<div class="row" id="testimonialsContainer">
    @forelse($testimonials as $testimonial)
        <div class="col-xl-6 col-lg-12 testimonial-item" data-id="{{ $testimonial->id }}">
            <div class="card testimonial-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-start">
                        <!-- Client Image -->
                        <div class="flex-shrink-0 me-3">
                            @if($testimonial->client_image_url)
                                <img src="{{ $testimonial->client_image_url }}" class="rounded-circle" 
                                     style="width: 60px; height: 60px; object-fit: cover;" alt="{{ $testimonial->name }}">
                            @else
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 60px; height: 60px;">
                                    <i class="ri-user-line text-muted" style="font-size: 1.5rem;"></i>
                                </div>
                            @endif
                        </div>
                        
                        <!-- Client Info -->
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-1">{{ $testimonial->name }}</h6>
                                    @if($testimonial->position || $testimonial->company)
                                        <p class="text-muted small mb-0">
                                            @if($testimonial->position){{ $testimonial->position }}@endif
                                            @if($testimonial->position && $testimonial->company) at @endif
                                            @if($testimonial->company){{ $testimonial->company }}@endif
                                        </p>
                                    @endif
                                </div>
                                
                                <!-- Status & Featured Badges -->
                                <div class="d-flex gap-1">
                                    <span class="badge {{ $testimonial->status === 'active' ? 'bg-success' : 'bg-secondary' }}">
                                        {{ ucfirst($testimonial->status) }}
                                    </span>
                                    @if($testimonial->featured)
                                        <span class="badge bg-warning">Featured</span>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Rating -->
                            <div class="mb-2">
                                {!! $testimonial->star_rating !!}
                                <small class="text-muted ms-1">({{ $testimonial->rating }}/5)</small>
                            </div>
                            
                            <!-- Comment -->
                            <blockquote class="blockquote mb-3">
                                <p class="mb-0 small">{{ Str::limit($testimonial->comment, 150) }}</p>
                            </blockquote>
                            
                            <!-- Meta Info -->
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    Order: {{ $testimonial->sort_order }} | 
                                    Added: {{ $testimonial->created_at->format('M d, Y') }}
                                </small>
                                
                                <!-- Actions -->
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('testimonials.show', $testimonial) }}">
                                            <i class="ri-eye-line me-2"></i>View
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('testimonials.edit', $testimonial) }}">
                                            <i class="ri-edit-line me-2"></i>Edit
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item toggle-status" href="#" data-id="{{ $testimonial->id }}" 
                                               data-status="{{ $testimonial->status }}">
                                            <i class="ri-toggle-line me-2"></i>
                                            {{ $testimonial->status === 'active' ? 'Deactivate' : 'Activate' }}
                                        </a></li>
                                        <li><a class="dropdown-item toggle-featured" href="#" data-id="{{ $testimonial->id }}" 
                                               data-featured="{{ $testimonial->featured ? 'true' : 'false' }}">
                                            <i class="ri-star-line me-2"></i>
                                            {{ $testimonial->featured ? 'Remove Featured' : 'Mark Featured' }}
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="duplicateTestimonial({{ $testimonial->id }})">
                                            <i class="ri-file-copy-line me-2"></i>Duplicate
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteTestimonial({{ $testimonial->id }})">
                                            <i class="ri-delete-bin-line me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Drag Handle (hidden by default) -->
                        <div class="drag-handle ms-2" style="display: none;">
                            <i class="ri-drag-move-2-line text-muted"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-chat-quote-line text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 mb-3">No Testimonials Found</h4>
                    <p class="text-muted mb-4">Start building trust by adding your first client testimonial.</p>
                    <a href="{{ route('testimonials.create') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Add First Testimonial
                    </a>
                </div>
            </div>
        </div>
    @endforelse
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this testimonial? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Duplicate Form -->
<form id="duplicateForm" method="POST" style="display: none;">
    @csrf
</form>
@endsection

@push('styles')
<style>
.testimonial-card {
    transition: transform 0.2s ease-in-out;
    border: 1px solid #e9ecef;
}

.testimonial-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.testimonial-item.reorder-mode .testimonial-card {
    cursor: move;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    transform: scale(1.02);
}

.blockquote {
    border-left: 3px solid var(--vz-primary);
    padding-left: 1rem;
    font-style: italic;
}

@media (max-width: 768px) {
    .testimonial-item {
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<!-- Sortable.js for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
let sortable = null;
let isReorderMode = false;

document.addEventListener('DOMContentLoaded', function() {
    initializeReorderMode();
    initializeStatusToggle();
    initializeFeaturedToggle();
});

function initializeReorderMode() {
    const reorderBtn = document.getElementById('reorderBtn');
    if (reorderBtn) {
        reorderBtn.addEventListener('click', function() {
            if (isReorderMode) {
                disableReorderMode();
            } else {
                enableReorderMode();
            }
        });
    }
}

function enableReorderMode() {
    const container = document.getElementById('testimonialsContainer');
    const items = container.querySelectorAll('.testimonial-item');

    // Show drag handles
    items.forEach(item => {
        item.classList.add('reorder-mode');
        const dragHandle = item.querySelector('.drag-handle');
        if (dragHandle) {
            dragHandle.style.display = 'block';
        }
    });

    // Initialize sortable
    sortable = Sortable.create(container, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        handle: '.testimonial-card',
        onEnd: function(evt) {
            // Auto-save order when item is moved
            saveOrder();
        }
    });

    // Update button
    const reorderBtn = document.getElementById('reorderBtn');
    reorderBtn.innerHTML = '<i class="ri-save-line me-1"></i>Save Order';
    reorderBtn.classList.remove('btn-outline-primary');
    reorderBtn.classList.add('btn-success');

    isReorderMode = true;
}

function disableReorderMode() {
    const container = document.getElementById('testimonialsContainer');
    const items = container.querySelectorAll('.testimonial-item');

    // Hide drag handles
    items.forEach(item => {
        item.classList.remove('reorder-mode');
        const dragHandle = item.querySelector('.drag-handle');
        if (dragHandle) {
            dragHandle.style.display = 'none';
        }
    });

    // Destroy sortable
    if (sortable) {
        sortable.destroy();
        sortable = null;
    }

    // Update button
    const reorderBtn = document.getElementById('reorderBtn');
    reorderBtn.innerHTML = '<i class="ri-drag-move-line me-1"></i>Reorder';
    reorderBtn.classList.remove('btn-success');
    reorderBtn.classList.add('btn-outline-primary');

    isReorderMode = false;
}

function saveOrder() {
    const container = document.getElementById('testimonialsContainer');
    const items = container.querySelectorAll('.testimonial-item');
    const order = Array.from(items).map(item => item.dataset.id);

    fetch('{{ route("testimonials.reorder") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ order: order })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to save order');
    });
}

function initializeStatusToggle() {
    document.querySelectorAll('.toggle-status').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const testimonialId = this.dataset.id;
            const currentStatus = this.dataset.status;

            toggleTestimonialStatus(testimonialId, currentStatus);
        });
    });
}

function initializeFeaturedToggle() {
    document.querySelectorAll('.toggle-featured').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const testimonialId = this.dataset.id;
            const currentFeatured = this.dataset.featured === 'true';

            toggleTestimonialFeatured(testimonialId, currentFeatured);
        });
    });
}

function toggleTestimonialStatus(testimonialId, currentStatus) {
    fetch(`/admin/testimonials/${testimonialId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to update status');
    });
}

function toggleTestimonialFeatured(testimonialId, currentFeatured) {
    fetch(`/admin/testimonials/${testimonialId}/toggle-featured`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to update featured status');
    });
}

function deleteTestimonial(testimonialId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/testimonials/${testimonialId}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function duplicateTestimonial(testimonialId) {
    const duplicateForm = document.getElementById('duplicateForm');
    duplicateForm.action = `/admin/testimonials/${testimonialId}/duplicate`;
    duplicateForm.submit();
}

function showToast(type, message) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="ri-${type === 'success' ? 'check' : 'error'}-warning-line me-2"></i>
            ${message}
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
</script>
@endpush
