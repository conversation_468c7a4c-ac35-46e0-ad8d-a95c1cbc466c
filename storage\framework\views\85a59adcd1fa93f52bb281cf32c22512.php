<?php if($about): ?>
<div class="container-fluid py-5 my-5" id="aboutus">
    <div class="container pt-5">
        <div class="row g-5">
            <div class="col-lg-5 col-md-6 col-sm-12 wow fadeIn" data-wow-delay=".3s">
                <div class="h-100 position-relative">
                    <img src="<?php echo e($about['aboutus_img_one_url']); ?>"
                         class="img-fluid w-75 rounded"
                         alt="<?php echo e($about['tittle'] ?? 'About Us'); ?>"
                         style="width: 500px; height: 450px; margin-bottom: 25%;">
                    <div class="position-absolute w-75" style="top: 25%; left: 25%;">
                        <img src="<?php echo e($about['aboutus_img_two_url']); ?>"
                             class="img-fluid w-75 rounded"
                             alt="Secondary Image"
                             style="width: 800px; height: 400px;">
                    </div>
                </div>
            </div>
            <div class="col-lg-7 col-md-4 col-sm-12 wow fadeIn" data-wow-delay=".5s">
                <h5 class="text-primary"><?php echo e($about['subtitle'] ?? 'About Us'); ?></h5>
                <h1 class="mb-4"><?php echo e($about['tittle']); ?></h1>
                <p><?php echo e($about['description']); ?></p>

                <?php if($about['short_description']): ?>
                    <p><?php echo e($about['short_description']); ?></p>
                <?php endif; ?>

                <!-- Statistics Row -->
                <?php if(isset($about['statistics']) && count($about['statistics']) > 0): ?>
                    <div class="row g-3 mb-4">
                        <?php $__currentLoopData = $about['statistics']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($stat['number'] > 0): ?>
                                <div class="col-6 col-md-3">
                                    <div class="text-center">
                                        <i class="<?php echo e($stat['icon']); ?> text-primary fs-4 mb-2"></i>
                                        <h4 class="text-primary mb-1"><?php echo e($stat['number']); ?><?php echo e($stat['suffix']); ?></h4>
                                        <small class="text-muted"><?php echo e($stat['label']); ?></small>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>

                <a href="<?php echo e($about['button_link'] ?? '/about'); ?>"
                   class="btn btn-secondary rounded-pill px-5 py-3 text-white">
                    <?php echo e($about['button_text'] ?? 'More Details'); ?>

                </a>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/include/aboutus.blade.php ENDPATH**/ ?>