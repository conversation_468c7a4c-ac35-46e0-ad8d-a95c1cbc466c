<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\HomepageSlider;

class HomepageSlidersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing sliders
        HomepageSlider::truncate();

        // Create sample sliders
        $sliders = [
            [
                'title' => 'An Innovative IT Solutions Agency',
                'subtitle' => 'Best IT Solutions',
                'description' => 'At GrandTek IT Solutions, we blend innovation with expertise to deliver cutting-edge technology solutions. From custom software development to robust security systems, we empower businesses to thrive in the digital age with reliable, efficient, and forward-thinking IT services.',
                'button_text' => 'Read More',
                'button_link' => '/',
                'media_type' => 'image',
                'image_path' => null, // Will use placeholder
                'status' => 'active',
                'sort_order' => 1,
                'background_color' => null,
                'text_color' => '#ffffff',
                'text_alignment' => 'left',
                'animation_settings' => json_encode([
                    'title_animation' => 'fadeInRight',
                    'subtitle_animation' => 'fadeInUp',
                    'description_animation' => 'fadeInDown',
                    'button_animation' => 'fadeInUp',
                    'duration' => 1000,
                    'delay' => 200,
                ]),
            ],
            [
                'title' => 'Comprehensive Off-Shelf Solutions',
                'subtitle' => 'Ready-Made Solutions',
                'description' => 'Discover our comprehensive range of off-the-shelf IT solutions designed to meet your immediate business needs. From enterprise software to cloud services, we provide tested and proven solutions that can be deployed quickly and efficiently.',
                'button_text' => 'Explore Solutions',
                'button_link' => '#services',
                'media_type' => 'image',
                'image_path' => null, // Will use placeholder
                'status' => 'active',
                'sort_order' => 2,
                'background_color' => null,
                'text_color' => '#ffffff',
                'text_alignment' => 'left',
                'animation_settings' => json_encode([
                    'title_animation' => 'fadeInLeft',
                    'subtitle_animation' => 'fadeInUp',
                    'description_animation' => 'fadeInDown',
                    'button_animation' => 'fadeInUp',
                    'duration' => 1000,
                    'delay' => 200,
                ]),
            ],
            [
                'title' => 'Engage with Us for 24/7 Support',
                'subtitle' => 'Best 24/7 Support',
                'description' => 'Experience unparalleled customer support with our 24/7 technical assistance. Our dedicated team of experts is always ready to help you resolve issues, provide guidance, and ensure your IT infrastructure runs smoothly around the clock.',
                'button_text' => 'Contact Support',
                'button_link' => '#contact',
                'media_type' => 'image',
                'image_path' => null, // Will use placeholder
                'status' => 'active',
                'sort_order' => 3,
                'background_color' => null,
                'text_color' => '#ffffff',
                'text_alignment' => 'left',
                'animation_settings' => json_encode([
                    'title_animation' => 'fadeInLeft',
                    'subtitle_animation' => 'fadeInUp',
                    'description_animation' => 'fadeInDown',
                    'button_animation' => 'fadeInUp',
                    'duration' => 1000,
                    'delay' => 200,
                ]),
            ],
            [
                'title' => 'Custom Software Development',
                'subtitle' => 'Tailored Solutions',
                'description' => 'Transform your business with custom software solutions designed specifically for your unique requirements. Our expert development team creates scalable, secure, and user-friendly applications that drive efficiency and growth.',
                'button_text' => 'Get Started',
                'button_link' => '#contact',
                'media_type' => 'image',
                'image_path' => null, // Will use placeholder
                'status' => 'inactive',
                'sort_order' => 4,
                'background_color' => '#1a1a1a',
                'text_color' => '#ffffff',
                'text_alignment' => 'center',
                'animation_settings' => json_encode([
                    'title_animation' => 'zoomIn',
                    'subtitle_animation' => 'fadeInDown',
                    'description_animation' => 'fadeInUp',
                    'button_animation' => 'fadeInUp',
                    'duration' => 1200,
                    'delay' => 300,
                ]),
            ],
        ];

        foreach ($sliders as $slider) {
            HomepageSlider::create($slider);
        }

        $this->command->info('Homepage sliders seeded successfully!');
    }
}
