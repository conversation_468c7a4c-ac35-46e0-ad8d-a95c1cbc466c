<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Rename existing columns for consistency
            if (Schema::hasColumn('projects', 'name') && !Schema::hasColumn('projects', 'title')) {
                $table->renameColumn('name', 'title');
            }

            if (!Schema::hasColumn('projects', 'slug')) {
                $table->string('slug')->unique()->nullable()->after('title');
            }
            
            if (!Schema::hasColumn('projects', 'short_description')) {
                $table->text('short_description')->nullable()->after('description');
            }
            
            if (!Schema::hasColumn('projects', 'full_description')) {
                $table->longText('full_description')->nullable()->after('short_description');
            }
            
            if (!Schema::hasColumn('projects', 'category')) {
                $table->string('category')->nullable()->after('full_description');
            }
            
            if (!Schema::hasColumn('projects', 'client_name')) {
                $table->string('client_name')->nullable()->after('category');
            }
            
            if (!Schema::hasColumn('projects', 'project_url')) {
                $table->string('project_url')->nullable()->after('client_name');
            }
            
            if (!Schema::hasColumn('projects', 'technologies')) {
                $table->json('technologies')->nullable()->after('project_url');
            }
            
            if (!Schema::hasColumn('projects', 'start_date')) {
                $table->date('start_date')->nullable()->after('technologies');
            }
            
            if (!Schema::hasColumn('projects', 'end_date')) {
                $table->date('end_date')->nullable()->after('start_date');
            }
            
            if (!Schema::hasColumn('projects', 'status')) {
                $table->enum('status', ['active', 'inactive', 'completed', 'in_progress'])->default('active')->after('end_date');
            }
            
            if (!Schema::hasColumn('projects', 'featured')) {
                $table->boolean('featured')->default(false)->after('status');
            }
            
            if (!Schema::hasColumn('projects', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('featured');
            }
            
            if (!Schema::hasColumn('projects', 'gallery_images')) {
                $table->json('gallery_images')->nullable()->after('sort_order');
            }
            
            if (!Schema::hasColumn('projects', 'meta_title')) {
                $table->string('meta_title')->nullable()->after('gallery_images');
            }
            
            if (!Schema::hasColumn('projects', 'meta_description')) {
                $table->text('meta_description')->nullable()->after('meta_title');
            }
            
            if (!Schema::hasColumn('projects', 'meta_keywords')) {
                $table->text('meta_keywords')->nullable()->after('meta_description');
            }
            
            // Make project_image nullable and rename for consistency
            if (Schema::hasColumn('projects', 'project_image')) {
                $table->string('project_image')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop added columns
            $columnsToCheck = [
                'slug',
                'short_description',
                'full_description',
                'category',
                'client_name',
                'project_url',
                'technologies',
                'start_date',
                'end_date',
                'status',
                'featured',
                'sort_order',
                'gallery_images',
                'meta_title',
                'meta_description',
                'meta_keywords'
            ];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('projects', $column)) {
                    $table->dropColumn($column);
                }
            }

            // Rename title back to name if needed
            if (Schema::hasColumn('projects', 'title') && !Schema::hasColumn('projects', 'name')) {
                $table->renameColumn('title', 'name');
            }
        });
    }
};
