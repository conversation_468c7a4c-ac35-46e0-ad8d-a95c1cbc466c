<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('homepage_sliders', function (Blueprint $table) {
            // Add missing columns if they don't exist
            if (!Schema::hasColumn('homepage_sliders', 'secondary_description')) {
                $table->text('secondary_description')->nullable()->after('description');
            }

            if (!Schema::hasColumn('homepage_sliders', 'secondary_image_path')) {
                $table->string('secondary_image_path')->nullable()->after('video_path');
            }

            // Note: button_text and button_link already exist in the original table creation
            // but let's ensure they exist
            if (!Schema::hasColumn('homepage_sliders', 'button_text')) {
                $table->string('button_text')->nullable()->after('secondary_description');
            }

            if (!Schema::hasColumn('homepage_sliders', 'button_link')) {
                $table->string('button_link')->nullable()->after('button_text');
            }

            // Note: background_color, text_color, text_alignment already exist in original table
            // but let's ensure they have correct defaults
            if (!Schema::hasColumn('homepage_sliders', 'background_color')) {
                $table->string('background_color')->nullable()->after('sort_order');
            }

            if (!Schema::hasColumn('homepage_sliders', 'text_color')) {
                $table->string('text_color')->default('#ffffff')->after('background_color');
            }

            if (!Schema::hasColumn('homepage_sliders', 'text_alignment')) {
                $table->enum('text_alignment', ['left', 'center', 'right'])->default('center')->after('text_color');
            }

            if (!Schema::hasColumn('homepage_sliders', 'animation_settings')) {
                $table->json('animation_settings')->nullable()->after('text_alignment');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('homepage_sliders', function (Blueprint $table) {
            $columnsToRemove = [
                'secondary_description',
                'secondary_image_path', 
                'button_text',
                'button_link',
                'background_color',
                'text_color',
                'text_alignment',
                'animation_settings'
            ];
            
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('homepage_sliders', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
