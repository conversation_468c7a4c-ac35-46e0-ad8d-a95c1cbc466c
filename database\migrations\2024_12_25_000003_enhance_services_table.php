<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Fix existing column issues and make service_image nullable
            if (Schema::hasColumn('services', 'service_image')) {
                $table->string('service_image')->nullable()->change();
            }
            
            // Rename Description to description for consistency
            if (Schema::hasColumn('services', 'Description')) {
                $table->renameColumn('Description', 'description');
            }
            
            // Add new comprehensive fields
            if (!Schema::hasColumn('services', 'short_description')) {
                $table->text('short_description')->nullable()->after('description');
            }
            
            if (!Schema::hasColumn('services', 'icon_class')) {
                $table->string('icon_class')->nullable()->after('short_description');
            }
            
            if (!Schema::hasColumn('services', 'button_text')) {
                $table->string('button_text')->default('Read More')->after('icon_class');
            }
            
            if (!Schema::hasColumn('services', 'button_link')) {
                $table->string('button_link')->default('#')->after('button_text');
            }
            
            if (!Schema::hasColumn('services', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('button_link');
            }
            
            if (!Schema::hasColumn('services', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('status');
            }

            if (!Schema::hasColumn('services', 'slug')) {
                $table->string('slug')->unique()->after('name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Rename back to original column name
            if (Schema::hasColumn('services', 'description')) {
                $table->renameColumn('description', 'Description');
            }
            
            $columnsToRemove = [
                'short_description',
                'icon_class',
                'slug',
                'button_text',
                'button_link',
                'status',
                'sort_order'
            ];
            
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('services', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
