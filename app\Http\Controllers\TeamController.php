<?php

namespace App\Http\Controllers;

use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class TeamController extends Controller
{
    /**
     * Display a listing of team members
     */
    public function index()
    {
        try {
            $teams = Team::ordered()->get();
            return view('backend.pages.teams.index', compact('teams'));
        } catch (\Exception $e) {
            Log::error('Error loading team members: ' . $e->getMessage());
            return view('backend.pages.teams.index', ['teams' => collect()]);
        }
    }

    /**
     * Show the form for creating a new team member
     */
    public function create()
    {
        return view('backend.pages.teams.create');
    }

    /**
     * Store a newly created team member
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'facebook' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'linkedin' => 'nullable|url|max:255',
            'team_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'name', 'designation', 'bio', 'email', 'phone',
            'facebook', 'twitter', 'instagram', 'linkedin', 'status'
        ]);

        // Handle team image upload
        if ($request->hasFile('team_image')) {
            $data['team_image'] = $request->file('team_image')->store('teams/images', 'public');
        }

        try {
            Team::create($data);
            return redirect()->route('teams.index')
                ->with('success', 'Team member created successfully!');
        } catch (\Exception $e) {
            Log::error('Error creating team member', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create team member: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified team member
     */
    public function show(Team $team)
    {
        return view('backend.pages.teams.show', compact('team'));
    }

    /**
     * Show the form for editing the specified team member
     */
    public function edit(Team $team)
    {
        return view('backend.pages.teams.edit', compact('team'));
    }

    /**
     * Update the specified team member
     */
    public function update(Request $request, Team $team)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'facebook' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'linkedin' => 'nullable|url|max:255',
            'team_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'name', 'designation', 'bio', 'email', 'phone',
            'facebook', 'twitter', 'instagram', 'linkedin', 'status'
        ]);

        // Handle team image upload
        if ($request->hasFile('team_image')) {
            // Delete old image
            if ($team->team_image && Storage::disk('public')->exists($team->team_image)) {
                Storage::disk('public')->delete($team->team_image);
            }
            $data['team_image'] = $request->file('team_image')->store('teams/images', 'public');
        }

        try {
            $team->update($data);
            return redirect()->route('teams.index')
                ->with('success', 'Team member updated successfully!');
        } catch (\Exception $e) {
            Log::error('Error updating team member', [
                'error' => $e->getMessage(),
                'team_id' => $team->id,
                'data' => $data
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to update team member: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified team member
     */
    public function destroy(Team $team)
    {
        try {
            $team->delete();
            return redirect()->route('teams.index')
                ->with('success', 'Team member deleted successfully!');
        } catch (\Exception $e) {
            Log::error('Error deleting team member: ' . $e->getMessage());
            return redirect()->route('teams.index')
                ->with('error', 'Failed to delete team member');
        }
    }

    /**
     * Toggle team member status
     */
    public function toggleStatus(Team $team)
    {
        try {
            $newStatus = $team->status === 'active' ? 'inactive' : 'active';
            $team->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'status' => $newStatus,
                'message' => 'Team member status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling team member status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status'
            ], 500);
        }
    }

    /**
     * Reorder team members
     */
    public function reorder(Request $request)
    {
        try {
            $orders = $request->input('order', []);
            
            foreach ($orders as $index => $id) {
                Team::where('id', $id)->update(['sort_order' => $index + 1]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Team members reordered successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error reordering team members: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder team members'
            ], 500);
        }
    }

    /**
     * Duplicate a team member
     */
    public function duplicate(Team $team)
    {
        try {
            $newTeam = $team->replicate();
            $newTeam->name = $team->name . ' (Copy)';
            $newTeam->status = 'inactive'; // Set copy as inactive by default
            $newTeam->sort_order = Team::max('sort_order') + 1;
            $newTeam->save();

            return redirect()->route('teams.index')
                ->with('success', 'Team member duplicated successfully!');
        } catch (\Exception $e) {
            Log::error('Error duplicating team member: ' . $e->getMessage());
            return redirect()->route('teams.index')
                ->with('error', 'Failed to duplicate team member');
        }
    }

    /**
     * Get team data for homepage
     */
    public function getHomepageData()
    {
        $teams = Team::active()->ordered()->take(8)->get();
        
        if ($teams->isEmpty()) {
            return null;
        }

        return $teams->map(function ($team) {
            return [
                'id' => $team->id,
                'name' => $team->name,
                'designation' => $team->designation,
                'bio' => $team->bio,
                'email' => $team->email,
                'phone' => $team->phone,
                'facebook' => $team->facebook,
                'twitter' => $team->twitter,
                'instagram' => $team->instagram,
                'linkedin' => $team->linkedin,
                'team_image_url' => $team->team_image_url,
                'status' => $team->status,
            ];
        });
    }
}
