@extends('backend.layouts.app')

@section('title', 'Create New System')

@section('content')
<!-- Page Title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Create New System</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('systems.index') }}">Systems</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<form action="{{ route('systems.store') }}" method="POST" enctype="multipart/form-data" id="systemForm">
    @csrf
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-information-line me-2"></i>Basic Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">System Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">URL Slug</label>
                            <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                   id="slug" name="slug" value="{{ old('slug') }}">
                            <div class="form-text">Leave empty to auto-generate from title</div>
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                   id="category" name="category" value="{{ old('category') }}" 
                                   placeholder="e.g., Web Application, Mobile App, API">
                            @error('category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="version" class="form-label">Version</label>
                            <input type="text" class="form-control @error('version') is-invalid @enderror" 
                                   id="version" name="version" value="{{ old('version') }}" 
                                   placeholder="e.g., 1.0.0">
                            @error('version')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                        <div class="form-text">Brief description for cards and listings (max 1000 characters)</div>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="short_description" class="form-label">Short Description</label>
                        <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                  id="short_description" name="short_description" rows="2">{{ old('short_description') }}</textarea>
                        <div class="form-text">Very brief description for previews (max 500 characters)</div>
                        @error('short_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="full_description" class="form-label">Full Description</label>
                        <textarea class="form-control @error('full_description') is-invalid @enderror" 
                                  id="full_description" name="full_description" rows="6">{{ old('full_description') }}</textarea>
                        <div class="form-text">Detailed description for the system detail page</div>
                        @error('full_description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Technologies & Features -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-code-line me-2"></i>Technologies & Features
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="technologies" class="form-label">Technologies Used</label>
                        <input type="text" class="form-control" id="technologies-input" 
                               placeholder="Type technology and press Enter">
                        <div id="technologies-container" class="mt-2"></div>
                        <div class="form-text">Add technologies one by one by typing and pressing Enter</div>
                    </div>

                    <div class="mb-3">
                        <label for="features" class="form-label">Key Features</label>
                        <input type="text" class="form-control" id="features-input" 
                               placeholder="Type feature and press Enter">
                        <div id="features-container" class="mt-2"></div>
                        <div class="form-text">Add key features one by one by typing and pressing Enter</div>
                    </div>
                </div>
            </div>

            <!-- URLs & Links -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-links-line me-2"></i>URLs & Links
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="system_url" class="form-label">Live Demo URL</label>
                            <input type="url" class="form-control @error('system_url') is-invalid @enderror" 
                                   id="system_url" name="system_url" value="{{ old('system_url') }}" 
                                   placeholder="https://example.com">
                            @error('system_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="github_url" class="form-label">GitHub Repository</label>
                            <input type="url" class="form-control @error('github_url') is-invalid @enderror" 
                                   id="github_url" name="github_url" value="{{ old('github_url') }}" 
                                   placeholder="https://github.com/username/repo">
                            @error('github_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="documentation_url" class="form-label">Documentation URL</label>
                        <input type="url" class="form-control @error('documentation_url') is-invalid @enderror" 
                               id="documentation_url" name="documentation_url" value="{{ old('documentation_url') }}" 
                               placeholder="https://docs.example.com">
                        @error('documentation_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Status & Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-settings-3-line me-2"></i>Status & Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                            <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="development" {{ old('status') == 'development' ? 'selected' : '' }}>Development</option>
                            <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="launch_date" class="form-label">Launch Date</label>
                        <input type="date" class="form-control @error('launch_date') is-invalid @enderror" 
                               id="launch_date" name="launch_date" value="{{ old('launch_date') }}">
                        @error('launch_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                               {{ old('featured') ? 'checked' : '' }}>
                        <label class="form-check-label" for="featured">
                            Featured System
                        </label>
                        <div class="form-text">Featured systems appear on the homepage</div>
                    </div>
                </div>
            </div>

            <!-- Images -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-image-line me-2"></i>Images
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="system_image" class="form-label">Main System Image</label>
                        <input type="file" class="form-control @error('system_image') is-invalid @enderror" 
                               id="system_image" name="system_image" accept="image/*">
                        <div class="form-text">Recommended size: 800x600px</div>
                        @error('system_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div id="main-image-preview" class="mt-2"></div>
                    </div>

                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">Gallery Images</label>
                        <input type="file" class="form-control @error('gallery_images.*') is-invalid @enderror" 
                               id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                        <div class="form-text">You can select multiple images</div>
                        @error('gallery_images.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div id="gallery-preview" class="mt-2"></div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Create System
                        </button>
                        <a href="{{ route('systems.index') }}" class="btn btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Systems
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from title
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');

    titleInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.value === generateSlug(titleInput.dataset.oldValue || '')) {
            slugInput.value = generateSlug(this.value);
        }
        titleInput.dataset.oldValue = this.value;
    });

    function generateSlug(text) {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }

    // Technologies management
    const technologiesInput = document.getElementById('technologies-input');
    const technologiesContainer = document.getElementById('technologies-container');
    let technologies = [];

    technologiesInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const value = this.value.trim();
            if (value && !technologies.includes(value)) {
                technologies.push(value);
                updateTechnologiesDisplay();
                this.value = '';
            }
        }
    });

    function updateTechnologiesDisplay() {
        technologiesContainer.innerHTML = '';
        technologies.forEach((tech, index) => {
            const badge = document.createElement('span');
            badge.className = 'badge bg-primary me-2 mb-2';
            badge.innerHTML = `
                ${tech}
                <button type="button" class="btn-close btn-close-white ms-1"
                        onclick="removeTechnology(${index})" style="font-size: 0.7em;"></button>
            `;
            technologiesContainer.appendChild(badge);
        });

        // Update hidden input
        updateHiddenInput('technologies', technologies);
    }

    window.removeTechnology = function(index) {
        technologies.splice(index, 1);
        updateTechnologiesDisplay();
    };

    // Features management
    const featuresInput = document.getElementById('features-input');
    const featuresContainer = document.getElementById('features-container');
    let features = [];

    featuresInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const value = this.value.trim();
            if (value && !features.includes(value)) {
                features.push(value);
                updateFeaturesDisplay();
                this.value = '';
            }
        }
    });

    function updateFeaturesDisplay() {
        featuresContainer.innerHTML = '';
        features.forEach((feature, index) => {
            const badge = document.createElement('span');
            badge.className = 'badge bg-success me-2 mb-2';
            badge.innerHTML = `
                ${feature}
                <button type="button" class="btn-close btn-close-white ms-1"
                        onclick="removeFeature(${index})" style="font-size: 0.7em;"></button>
            `;
            featuresContainer.appendChild(badge);
        });

        // Update hidden input
        updateHiddenInput('features', features);
    }

    window.removeFeature = function(index) {
        features.splice(index, 1);
        updateFeaturesDisplay();
    };

    function updateHiddenInput(name, array) {
        // Remove existing hidden inputs
        document.querySelectorAll(`input[name="${name}[]"]`).forEach(input => input.remove());

        // Add new hidden inputs
        array.forEach(item => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `${name}[]`;
            input.value = item;
            document.getElementById('systemForm').appendChild(input);
        });
    }

    // Image preview functionality
    const mainImageInput = document.getElementById('system_image');
    const mainImagePreview = document.getElementById('main-image-preview');

    mainImageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                mainImagePreview.innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                `;
            };
            reader.readAsDataURL(file);
        } else {
            mainImagePreview.innerHTML = '';
        }
    });

    // Gallery images preview
    const galleryInput = document.getElementById('gallery_images');
    const galleryPreview = document.getElementById('gallery-preview');

    galleryInput.addEventListener('change', function() {
        galleryPreview.innerHTML = '';
        Array.from(this.files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'd-inline-block me-2 mb-2';
                div.innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 100px; max-height: 75px;">
                `;
                galleryPreview.appendChild(div);
            };
            reader.readAsDataURL(file);
        });
    });

    // Form validation
    document.getElementById('systemForm').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();

        if (!title) {
            e.preventDefault();
            alert('Please enter a system title.');
            document.getElementById('title').focus();
            return;
        }

        if (!description) {
            e.preventDefault();
            alert('Please enter a system description.');
            document.getElementById('description').focus();
            return;
        }
    });
});
</script>
@endpush
