<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's check if we have data in the old sliders table
        if (Schema::hasTable('sliders')) {
            $oldSliders = DB::table('sliders')->get();
            
            // If we have data in the old table and homepage_sliders exists, migrate the data
            if ($oldSliders->count() > 0 && Schema::hasTable('homepage_sliders')) {
                foreach ($oldSliders as $slider) {
                    // Check if this slider doesn't already exist in homepage_sliders
                    $exists = DB::table('homepage_sliders')
                        ->where('title', $slider->first_slogan_one)
                        ->exists();
                    
                    if (!$exists) {
                        // Prepare data array with only columns that exist
                        $insertData = [
                            'title' => $slider->first_slogan_one ?? 'Untitled Slider',
                            'subtitle' => $slider->second_slogan_one ?? '',
                            'description' => $slider->first_slogan_two ?? '',
                            'image_path' => $slider->slider_one_img ?? null,
                            'media_type' => 'image',
                            'status' => $slider->status ?? 'active',
                            'sort_order' => $slider->sort_order ?? 0,
                            'text_color' => '#ffffff',
                            'text_alignment' => 'center',
                            'created_at' => $slider->created_at ?? now(),
                            'updated_at' => $slider->updated_at ?? now(),
                        ];
                        
                        // Add optional columns only if they exist in the table
                        if (Schema::hasColumn('homepage_sliders', 'secondary_description')) {
                            $insertData['secondary_description'] = $slider->second_slogan_two ?? '';
                        }
                        
                        if (Schema::hasColumn('homepage_sliders', 'secondary_image_path')) {
                            $insertData['secondary_image_path'] = $slider->slogan_two_img ?? null;
                        }
                        
                        if (Schema::hasColumn('homepage_sliders', 'background_color')) {
                            $insertData['background_color'] = '#000000';
                        }
                        
                        DB::table('homepage_sliders')->insert($insertData);
                    }
                }
                
                echo "Migrated " . $oldSliders->count() . " sliders to homepage_sliders table.\n";
            }
            
            // Now truncate the old sliders table
            DB::table('sliders')->truncate();
            echo "Cleaned up old sliders table.\n";
        }
        
        // Clean up any other old slider-related tables if they exist
        $oldTables = ['slider_images', 'slider_settings', 'old_sliders'];
        foreach ($oldTables as $table) {
            if (Schema::hasTable($table)) {
                Schema::dropIfExists($table);
                echo "Dropped old table: {$table}\n";
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not reversible as it involves data cleanup
        // If you need to restore data, use your database backup
        echo "This migration cannot be reversed. Please restore from backup if needed.\n";
    }
};
