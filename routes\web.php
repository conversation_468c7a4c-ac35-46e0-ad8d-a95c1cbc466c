<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\BackendController;
use App\Http\Controllers\FrontendController;
use Illuminate\Support\Facades\Route;

// Frontend Routes
Route::get('/', [FrontendController::class, 'index'])->name('home');
Route::get('/about', [App\Http\Controllers\AboutController::class, 'index'])->name('about');

// Kenya-focused SEO pages
Route::get('/software-development-kenya', function () {
    return view('frontend.pages.software-development-kenya');
})->name('software-development-kenya');

Route::get('/web-development-nairobi', function () {
    return view('frontend.pages.web-development-nairobi');
})->name('web-development-nairobi');

Route::get('/mobile-app-development-kenya', function () {
    return view('frontend.pages.mobile-app-development-kenya');
})->name('mobile-app-development-kenya');

Route::get('/contact-kenya', function () {
    return view('frontend.pages.contact-kenya');
})->name('contact-kenya');

// Blog Routes
Route::get('/blog', [App\Http\Controllers\BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/search', [App\Http\Controllers\BlogController::class, 'search'])->name('blog.search');
Route::get('/blog/category/{category}', [App\Http\Controllers\BlogController::class, 'category'])->name('blog.category');
Route::get('/blog/tag/{tag}', [App\Http\Controllers\BlogController::class, 'tag'])->name('blog.tag');
Route::get('/blog/rss', [App\Http\Controllers\BlogController::class, 'rss'])->name('blog.rss');
Route::get('/blog/{post}', [App\Http\Controllers\BlogController::class, 'show'])->name('blog.show');

// Admin Routes (SEO Management)
Route::prefix('admin')->name('admin.')->middleware(['auth'])->group(function () {
    // SEO Management
    Route::prefix('seo')->name('seo.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SeoController::class, 'dashboard'])->name('dashboard');
        Route::get('/settings', [App\Http\Controllers\Admin\SeoController::class, 'settings'])->name('settings');
        Route::post('/settings', [App\Http\Controllers\Admin\SeoController::class, 'updateSettings'])->name('settings.update');
        Route::post('/settings/create', [App\Http\Controllers\Admin\SeoController::class, 'createSetting'])->name('settings.create');

        Route::get('/pages', [App\Http\Controllers\Admin\SeoController::class, 'pages'])->name('pages');
        Route::get('/pages/create', [App\Http\Controllers\Admin\SeoController::class, 'showPage'])->name('pages.create');
        Route::post('/pages', [App\Http\Controllers\Admin\SeoController::class, 'storePage'])->name('pages.store');
        Route::get('/pages/{id}/edit', [App\Http\Controllers\Admin\SeoController::class, 'showPage'])->name('pages.edit');
        Route::put('/pages/{id}', [App\Http\Controllers\Admin\SeoController::class, 'storePage'])->name('pages.update');
        Route::delete('/pages/{id}', [App\Http\Controllers\Admin\SeoController::class, 'deletePage'])->name('pages.delete');
        Route::post('/pages/bulk', [App\Http\Controllers\Admin\SeoController::class, 'bulkPages'])->name('pages.bulk');

        Route::get('/analysis', [App\Http\Controllers\Admin\SeoController::class, 'analysis'])->name('analysis');
        Route::post('/sitemap/generate', [App\Http\Controllers\Admin\SeoController::class, 'generateSitemap'])->name('sitemap.generate');
    });

    // Blog Management
    Route::prefix('blog')->name('blog.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\BlogController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\BlogController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\BlogController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [App\Http\Controllers\Admin\BlogController::class, 'edit'])->name('edit');
        Route::put('/{id}', [App\Http\Controllers\Admin\BlogController::class, 'update'])->name('update');
        Route::delete('/{id}', [App\Http\Controllers\Admin\BlogController::class, 'destroy'])->name('destroy');
        Route::post('/bulk', [App\Http\Controllers\Admin\BlogController::class, 'bulkAction'])->name('bulk');

        Route::get('/{id}/seo', [App\Http\Controllers\Admin\BlogController::class, 'seo'])->name('seo');
        Route::post('/{id}/seo', [App\Http\Controllers\Admin\BlogController::class, 'updateSeo'])->name('seo.update');
    });
});

// SEO Routes
Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [App\Http\Controllers\SitemapController::class, 'robots'])->name('robots');

// API Routes for frontend (using homepage_sliders table)
Route::get('/api/sliders', [App\Http\Controllers\SliderController::class, 'api'])->name('api.sliders');

// Frontend Project Routes
Route::get('/projects', [App\Http\Controllers\ProjectController::class, 'allProjects'])->name('projects.all');
Route::get('/project/{project:slug}', [App\Http\Controllers\ProjectController::class, 'showProject'])->name('project.detail');

// Frontend System Routes
Route::get('/systems', [App\Http\Controllers\SystemController::class, 'allSystems'])->name('systems.all');
Route::get('/system/{system:slug}', [App\Http\Controllers\SystemController::class, 'showFrontend'])->name('system.detail');
Route::post('/system/{system:slug}/rate', [App\Http\Controllers\SystemController::class, 'storeRating'])->name('system.rate');

// Frontend Service Routes
Route::get('/services', [App\Http\Controllers\ServicesController::class, 'allServices'])->name('services.all');
Route::get('/service/{service:slug}', [App\Http\Controllers\ServicesController::class, 'showService'])->name('service.detail');

// Debug route for testing slider database connection
Route::get('/debug/sliders', function() {
    try {
        // Check original sliders table
        $originalSliders = \App\Models\Sliders::all();
        $activeOriginalSliders = \App\Models\Sliders::active()->ordered()->get();

        // Check homepage sliders table
        $homepageSliders = \App\Models\HomepageSlider::all();
        $activeHomepageSliders = \App\Models\HomepageSlider::active()->ordered()->get();

        return response()->json([
            'database_connection' => 'OK',
            'original_sliders' => [
                'table' => 'sliders',
                'total' => $originalSliders->count(),
                'active' => $activeOriginalSliders->count(),
                'data' => $originalSliders->map(function($slider) {
                    return [
                        'id' => $slider->id,
                        'first_slogan_one' => $slider->first_slogan_one,
                        'second_slogan_one' => $slider->second_slogan_one,
                        'status' => $slider->status ?? 'active',
                        'sort_order' => $slider->sort_order ?? 0,
                        'has_slider_one_img' => !empty($slider->slider_one_img),
                        'has_slogan_two_img' => !empty($slider->slogan_two_img),
                    ];
                })
            ],
            'homepage_sliders' => [
                'table' => 'homepage_sliders',
                'total' => $homepageSliders->count(),
                'active' => $activeHomepageSliders->count(),
                'data' => $homepageSliders->map(function($slider) {
                    return [
                        'id' => $slider->id,
                        'title' => $slider->title,
                        'status' => $slider->status,
                        'sort_order' => $slider->sort_order,
                        'media_type' => $slider->media_type,
                        'has_image' => !empty($slider->image_path),
                        'has_video' => !empty($slider->video_path),
                    ];
                })
            ],
            'current_api_using' => 'homepage_sliders'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'database_connection' => 'ERROR',
            'error' => $e->getMessage()
        ], 500);
    }
})->name('debug.sliders');

Route::get('/dashboard', function () {
    return view('backend.pages.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/admin', [BackendController::class, 'adminProfile'])->name('profile.view');
    Route::get('/sitesetting', [BackendController::class, 'siteSetting'])->name('site.setting');
    Route::get('/logout', [BackendController::class, 'destroy'])->name('admin.logout');

    // Main Slider Management Routes (using homepage_sliders table)
    Route::prefix('admin')->group(function () {
        Route::resource('sliders', App\Http\Controllers\SliderController::class);
        Route::post('sliders/{slider}/toggle-status', [App\Http\Controllers\SliderController::class, 'toggleStatus'])->name('sliders.toggle-status');
        Route::post('sliders/reorder', [App\Http\Controllers\SliderController::class, 'reorder'])->name('sliders.reorder');
        Route::post('sliders/{slider}/duplicate', [App\Http\Controllers\SliderController::class, 'duplicate'])->name('sliders.duplicate');

        // Project Management Routes
        Route::resource('projects', App\Http\Controllers\ProjectController::class);
        Route::post('projects/{project}/toggle-status', [App\Http\Controllers\ProjectController::class, 'toggleStatus'])->name('projects.toggle-status');
        Route::post('projects/{project}/toggle-featured', [App\Http\Controllers\ProjectController::class, 'toggleFeatured'])->name('projects.toggle-featured');
        Route::post('projects/reorder', [App\Http\Controllers\ProjectController::class, 'reorder'])->name('projects.reorder');
        Route::post('projects/{project}/duplicate', [App\Http\Controllers\ProjectController::class, 'duplicate'])->name('projects.duplicate');
        Route::delete('projects/{project}/gallery/{image_index}', [App\Http\Controllers\ProjectController::class, 'removeGalleryImage'])->name('projects.remove-gallery-image');

        // Team Management Routes
        Route::resource('teams', App\Http\Controllers\TeamController::class);
        Route::post('teams/{team}/toggle-status', [App\Http\Controllers\TeamController::class, 'toggleStatus'])->name('teams.toggle-status');
        Route::post('teams/reorder', [App\Http\Controllers\TeamController::class, 'reorder'])->name('teams.reorder');
        Route::post('teams/{team}/duplicate', [App\Http\Controllers\TeamController::class, 'duplicate'])->name('teams.duplicate');

        // Testimonial Management Routes
        Route::resource('testimonials', App\Http\Controllers\TestimonialController::class);
        Route::post('testimonials/{testimonial}/toggle-status', [App\Http\Controllers\TestimonialController::class, 'toggleStatus'])->name('testimonials.toggle-status');
        Route::post('testimonials/{testimonial}/toggle-featured', [App\Http\Controllers\TestimonialController::class, 'toggleFeatured'])->name('testimonials.toggle-featured');
        Route::post('testimonials/reorder', [App\Http\Controllers\TestimonialController::class, 'reorder'])->name('testimonials.reorder');
        Route::post('testimonials/{testimonial}/duplicate', [App\Http\Controllers\TestimonialController::class, 'duplicate'])->name('testimonials.duplicate');

        // About Section Management Routes
        Route::get('about', [App\Http\Controllers\AboutController::class, 'adminIndex'])->name('admin.about.index');
        Route::get('about/create', [App\Http\Controllers\AboutController::class, 'create'])->name('admin.about.create');
        Route::post('about', [App\Http\Controllers\AboutController::class, 'store'])->name('admin.about.store');
        Route::get('about/{about}/edit', [App\Http\Controllers\AboutController::class, 'edit'])->name('admin.about.edit');
        Route::put('about/{about}', [App\Http\Controllers\AboutController::class, 'update'])->name('admin.about.update');
        Route::delete('about/{about}', [App\Http\Controllers\AboutController::class, 'destroy'])->name('admin.about.destroy');
        Route::post('about/{about}/toggle-status', [App\Http\Controllers\AboutController::class, 'toggleStatus'])->name('admin.about.toggle-status');

        // Services Management Routes
        Route::get('services', [App\Http\Controllers\ServicesController::class, 'index'])->name('admin.services.index');
        Route::get('services/create', [App\Http\Controllers\ServicesController::class, 'create'])->name('admin.services.create');
        Route::post('services', [App\Http\Controllers\ServicesController::class, 'store'])->name('admin.services.store');
        Route::get('services/{service}/edit', [App\Http\Controllers\ServicesController::class, 'edit'])->name('admin.services.edit');
        Route::put('services/{service}', [App\Http\Controllers\ServicesController::class, 'update'])->name('admin.services.update');
        Route::delete('services/{service}', [App\Http\Controllers\ServicesController::class, 'destroy'])->name('admin.services.destroy');
        Route::post('services/{service}/toggle-status', [App\Http\Controllers\ServicesController::class, 'toggleStatus'])->name('admin.services.toggle-status');

        // Systems Management Routes
        Route::resource('systems', App\Http\Controllers\SystemController::class);
        Route::post('systems/{system}/toggle-featured', [App\Http\Controllers\SystemController::class, 'toggleFeatured'])->name('systems.toggle-featured');
        Route::post('systems/update-sort-order', [App\Http\Controllers\SystemController::class, 'updateSortOrder'])->name('systems.update-sort-order');
        Route::post('systems/{system}/rate', [App\Http\Controllers\SystemController::class, 'storeRating'])->name('systems.rate');

        // Legacy Original Slider Routes (for backward compatibility - will be removed)
        Route::resource('original-sliders', App\Http\Controllers\OriginalSliderController::class);
        Route::post('original-sliders/{slider}/toggle-status', [App\Http\Controllers\OriginalSliderController::class, 'toggleStatus'])->name('original-sliders.toggle-status');
        Route::post('original-sliders/reorder', [App\Http\Controllers\OriginalSliderController::class, 'reorder'])->name('original-sliders.reorder');
        Route::post('original-sliders/{slider}/duplicate', [App\Http\Controllers\OriginalSliderController::class, 'duplicate'])->name('original-sliders.duplicate');
    });

    // Legacy routes (keep for backward compatibility)
    Route::get('/sliders-old', [BackendController::class, 'adminSliders'])->name('sliders.old');
    // Route::get('/services', [BackendController::class, 'adminServices'])->name('services'); // Disabled - conflicts with new frontend services route
    // Route::get('/projects', [BackendController::class, 'adminProjects'])->name('projects'); // Disabled - conflicts with new ProjectController
    Route::get('/testimonials', [BackendController::class, 'adminTestimonials'])->name('testimonials');
    Route::get('/teams', [BackendController::class, 'adminTeams'])->name('teams');
    Route::get('/messages', [BackendController::class, 'adminMessages'])->name('messages');
    Route::get('/allservices', [BackendController::class, 'adminAllServices'])->name('all.service');
    // Route::get('/allprojects', [BackendController::class, 'adminAllProjects'])->name('all.projects'); // Disabled - conflicts with new ProjectController
    Route::get('/alltestimonials', [BackendController::class, 'adminAllTestimonial'])->name('all.testimonials');
    Route::get('/allteams', [BackendController::class, 'adminAllTeams'])->name('all.teams');
    Route::post('/storesitesettings', [BackendController::class, 'storeSettings'])->name('store.sitesettings');
    Route::post('/storecompanyinfo', [BackendController::class, 'storeCompanyinfo'])->name('store.companyinfo');
    Route::post('/storesliders', [BackendController::class, 'storeSliders'])->name('store.sliders');
    Route::post('/storeaboutus', [BackendController::class, 'storeAboutus'])->name('store.aboutus');
    Route::post('/storeservices', [BackendController::class, 'storeServices'])->name('store.services');
    // Route::post('/storeprojects', [BackendController::class, 'storeProjects'])->name('store.projects'); // Disabled - conflicts with new ProjectController
    Route::post('/storeteam', [BackendController::class, 'storeTeam'])->name('store.Team');
});

require __DIR__.'/auth.php';



Route::post('/', [BackendController::class, 'storeMessage'])->name('store.contact');
