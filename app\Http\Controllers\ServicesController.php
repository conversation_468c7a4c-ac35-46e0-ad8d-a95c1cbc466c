<?php

namespace App\Http\Controllers;

use App\Models\Services;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ServicesController extends Controller
{
    /**
     * Display a listing of services for admin
     */
    public function index()
    {
        $services = Services::orderBy('created_at', 'desc')->get();
        return view('backend.pages.services.index', compact('services'));
    }

    /**
     * Show the form for creating new service
     */
    public function create()
    {
        return view('backend.pages.services.create');
    }

    /**
     * Store a newly created service
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'icon_class' => 'nullable|string|max:100',
            'service_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'button_text' => 'nullable|string|max:50',
            'button_link' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('service_image')) {
            $data['service_image'] = $request->file('service_image')->store('services', 'public');
        }

        Services::create($data);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service created successfully!');
    }

    /**
     * Show the form for editing service
     */
    public function edit($id)
    {
        $service = Services::findOrFail($id);
        return view('backend.pages.services.edit', compact('service'));
    }

    /**
     * Update the specified service
     */
    public function update(Request $request, $id)
    {
        $service = Services::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'icon_class' => 'nullable|string|max:100',
            'service_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'button_text' => 'nullable|string|max:50',
            'button_link' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('service_image')) {
            // Delete old image
            if ($service->service_image && Storage::disk('public')->exists($service->service_image)) {
                Storage::disk('public')->delete($service->service_image);
            }
            $data['service_image'] = $request->file('service_image')->store('services', 'public');
        }

        $service->update($data);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service updated successfully!');
    }

    /**
     * Remove the specified service
     */
    public function destroy($id)
    {
        $service = Services::findOrFail($id);
        
        // Delete associated image
        if ($service->service_image && Storage::disk('public')->exists($service->service_image)) {
            Storage::disk('public')->delete($service->service_image);
        }
        
        $service->delete();

        return redirect()->route('admin.services.index')
            ->with('success', 'Service deleted successfully!');
    }

    /**
     * Toggle status of service
     */
    public function toggleStatus($id)
    {
        $service = Services::findOrFail($id);
        $service->status = $service->status === 'active' ? 'inactive' : 'active';
        $service->save();

        return response()->json([
            'success' => true,
            'status' => $service->status,
            'message' => 'Status updated successfully!'
        ]);
    }

    /**
     * Display all services for frontend
     */
    public function allServices()
    {
        $services = Services::active()->ordered()->paginate(12);

        return view('frontend.services.index', compact('services'));
    }

    /**
     * Display a specific service for frontend
     */
    public function showService(Services $service)
    {
        // Check if service is active
        if ($service->status !== 'active') {
            abort(404);
        }

        return view('frontend.services.detail', compact('service'));
    }

    /**
     * Get services data for homepage
     */
    public function getHomepageData()
    {
        $services = Services::active()->ordered()->get();

        if ($services->isEmpty()) {
            // Return null if no services exist - no fallback data
            return null;
        }

        return $services->map(function ($service) {
            return [
                'id' => $service->id,
                'name' => $service->name,
                'slug' => $service->slug,
                'description' => $service->description,
                'short_description' => $service->short_description,
                'icon_class' => $service->icon_class ?? 'fas fa-cogs',
                'service_image_url' => $service->service_image_url,
                'button_text' => $service->button_text ?? 'Read More',
                'button_link' => $service->button_link ?? '#',
            ];
        });
    }
}
