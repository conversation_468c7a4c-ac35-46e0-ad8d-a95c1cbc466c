<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Project;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Generate slugs for any projects that don't have them
        $projects = Project::whereNull('slug')->orWhere('slug', '')->get();

        foreach ($projects as $project) {
            $baseSlug = Str::slug($project->title);
            $slug = $baseSlug;
            $counter = 1;

            // Ensure slug is unique
            while (Project::where('slug', $slug)->where('id', '!=', $project->id)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $project->slug = $slug;
            $project->save();
        }

        // Ensure slug column is not nullable (if it exists)
        if (Schema::hasColumn('projects', 'slug')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->string('slug')->nullable(false)->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Make slug nullable again if column exists
        if (Schema::hasColumn('projects', 'slug')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->string('slug')->nullable()->change();
            });
        }
    }
};
