/* ===================================
   SHADER SLIDER STYLES - THEME RESPONSIVE
   =================================== */

/* Theme Variables for Sliders */
:root {
    /* Light Theme Slider Variables - Optimized for White Background */
    --slider-bg-overlay: rgba(0, 0, 0, 0.5);
    --slider-text-primary: #ffffff;
    --slider-text-secondary: #f8f9fa;
    --slider-text-muted: rgba(255, 255, 255, 0.9);
    --slider-button-bg: var(--grand-success, #34c38f);
    --slider-button-text: #ffffff;
    --slider-button-hover-bg: var(--grand-primary, #556ee6);
    --slider-button-secondary-bg: rgba(255, 255, 255, 0.95);
    --slider-button-secondary-text: var(--grand-body-color, #495057);
    --slider-button-secondary-hover-bg: var(--grand-body-color, #495057);
    --slider-button-secondary-hover-text: var(--grand-body-bg, #ffffff);
    --slider-progress-bg: rgba(255, 255, 255, 0.25);
    --slider-progress-fill: linear-gradient(90deg, var(--grand-success, #34c38f), var(--grand-primary, #556ee6));
    --slider-nav-bg: rgba(255, 255, 255, 0.15);
    --slider-nav-hover-bg: rgba(255, 255, 255, 0.25);
    --slider-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    --slider-container-bg: rgba(248, 249, 250, 0.02);
}

/* Dark Theme Slider Variables */
[data-bs-theme="dark"] {
    --slider-bg-overlay: rgba(0, 0, 0, 0.7);
    --slider-text-primary: #ffffff;
    --slider-text-secondary: #e9ecef;
    --slider-text-muted: rgba(255, 255, 255, 0.8);
    --slider-button-bg: var(--grand-success, #26d48c);
    --slider-button-text: #ffffff;
    --slider-button-hover-bg: var(--grand-primary, #556ee6);
    --slider-button-secondary-bg: transparent;
    --slider-button-secondary-text: var(--grand-body-color, #ffffff);
    --slider-button-secondary-hover-bg: var(--grand-body-color, #ffffff);
    --slider-button-secondary-hover-text: var(--grand-body-bg, #000000);
    --slider-progress-bg: rgba(255, 255, 255, 0.15);
    --slider-progress-fill: linear-gradient(90deg, var(--grand-success, #26d48c), var(--grand-primary, #556ee6));
    --slider-nav-bg: rgba(255, 255, 255, 0.05);
    --slider-nav-hover-bg: rgba(255, 255, 255, 0.1);
    --slider-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    --slider-container-bg: rgba(26, 26, 26, 0.05);
}

/* Dark theme specific container styling */
[data-bs-theme="dark"] .shader-slider-container {
    border-color: rgba(255, 255, 255, 0.1);
}

/* Theme-aware overlay adjustments */
[data-bs-theme="light"] .shader-slide-overlay {
    background: var(--slider-bg-overlay);
}

[data-bs-theme="dark"] .shader-slide-overlay {
    background: var(--slider-bg-overlay);
}

/* Enhanced theme integration for better visual consistency */
.shader-slider-container {
    /* Ensure smooth theme transitions */
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.3s ease,
                background-color 0.3s ease,
                box-shadow 0.3s ease;
}

/* Container */
.shader-slider-container {
    position: relative;
    width: calc(100% - 4rem);
    height: 75vh;
    min-height: 500px;
    overflow: hidden;
    /* Add space on all sides */
    margin: 2rem auto 0 auto;
    margin-left: 2rem;
    margin-right: 2rem;
    border-radius: 1rem;
    box-sizing: border-box;
    /* Performance optimizations */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    /* Smooth transitions with theme support */
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--slider-shadow);
    /* Theme-aware background for better integration */
    background: var(--slider-container-bg);
    /* Subtle border for light theme definition */
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.shader-slider {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 1rem;
    overflow: hidden;
    /* Hardware acceleration */
    transform: translateZ(0);
    will-change: transform;
    /* Smooth transitions */
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Slider Wrapper */
.shader-slider-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    /* Smooth transitions */
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Individual Slides */
.shader-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    border-radius: 1rem;
    overflow: hidden;
    /* Enhanced smooth transitions */
    transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: scale(1.05) translateZ(0);
    will-change: transform, opacity;
}

.shader-slide.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateZ(0);
    z-index: 2;
    /* Enhanced smooth active state */
    transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Transition States */
.shader-slide.slide-in-right {
    transform: translateX(100%) scale(0.8) translateZ(0);
    opacity: 0;
}

.shader-slide.slide-in-left {
    transform: translateX(-100%) scale(0.8) translateZ(0);
    opacity: 0;
}

.shader-slide.slide-out-right {
    transform: translateX(100%) scale(1.2) translateZ(0);
    opacity: 0;
}

.shader-slide.slide-out-left {
    transform: translateX(-100%) scale(1.2) translateZ(0);
    opacity: 0;
}

.shader-slide.zoom-in {
    transform: scale(0.5) translateZ(0);
    opacity: 0;
}

.shader-slide.zoom-out {
    transform: scale(1.5) translateZ(0);
    opacity: 0;
}

.shader-slide.fade-in {
    transform: scale(1.05) translateZ(0);
    opacity: 0;
}

.shader-slide.fade-out {
    transform: scale(0.95) translateZ(0);
    opacity: 0;
}

/* Video Background */
.shader-video-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transition: all 1.5s cubic-bezier(0.23, 1, 0.32, 1);
}

/* Add a subtle overlay for enhanced shader effects */
.shader-video-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        ellipse at center,
        transparent 0%,
        rgba(0, 0, 0, 0.05) 50%,
        rgba(0, 0, 0, 0.1) 100%
    );
    z-index: 1;
    opacity: 0;
    transition: opacity 2s ease;
    pointer-events: none;
}

.shader-slide.active .shader-video-bg::before {
    opacity: 1;
    animation: shaderVideoOverlay 12s ease-in-out infinite;
}

@keyframes shaderVideoOverlay {
    0%, 100% {
        opacity: 0.3;
        background: radial-gradient(
            ellipse at center,
            transparent 0%,
            rgba(0, 0, 0, 0.05) 50%,
            rgba(0, 0, 0, 0.1) 100%
        );
    }
    50% {
        opacity: 0.6;
        background: radial-gradient(
            ellipse at 30% 70%,
            transparent 0%,
            rgba(0, 0, 0, 0.08) 40%,
            rgba(0, 0, 0, 0.15) 100%
        );
    }
}

.shader-slide.active .shader-video-bg {
    opacity: 1;
    visibility: visible;
}

.shader-video-bg video,
.shader-video-bg .shader-image,
.shader-video-bg .shader-video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%) scale(1.05) translateZ(0);
    /* Enhanced smooth image transitions */
    transition: all 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, filter;
    object-fit: cover;
    filter: brightness(1.1) contrast(1.1) saturate(1.2);
}

/* Video specific styles */
.shader-video {
    z-index: 1;
}

/* Ensure videos play smoothly */
.shader-slide.active .shader-video {
    transform: translate(-50%, -50%) scale(1) translateZ(0);
}

/* Active slide video and image animations - Multiple layered shader effects */
.shader-slide.active .shader-video-bg video,
.shader-slide.active .shader-video-bg .shader-image {
    animation:
        shaderVideoParallax 25s ease-in-out infinite,
        shaderVideoFloat 10s ease-in-out infinite,
        shaderVideoBreathe 15s ease-in-out infinite,
        shaderVideoFilters 18s ease-in-out infinite,
        shaderVideoColorShift 22s ease-in-out infinite,
        shaderVideoPerspective 30s ease-in-out infinite;
    animation-delay: 0s, 2s, 4s, 1s, 6s, 3s;
}

/* Video animation keyframes - Enhanced Shader Effects */
@keyframes shaderVideoParallax {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1.1) rotate(0deg) translateZ(0);
    }
    12.5% {
        transform: translate(-51%, -49%) scale(1.13) rotate(0.2deg) translateZ(0);
    }
    25% {
        transform: translate(-52%, -50.5%) scale(1.16) rotate(0deg) translateZ(0);
    }
    37.5% {
        transform: translate(-51%, -52%) scale(1.14) rotate(-0.2deg) translateZ(0);
    }
    50% {
        transform: translate(-50%, -53%) scale(1.18) rotate(0deg) translateZ(0);
    }
    62.5% {
        transform: translate(-49%, -52%) scale(1.14) rotate(0.2deg) translateZ(0);
    }
    75% {
        transform: translate(-48%, -50.5%) scale(1.16) rotate(0deg) translateZ(0);
    }
    87.5% {
        transform: translate(-49%, -49%) scale(1.13) rotate(-0.2deg) translateZ(0);
    }
}

/* Additional floating animation for more dynamic movement */
@keyframes shaderVideoFloat {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1.1) translateY(0px) translateZ(0);
    }
    33% {
        transform: translate(-50%, -50%) scale(1.12) translateY(-8px) translateZ(0);
    }
    66% {
        transform: translate(-50%, -50%) scale(1.14) translateY(4px) translateZ(0);
    }
}

/* Breathing effect for video scaling */
@keyframes shaderVideoBreathe {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1.1) translateZ(0);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.18) translateZ(0);
    }
}

/* Advanced shader filter effects */
@keyframes shaderVideoFilters {
    0%, 100% {
        filter: brightness(1.1) contrast(1.1) saturate(1.2) hue-rotate(0deg);
    }
    25% {
        filter: brightness(1.15) contrast(1.15) saturate(1.3) hue-rotate(2deg);
    }
    50% {
        filter: brightness(1.2) contrast(1.2) saturate(1.4) hue-rotate(0deg);
    }
    75% {
        filter: brightness(1.15) contrast(1.15) saturate(1.3) hue-rotate(-2deg);
    }
}

/* Subtle color shift effect */
@keyframes shaderVideoColorShift {
    0%, 100% {
        filter: brightness(1.1) contrast(1.1) saturate(1.2) sepia(0%) hue-rotate(0deg);
    }
    33% {
        filter: brightness(1.12) contrast(1.12) saturate(1.25) sepia(5%) hue-rotate(3deg);
    }
    66% {
        filter: brightness(1.08) contrast(1.08) saturate(1.15) sepia(3%) hue-rotate(-2deg);
    }
}

/* Zoom and perspective effect */
@keyframes shaderVideoPerspective {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1.1) perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0);
    }
    25% {
        transform: translate(-50%, -50%) scale(1.13) perspective(1000px) rotateX(0.5deg) rotateY(0.3deg) translateZ(0);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.16) perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0);
    }
    75% {
        transform: translate(-50%, -50%) scale(1.13) perspective(1000px) rotateX(-0.5deg) rotateY(-0.3deg) translateZ(0);
    }
}

/* Glitch effect for dramatic transitions */
.shader-video-bg video.glitch-effect {
    animation: shaderVideoGlitch 0.3s ease-in-out;
}

@keyframes shaderVideoGlitch {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1.1) translateZ(0);
        filter: brightness(1.1) contrast(1.1) saturate(1.2);
    }
    10% {
        transform: translate(-48%, -50%) scale(1.12) skewX(2deg) translateZ(0);
        filter: brightness(1.3) contrast(1.4) saturate(1.5) hue-rotate(90deg);
    }
    20% {
        transform: translate(-52%, -50%) scale(1.08) skewX(-1deg) translateZ(0);
        filter: brightness(0.8) contrast(1.6) saturate(0.8) hue-rotate(-45deg);
    }
    30% {
        transform: translate(-50%, -48%) scale(1.15) skewY(1deg) translateZ(0);
        filter: brightness(1.5) contrast(0.9) saturate(2) hue-rotate(180deg);
    }
    40% {
        transform: translate(-50%, -52%) scale(1.05) skewY(-2deg) translateZ(0);
        filter: brightness(0.6) contrast(2) saturate(0.5) hue-rotate(270deg);
    }
    50% {
        transform: translate(-49%, -49%) scale(1.2) skewX(3deg) translateZ(0);
        filter: brightness(2) contrast(0.5) saturate(3) hue-rotate(45deg);
    }
    60% {
        transform: translate(-51%, -51%) scale(0.95) skewX(-3deg) translateZ(0);
        filter: brightness(0.4) contrast(2.5) saturate(0.3) hue-rotate(135deg);
    }
    70% {
        transform: translate(-50%, -50%) scale(1.18) skewY(2deg) translateZ(0);
        filter: brightness(1.8) contrast(0.7) saturate(2.5) hue-rotate(315deg);
    }
    80% {
        transform: translate(-50%, -50%) scale(1.02) skewY(-1deg) translateZ(0);
        filter: brightness(0.9) contrast(1.8) saturate(0.7) hue-rotate(225deg);
    }
    90% {
        transform: translate(-50%, -50%) scale(1.14) skewX(1deg) translateZ(0);
        filter: brightness(1.4) contrast(1.2) saturate(1.8) hue-rotate(60deg);
    }
}

/* Transition-specific video effects with enhanced shader animations */
.shader-slide.slide-in-right .shader-video-bg,
.shader-slide.slide-in-left .shader-video-bg {
    opacity: 0;
    transform: translateX(100px) translateZ(0);
}

.shader-slide.slide-in-right .shader-video-bg video,
.shader-slide.slide-in-left .shader-video-bg video {
    transform: translate(-50%, -50%) scale(1.3) rotateY(15deg) translateZ(0);
    filter: brightness(1.3) contrast(1.3) saturate(1.5) blur(2px);
}

.shader-slide.slide-out-right .shader-video-bg,
.shader-slide.slide-out-left .shader-video-bg {
    opacity: 0;
    transform: translateX(-100px) translateZ(0);
}

.shader-slide.slide-out-right .shader-video-bg video,
.shader-slide.slide-out-left .shader-video-bg video {
    transform: translate(-50%, -50%) scale(0.8) rotateY(-15deg) translateZ(0);
    filter: brightness(0.7) contrast(1.5) saturate(0.8) blur(3px);
}

.shader-slide.zoom-in .shader-video-bg {
    opacity: 0;
    transform: scale(0.5) translateZ(0);
}

.shader-slide.zoom-in .shader-video-bg video {
    transform: translate(-50%, -50%) scale(0.5) rotateZ(5deg) translateZ(0);
    filter: brightness(1.5) contrast(0.8) saturate(2) blur(4px);
}

.shader-slide.zoom-out .shader-video-bg {
    opacity: 0;
    transform: scale(1.5) translateZ(0);
}

.shader-slide.zoom-out .shader-video-bg video {
    transform: translate(-50%, -50%) scale(2) rotateZ(-5deg) translateZ(0);
    filter: brightness(0.5) contrast(2) saturate(0.5) blur(1px);
}

.shader-slide.fade-in .shader-video-bg,
.shader-slide.fade-out .shader-video-bg {
    opacity: 0;
}

.shader-slide.fade-in .shader-video-bg video,
.shader-slide.fade-out .shader-video-bg video {
    transform: translate(-50%, -50%) scale(1.1) translateZ(0);
    filter: brightness(1.2) contrast(1.2) saturate(1.3) blur(1px);
}

/* Enhanced transition effects with shader-like distortions */
.shader-slide.slide-in-right .shader-video-bg video {
    animation: shaderSlideInRight 1.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.shader-slide.slide-in-left .shader-video-bg video {
    animation: shaderSlideInLeft 1.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.shader-slide.zoom-in .shader-video-bg video {
    animation: shaderZoomIn 1.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.shader-slide.fade-in .shader-video-bg video {
    animation: shaderFadeIn 1.8s cubic-bezier(0.23, 1, 0.32, 1);
}

@keyframes shaderSlideInRight {
    0% {
        transform: translate(-30%, -50%) scale(1.3) rotateY(15deg) skewX(5deg) translateZ(0);
        filter: brightness(1.3) contrast(1.3) saturate(1.5) blur(2px) hue-rotate(30deg);
    }
    50% {
        transform: translate(-40%, -50%) scale(1.2) rotateY(8deg) skewX(2deg) translateZ(0);
        filter: brightness(1.15) contrast(1.15) saturate(1.3) blur(1px) hue-rotate(15deg);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.1) rotateY(0deg) skewX(0deg) translateZ(0);
        filter: brightness(1.1) contrast(1.1) saturate(1.2) blur(0px) hue-rotate(0deg);
    }
}

@keyframes shaderSlideInLeft {
    0% {
        transform: translate(-70%, -50%) scale(1.3) rotateY(-15deg) skewX(-5deg) translateZ(0);
        filter: brightness(1.3) contrast(1.3) saturate(1.5) blur(2px) hue-rotate(-30deg);
    }
    50% {
        transform: translate(-60%, -50%) scale(1.2) rotateY(-8deg) skewX(-2deg) translateZ(0);
        filter: brightness(1.15) contrast(1.15) saturate(1.3) blur(1px) hue-rotate(-15deg);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.1) rotateY(0deg) skewX(0deg) translateZ(0);
        filter: brightness(1.1) contrast(1.1) saturate(1.2) blur(0px) hue-rotate(0deg);
    }
}

@keyframes shaderZoomIn {
    0% {
        transform: translate(-50%, -50%) scale(0.5) rotateZ(5deg) translateZ(0);
        filter: brightness(1.5) contrast(0.8) saturate(2) blur(4px) hue-rotate(45deg);
    }
    50% {
        transform: translate(-50%, -50%) scale(0.8) rotateZ(2deg) translateZ(0);
        filter: brightness(1.3) contrast(0.9) saturate(1.6) blur(2px) hue-rotate(22deg);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.1) rotateZ(0deg) translateZ(0);
        filter: brightness(1.1) contrast(1.1) saturate(1.2) blur(0px) hue-rotate(0deg);
    }
}

@keyframes shaderFadeIn {
    0% {
        transform: translate(-50%, -50%) scale(1.05) translateZ(0);
        filter: brightness(1.2) contrast(1.2) saturate(1.3) blur(1px) hue-rotate(10deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.08) translateZ(0);
        filter: brightness(1.15) contrast(1.15) saturate(1.25) blur(0.5px) hue-rotate(5deg);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.1) translateZ(0);
        filter: brightness(1.1) contrast(1.1) saturate(1.2) blur(0px) hue-rotate(0deg);
        opacity: 1;
    }
}

/* Overlay */
.shader-slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--slider-bg-overlay);
    z-index: 1;
    transition: all 1.5s cubic-bezier(0.23, 1, 0.32, 1);
    opacity: 1;
    will-change: opacity, transform;
}

/* Enhanced styling for slides without media - Follow page theme completely */
.shader-slide:not(.has-media) {
    background: var(--grand-body-bg, #ffffff) !important;
    color: var(--grand-body-color, #495057) !important;
    /* Remove all overlays and effects for clean theme integration */
}

/* Remove all overlay effects for slides without media */
.shader-slide:not(.has-media) .shader-slide-overlay {
    background: transparent !important;
    opacity: 0 !important;
    display: none;
}

/* Hide video background for slides without media */
.shader-slide:not(.has-media) .shader-video-bg {
    display: none !important;
}

/* Remove shader effects for slides without media */
.shader-slide:not(.has-media)::before,
.shader-slide:not(.has-media)::after {
    display: none !important;
}

/* Text styling for slides without media - use theme colors */
.shader-slide:not(.has-media) .shader-slide-content {
    color: var(--grand-body-color, #495057) !important;
    background: transparent;
}

.shader-slide:not(.has-media) .shader-title {
    color: var(--grand-body-color, #495057) !important;
}

.shader-slide:not(.has-media) .shader-subtitle {
    color: var(--grand-success, #34c38f) !important;
}

.shader-slide:not(.has-media) .shader-description {
    color: var(--grand-body-color, #495057) !important;
    opacity: 0.8;
}

/* Dark theme adjustments for slides without media */
[data-bs-theme="dark"] .shader-slide:not(.has-media) {
    background: var(--grand-body-bg, #000000) !important;
    color: var(--grand-body-color, #ffffff) !important;
}

[data-bs-theme="dark"] .shader-slide:not(.has-media) .shader-title {
    color: var(--grand-body-color, #ffffff) !important;
}

[data-bs-theme="dark"] .shader-slide:not(.has-media) .shader-subtitle {
    color: var(--grand-success, #26d48c) !important;
}

[data-bs-theme="dark"] .shader-slide:not(.has-media) .shader-description {
    color: var(--grand-body-color, #ffffff) !important;
    opacity: 0.8;
}

/* Enhanced button theme integration for slides without media */
.shader-slide:not(.has-media) .shader-btn-primary {
    background: var(--grand-success, #34c38f) !important;
    border-color: var(--grand-success, #34c38f) !important;
    color: #ffffff !important;
}

.shader-slide:not(.has-media) .shader-btn-primary:hover {
    background: var(--grand-primary, #556ee6) !important;
    border-color: var(--grand-primary, #556ee6) !important;
    color: #ffffff !important;
    transform: translateY(-2px) scale(1.05);
}

.shader-slide:not(.has-media) .shader-btn-secondary {
    background: transparent !important;
    border: 2px solid var(--grand-border-color, #eff2f7) !important;
    color: var(--grand-body-color, #495057) !important;
}

.shader-slide:not(.has-media) .shader-btn-secondary:hover {
    background: var(--grand-body-color, #495057) !important;
    border-color: var(--grand-body-color, #495057) !important;
    color: var(--grand-body-bg, #ffffff) !important;
    transform: translateY(-2px) scale(1.05);
}

/* Dark theme button adjustments for slides without media */
[data-bs-theme="dark"] .shader-slide:not(.has-media) .shader-btn-primary {
    background: var(--grand-success, #26d48c) !important;
    border-color: var(--grand-success, #26d48c) !important;
}

[data-bs-theme="dark"] .shader-slide:not(.has-media) .shader-btn-primary:hover {
    background: var(--grand-primary, #556ee6) !important;
    border-color: var(--grand-primary, #556ee6) !important;
}

[data-bs-theme="dark"] .shader-slide:not(.has-media) .shader-btn-secondary {
    background: transparent !important;
    border: 2px solid var(--grand-border-color, #333333) !important;
    color: var(--grand-body-color, #ffffff) !important;
}

[data-bs-theme="dark"] .shader-slide:not(.has-media) .shader-btn-secondary:hover {
    background: var(--grand-body-color, #ffffff) !important;
    border-color: var(--grand-body-color, #ffffff) !important;
    color: var(--grand-body-bg, #000000) !important;
}

/* Overlay animation effects */
.shader-slide.active .shader-slide-overlay {
    animation: shaderOverlayPulse 8s ease-in-out infinite;
}

@keyframes shaderOverlayPulse {
    0%, 100% {
        opacity: 1;
        background: linear-gradient(
            135deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.4) 50%,
            rgba(0, 0, 0, 0.6) 100%
        );
    }
    50% {
        opacity: 0.9;
        background: linear-gradient(
            135deg,
            rgba(0, 0, 0, 0.6) 0%,
            rgba(0, 0, 0, 0.3) 50%,
            rgba(0, 0, 0, 0.5) 100%
        );
    }
}

/* Transition overlay effects */
.shader-slide.slide-in-right .shader-slide-overlay,
.shader-slide.slide-in-left .shader-slide-overlay {
    opacity: 0;
    transform: translateX(50px) translateZ(0);
}

.shader-slide.slide-out-right .shader-slide-overlay,
.shader-slide.slide-out-left .shader-slide-overlay {
    opacity: 0;
    transform: translateX(-50px) translateZ(0);
}

.shader-slide.zoom-in .shader-slide-overlay,
.shader-slide.zoom-out .shader-slide-overlay {
    opacity: 0;
    transform: scale(0.8) translateZ(0);
}

.shader-slide.fade-in .shader-slide-overlay,
.shader-slide.fade-out .shader-slide-overlay {
    opacity: 0;
}

/* Content */
.shader-slide-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 20px;
}

.shader-content-inner {
    max-width: 800px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center; /* Default center alignment */
    text-align: center; /* Ensure text is centered by default */
}

/* Dynamic text alignment based on slider settings - More robust selectors */
.shader-slide[style*="text-align: left"] .shader-content-inner,
.shader-slide[data-text-align="left"] .shader-content-inner {
    align-items: flex-start;
    text-align: left;
}

.shader-slide[style*="text-align: center"] .shader-content-inner,
.shader-slide[data-text-align="center"] .shader-content-inner,
.shader-content-inner {
    align-items: center;
    text-align: center;
}

.shader-slide[style*="text-align: right"] .shader-content-inner,
.shader-slide[data-text-align="right"] .shader-content-inner {
    align-items: flex-end;
    text-align: right;
}

/* Default text alignment - All text centered by default */
.shader-subtitle,
.shader-title,
.shader-description {
    text-align: center !important;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

/* Force center alignment for all slider content by default */
.shader-slide-content {
    text-align: center;
}

.shader-content-inner {
    text-align: center;
}

/* Ensure all text elements are centered unless specifically overridden */
.shader-slide .shader-subtitle,
.shader-slide .shader-title,
.shader-slide .shader-description {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

/* Text alignment specific styles - More robust selectors with higher specificity */
.shader-slide[style*="text-align: left"] .shader-subtitle,
.shader-slide[style*="text-align: left"] .shader-title,
.shader-slide[style*="text-align: left"] .shader-description,
.shader-slide[data-text-align="left"] .shader-subtitle,
.shader-slide[data-text-align="left"] .shader-title,
.shader-slide[data-text-align="left"] .shader-description {
    text-align: left !important;
    margin-left: 0;
    margin-right: auto;
}

.shader-slide[style*="text-align: center"] .shader-subtitle,
.shader-slide[style*="text-align: center"] .shader-title,
.shader-slide[style*="text-align: center"] .shader-description,
.shader-slide[data-text-align="center"] .shader-subtitle,
.shader-slide[data-text-align="center"] .shader-title,
.shader-slide[data-text-align="center"] .shader-description {
    text-align: center !important;
    margin-left: auto;
    margin-right: auto;
}

.shader-slide[style*="text-align: right"] .shader-subtitle,
.shader-slide[style*="text-align: right"] .shader-title,
.shader-slide[style*="text-align: right"] .shader-description,
.shader-slide[data-text-align="right"] .shader-subtitle,
.shader-slide[data-text-align="right"] .shader-title,
.shader-slide[data-text-align="right"] .shader-description {
    text-align: right !important;
    margin-left: auto;
    margin-right: 0;
}

/* Button alignment - Default center, with overrides */
.shader-buttons {
    justify-content: center;
    width: 100%;
}

.shader-slide[style*="text-align: left"] .shader-buttons,
.shader-slide[data-text-align="left"] .shader-buttons {
    justify-content: flex-start;
}

.shader-slide[style*="text-align: center"] .shader-buttons,
.shader-slide[data-text-align="center"] .shader-buttons {
    justify-content: center;
}

.shader-slide[style*="text-align: right"] .shader-buttons,
.shader-slide[data-text-align="right"] .shader-buttons {
    justify-content: flex-end;
}

/* Typography */
.shader-subtitle {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--grand-success, #34c38f);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(50px) scale(0.8) translateZ(0);
    transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1);
    will-change: transform, opacity;
    animation-delay: 0.2s;
    width: 100%;
    text-align: center; /* Ensure centered by default */
}

/* Dark theme subtitle color */
[data-bs-theme="dark"] .shader-subtitle {
    color: #26d48c;
}

.shader-slide.active .shader-subtitle {
    opacity: 1;
    transform: translateY(0) scale(1) translateZ(0);
    animation: shaderTextReveal 1.5s cubic-bezier(0.23, 1, 0.32, 1) 0.2s both;
}

.shader-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--slider-text-primary);
    line-height: 1.2;
    margin-bottom: 30px;
    opacity: 0;
    transform: translateY(80px) scale(0.9) translateZ(0);
    transition: all 1.5s cubic-bezier(0.23, 1, 0.32, 1);
    will-change: transform, opacity;
    animation-delay: 0.4s;
    width: 100%;
    text-align: center; /* Ensure centered by default */
}

.shader-slide.active .shader-title {
    opacity: 1;
    transform: translateY(0) scale(1) translateZ(0);
    animation: shaderTitleReveal 1.8s cubic-bezier(0.23, 1, 0.32, 1) 0.4s both;
}

.shader-description {
    font-size: 1.2rem;
    color: var(--slider-text-muted);
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0;
    transform: translateY(60px) scale(0.95) translateZ(0);
    transition: all 1.3s cubic-bezier(0.23, 1, 0.32, 1);
    will-change: transform, opacity;
    animation-delay: 0.6s;
    width: 100%;
    max-width: 600px;
    text-align: center; /* Ensure centered by default */
    margin-left: auto;
    margin-right: auto;
}

.shader-slide.active .shader-description {
    opacity: 1;
    transform: translateY(0) scale(1) translateZ(0);
    animation: shaderTextReveal 1.6s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
}

/* Content Animation Keyframes */
@keyframes shaderTextReveal {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.9) translateZ(0);
        filter: blur(10px);
    }
    50% {
        opacity: 0.7;
        transform: translateY(10px) scale(0.98) translateZ(0);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) translateZ(0);
        filter: blur(0);
    }
}

@keyframes shaderTitleReveal {
    0% {
        opacity: 0;
        transform: translateY(80px) scale(0.8) rotateX(20deg) translateZ(0);
        filter: blur(15px);
    }
    30% {
        opacity: 0.3;
        transform: translateY(40px) scale(0.9) rotateX(10deg) translateZ(0);
        filter: blur(8px);
    }
    70% {
        opacity: 0.8;
        transform: translateY(10px) scale(0.98) rotateX(2deg) translateZ(0);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg) translateZ(0);
        filter: blur(0);
    }
}

/* Buttons */
.shader-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    opacity: 0;
    transform: translateY(60px) scale(0.8) translateZ(0);
    transition: all 1.4s cubic-bezier(0.23, 1, 0.32, 1);
    will-change: transform, opacity;
    animation-delay: 0.8s;
    width: 100%;
    text-align: center;
}

.shader-slide.active .shader-buttons {
    opacity: 1;
    transform: translateY(0) scale(1) translateZ(0);
    animation: shaderButtonsReveal 1.7s cubic-bezier(0.23, 1, 0.32, 1) 0.8s both;
}

/* Button reveal animation */
@keyframes shaderButtonsReveal {
    0% {
        opacity: 0;
        transform: translateY(60px) scale(0.7) translateZ(0);
        filter: blur(8px);
    }
    40% {
        opacity: 0.6;
        transform: translateY(20px) scale(0.9) translateZ(0);
        filter: blur(3px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) translateZ(0);
        filter: blur(0);
    }
}

.shader-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    z-index: 1;
    transform: translateZ(0);
    will-change: transform;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.shader-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: -1;
}

.shader-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: -1;
}

.shader-btn:hover::before {
    left: 100%;
}

.shader-btn:hover::after {
    width: 300px;
    height: 300px;
}

.shader-btn:hover {
    transform: translateY(-3px) scale(1.05) translateZ(0);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.shader-btn-primary {
    background: var(--slider-button-bg);
    color: var(--slider-button-text);
    border: 2px solid var(--slider-button-bg);
}

.shader-btn-primary:hover {
    background: var(--slider-button-hover-bg);
    color: var(--slider-button-text);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(52, 195, 143, 0.4);
}

.shader-btn-secondary {
    background: var(--slider-button-secondary-bg);
    color: var(--slider-button-secondary-text);
    border: 2px solid var(--slider-button-secondary-text);
}

.shader-btn-secondary:hover {
    background: var(--slider-button-secondary-hover-bg);
    color: var(--slider-button-secondary-hover-text);
    border-color: var(--slider-button-secondary-hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Navigation Dots - Hidden */
.shader-pagination {
    display: none !important;
}

/* Progress Bar for Slide Timing */
.shader-progress-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 4px;
    background: var(--slider-progress-bg);
    border-radius: 2px;
    z-index: 3;
    overflow: hidden;
}

.shader-progress-bar {
    height: 100%;
    background: var(--slider-progress-fill);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Navigation Arrows - Hidden */
.shader-navigation {
    display: none !important;
}

/* Progress Bar */
.shader-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 3;
}

.shader-progress-bar {
    height: 100%;
    background: var(--bs-primary, #007bff);
    width: 0;
    transition: width 0.3s ease;
}

/* Advanced Shader Effects */
.shader-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        ellipse at center,
        transparent 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(0, 0, 0, 0.3) 100%
    );
    z-index: 0;
    opacity: 0;
    transition: opacity 2s ease;
}

.shader-slide.active::before {
    opacity: 1;
    animation: shaderVignette 10s ease-in-out infinite;
}

@keyframes shaderVignette {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1) translateZ(0);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1) translateZ(0);
    }
}

/* Particle Effect Overlay */
.shader-slide::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.1), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    z-index: 0;
    opacity: 0;
    animation: shaderParticles 20s linear infinite;
}

.shader-slide.active::after {
    opacity: 0.4;
}

@keyframes shaderParticles {
    0% {
        transform: translateY(0) translateZ(0);
    }
    100% {
        transform: translateY(-100px) translateZ(0);
    }
}

/* Glitch Effect for Transitions */
.shader-slide.glitch-effect {
    animation: shaderGlitch 0.3s ease-in-out;
}

@keyframes shaderGlitch {
    0%, 100% {
        transform: translate(0) translateZ(0);
        filter: hue-rotate(0deg);
    }
    20% {
        transform: translate(-2px, 2px) translateZ(0);
        filter: hue-rotate(90deg);
    }
    40% {
        transform: translate(-2px, -2px) translateZ(0);
        filter: hue-rotate(180deg);
    }
    60% {
        transform: translate(2px, 2px) translateZ(0);
        filter: hue-rotate(270deg);
    }
    80% {
        transform: translate(2px, -2px) translateZ(0);
        filter: hue-rotate(360deg);
    }
}

/* Lens Flare Effect */
.shader-slide-content::before {
    content: '';
    position: absolute;
    top: 20%;
    right: 20%;
    width: 100px;
    height: 100px;
    background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 30%,
        transparent 70%
    );
    border-radius: 50%;
    opacity: 0;
    transform: scale(0) translateZ(0);
    transition: all 2s cubic-bezier(0.23, 1, 0.32, 1);
    pointer-events: none;
}

.shader-slide.active .shader-slide-content::before {
    opacity: 1;
    transform: scale(1) translateZ(0);
    animation: shaderLensFlare 8s ease-in-out infinite;
}

@keyframes shaderLensFlare {
    0%, 100% {
        opacity: 0.1;
        transform: scale(1) translate(0, 0) translateZ(0);
    }
    25% {
        opacity: 0.3;
        transform: scale(1.2) translate(-20px, 10px) translateZ(0);
    }
    50% {
        opacity: 0.2;
        transform: scale(0.8) translate(20px, -10px) translateZ(0);
    }
    75% {
        opacity: 0.4;
        transform: scale(1.1) translate(-10px, -20px) translateZ(0);
    }
}

/* Theme-responsive styles are now handled by CSS variables above */

/* Responsive Design */
@media (max-width: 992px) {
    .shader-slider-container {
        width: calc(100% - 3rem);
        height: 65vh;
        min-height: 450px;
        margin: 1.5rem auto 0 auto;
        margin-left: 1.5rem;
        margin-right: 1.5rem;
    }

    .shader-title {
        font-size: 2.5rem;
    }

    .shader-description {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .shader-slider-container {
        width: calc(100% - 2rem);
        height: 55vh;
        min-height: 400px;
        margin: 1.5rem auto 0 auto;
        margin-left: 1rem;
        margin-right: 1rem;
    }

    .shader-title {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .shader-description {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    .shader-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .shader-btn {
        padding: 12px 25px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .shader-slider-container {
        width: calc(100% - 1.5rem);
        height: 50vh;
        min-height: 350px;
        margin: 1.5rem auto 0 auto;
        margin-left: 0.75rem;
        margin-right: 0.75rem;
    }

    .shader-slide-content {
        padding: 0 15px;
    }

    .shader-title {
        font-size: 1.8rem;
    }

    .shader-subtitle {
        font-size: 1rem;
        margin-bottom: 15px;
    }

    .shader-description {
        font-size: 0.95rem;
        margin-bottom: 25px;
    }

    /* Reduce effects on mobile for better performance */
    .shader-slide::after {
        display: none;
    }

    .shader-slide-content::before {
        display: none;
    }

    .shader-slide.active .shader-slide-bg {
        animation: none;
        transform: scale(1) translateZ(0);
    }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    .shader-slide,
    .shader-video-bg,
    .shader-video-bg video,
    .shader-slide-overlay,
    .shader-subtitle,
    .shader-title,
    .shader-description,
    .shader-buttons,
    .shader-btn {
        animation: none !important;
        transition: none !important;
    }

    .shader-video-bg video {
        transform: translate(-50%, -50%) scale(1.1) translateZ(0);
    }
}

/* GPU Acceleration for all animated elements */
.shader-slide,
.shader-video-bg,
.shader-video-bg video,
.shader-slide-overlay,
.shader-slide-content,
.shader-subtitle,
.shader-title,
.shader-description,
.shader-buttons,
.shader-btn,
.shader-dot,
.shader-nav-btn,
.shader-progress-bar {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Optimize video performance */
.shader-video-bg video {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}
