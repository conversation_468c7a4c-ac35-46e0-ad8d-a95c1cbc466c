/**
 * Grand-Style Theme JavaScript
 * Custom implementation for modern admin dashboard
 */

class GrandTheme {
    constructor() {
        this.init();
    }

    init() {
        this.initSidebar();
        this.initThemeToggle();
        this.initSearch();
        this.initDropdowns();
        this.initCounters();
        this.initTooltips();
        this.initAnimations();
        this.initRippleEffects();
        this.initNotifications();
        this.loadThemeSettings();
    }

    // Sidebar functionality
    initSidebar() {
        const hamburger = document.getElementById('topnav-hamburger-icon');
        const sidebar = document.querySelector('.app-menu');
        const body = document.body;
        const horizontalLogo = document.querySelector('.horizontal-logo');

        if (hamburger) {
            hamburger.addEventListener('click', () => {
                body.classList.toggle('sidebar-enable');

                // Handle logo visibility on mobile
                if (window.innerWidth <= 991) {
                    if (body.classList.contains('sidebar-enable')) {
                        horizontalLogo.style.display = 'none';
                    } else {
                        horizontalLogo.style.display = 'block';
                    }
                }
            });
        }

        // Close sidebar on outside click (mobile)
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 991) {
                if (!sidebar.contains(e.target) && !hamburger.contains(e.target)) {
                    body.classList.remove('sidebar-enable');
                    if (horizontalLogo) {
                        horizontalLogo.style.display = 'block';
                    }
                }
            }
        });

        // Handle collapsible menu items
        const menuLinks = document.querySelectorAll('[data-bs-toggle="collapse"]');
        menuLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    const bsCollapse = new bootstrap.Collapse(target, {
                        toggle: true
                    });
                }
            });
        });

        // Set active menu item
        this.setActiveMenuItem();
    }

    setActiveMenuItem() {
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll('.nav-link');

        menuLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href.replace(/^\//, ''))) {
                link.classList.add('active');
                // Add slide-in animation
                link.classList.add('slide-in-left');

                // Open parent collapse if exists
                const parentCollapse = link.closest('.collapse');
                if (parentCollapse) {
                    parentCollapse.classList.add('show');
                    // Animate parent menu item
                    const parentLink = document.querySelector(`[data-bs-target="#${parentCollapse.id}"]`);
                    if (parentLink) {
                        parentLink.classList.add('active');
                    }
                }
            }
        });
    }

    // Theme toggle functionality
    initThemeToggle() {
        // Theme toggle button in header
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Theme customizer toggle (if exists)
        const themeToggle = document.querySelector('[data-bs-theme-toggle]');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Theme customizer radio buttons
        const themeRadios = document.querySelectorAll('input[name="data-bs-theme"]');
        themeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.setTheme(e.target.value);
            });
        });
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-bs-theme', theme);
        localStorage.setItem('theme', theme);

        // Update theme customizer radio
        const themeRadio = document.querySelector(`input[name="data-bs-theme"][value="${theme}"]`);
        if (themeRadio) {
            themeRadio.checked = true;
        }

        // Update theme toggle icon
        this.updateThemeIcon(theme);
    }

    updateThemeIcon(theme) {
        const themeIcon = document.getElementById('theme-icon');
        if (themeIcon) {
            if (theme === 'dark') {
                themeIcon.className = 'ri-moon-line fs-22';
            } else {
                themeIcon.className = 'ri-sun-line fs-22';
            }
        }
    }

    loadThemeSettings() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);
    }



    // Search functionality
    initSearch() {
        const searchInput = document.getElementById('search-options');
        const searchClose = document.getElementById('search-close-options');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const value = e.target.value;
                if (value.length > 0) {
                    searchClose.classList.remove('d-none');
                } else {
                    searchClose.classList.add('d-none');
                }
            });
        }

        if (searchClose) {
            searchClose.addEventListener('click', () => {
                searchInput.value = '';
                searchClose.classList.add('d-none');
                searchInput.focus();
            });
        }
    }

    // Initialize dropdowns
    initDropdowns() {
        const dropdownTriggers = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        dropdownTriggers.forEach(trigger => {
            new bootstrap.Dropdown(trigger);
        });
    }

    // Animated counters
    initCounters() {
        const counters = document.querySelectorAll('.counter-value');

        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseFloat(counter.getAttribute('data-target'));

                    if (window.CountUp) {
                        const countUp = new CountUp(counter, target, {
                            duration: 2,
                            separator: ',',
                            decimal: '.',
                            suffix: counter.textContent.includes('k') ? 'k' :
                                   counter.textContent.includes('M') ? 'M' : ''
                        });

                        if (!countUp.error) {
                            countUp.start();
                        }
                    } else {
                        // Fallback if CountUp is not available
                        counter.textContent = target;
                    }

                    observer.unobserve(counter);
                }
            });
        }, observerOptions);

        counters.forEach(counter => {
            observer.observe(counter);
        });
    }

    // Initialize tooltips
    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Initialize animations
    initAnimations() {
        // Add fade-in animation to cards
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in-up');
        });

        // Add slide-in animation to sidebar menu items
        const menuItems = document.querySelectorAll('.nav-item');
        menuItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.05}s`;
            item.classList.add('slide-in-left');
        });
    }

    // Initialize ripple effects
    initRippleEffects() {
        const buttons = document.querySelectorAll('.btn-topbar, .nav-link');
        buttons.forEach(button => {
            button.addEventListener('click', this.createRipple.bind(this));
        });
    }

    createRipple(event) {
        const button = event.currentTarget;
        const circle = document.createElement('span');
        const diameter = Math.max(button.clientWidth, button.clientHeight);
        const radius = diameter / 2;

        circle.style.width = circle.style.height = `${diameter}px`;
        circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
        circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
        circle.classList.add('ripple');

        const ripple = button.getElementsByClassName('ripple')[0];
        if (ripple) {
            ripple.remove();
        }

        button.appendChild(circle);
    }

    // Notification management
    initNotifications() {
        const notificationCheckboxes = document.querySelectorAll('.notification-check input');
        const selectContent = document.getElementById('select-content');

        if (notificationCheckboxes.length > 0) {
            notificationCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    const checkedCount = document.querySelectorAll('.notification-check input:checked').length;
                    if (selectContent) {
                        selectContent.textContent = checkedCount;
                    }
                });
            });
        }
    }

    // Utility methods
    showToast(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        const toast = this.createToast(message, type);
        toastContainer.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }

    createToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        return toast;
    }

    // Layout utilities
    updateLayout(layout) {
        document.documentElement.setAttribute('data-layout', layout);
        localStorage.setItem('layout', layout);
    }

    updateSidebarSize(size) {
        document.documentElement.setAttribute('data-sidebar-size', size);
        localStorage.setItem('sidebarSize', size);
    }

    updateSidebarColor(color) {
        document.documentElement.setAttribute('data-sidebar', color);
        localStorage.setItem('sidebarColor', color);
    }

    // Reset theme to defaults
    resetTheme() {
        localStorage.removeItem('theme');
        localStorage.removeItem('layout');
        localStorage.removeItem('sidebarSize');
        localStorage.removeItem('sidebarColor');

        document.documentElement.setAttribute('data-bs-theme', 'light');
        document.documentElement.setAttribute('data-layout', 'vertical');
        document.documentElement.setAttribute('data-sidebar-size', 'lg');
        document.documentElement.setAttribute('data-sidebar', 'dark');

        // Reset customizer form
        const form = document.querySelector('#theme-settings-offcanvas form');
        if (form) {
            form.reset();
        }

        this.showToast('Theme reset to default settings', 'success');
    }
}

// Initialize theme when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.grandTheme = new GrandTheme();

    // Reset button functionality
    const resetButton = document.getElementById('reset-layout');
    if (resetButton) {
        resetButton.addEventListener('click', () => {
            window.grandTheme.resetTheme();
        });
    }
});

// Handle window resize
window.addEventListener('resize', () => {
    const horizontalLogo = document.querySelector('.horizontal-logo');

    if (window.innerWidth > 991) {
        document.body.classList.remove('sidebar-enable');
        // Hide horizontal logo on desktop
        if (horizontalLogo) {
            horizontalLogo.style.display = 'none';
        }
    } else {
        // Show horizontal logo on mobile when sidebar is closed
        if (horizontalLogo && !document.body.classList.contains('sidebar-enable')) {
            horizontalLogo.style.display = 'block';
        }
    }
});

// Export for global access
window.GrandTheme = GrandTheme;
