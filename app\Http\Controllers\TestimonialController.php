<?php

namespace App\Http\Controllers;

use App\Models\Testimonials;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class TestimonialController extends Controller
{
    /**
     * Display a listing of testimonials
     */
    public function index()
    {
        try {
            $testimonials = Testimonials::ordered()->get();
            return view('backend.pages.testimonials.index', compact('testimonials'));
        } catch (\Exception $e) {
            Log::error('Error loading testimonials: ' . $e->getMessage());
            return view('backend.pages.testimonials.index', ['testimonials' => collect()]);
        }
    }

    /**
     * Show the form for creating a new testimonial
     */
    public function create()
    {
        return view('backend.pages.testimonials.create');
    }

    /**
     * Store a newly created testimonial
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'comment' => 'required|string|max:2000',
            'rating' => 'required|integer|min:1|max:5',
            'client_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'name', 'position', 'company', 'comment', 'rating', 'status', 'featured'
        ]);

        // Handle client image upload
        if ($request->hasFile('client_image')) {
            $data['client_image'] = $request->file('client_image')->store('testimonials/images', 'public');
        }

        // Set featured flag
        $data['featured'] = $request->has('featured');

        try {
            Testimonials::create($data);
            return redirect()->route('testimonials.index')
                ->with('success', 'Testimonial created successfully!');
        } catch (\Exception $e) {
            Log::error('Error creating testimonial', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create testimonial: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified testimonial
     */
    public function show(Testimonials $testimonial)
    {
        return view('backend.pages.testimonials.show', compact('testimonial'));
    }

    /**
     * Show the form for editing the specified testimonial
     */
    public function edit(Testimonials $testimonial)
    {
        return view('backend.pages.testimonials.edit', compact('testimonial'));
    }

    /**
     * Update the specified testimonial
     */
    public function update(Request $request, Testimonials $testimonial)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'comment' => 'required|string|max:2000',
            'rating' => 'required|integer|min:1|max:5',
            'client_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only([
            'name', 'position', 'company', 'comment', 'rating', 'status', 'featured'
        ]);

        // Handle client image upload
        if ($request->hasFile('client_image')) {
            // Delete old image
            if ($testimonial->client_image && Storage::disk('public')->exists($testimonial->client_image)) {
                Storage::disk('public')->delete($testimonial->client_image);
            }
            $data['client_image'] = $request->file('client_image')->store('testimonials/images', 'public');
        }

        // Set featured flag
        $data['featured'] = $request->has('featured');

        try {
            $testimonial->update($data);
            return redirect()->route('testimonials.index')
                ->with('success', 'Testimonial updated successfully!');
        } catch (\Exception $e) {
            Log::error('Error updating testimonial', [
                'error' => $e->getMessage(),
                'testimonial_id' => $testimonial->id,
                'data' => $data
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Failed to update testimonial: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified testimonial
     */
    public function destroy(Testimonials $testimonial)
    {
        try {
            $testimonial->delete();
            return redirect()->route('testimonials.index')
                ->with('success', 'Testimonial deleted successfully!');
        } catch (\Exception $e) {
            Log::error('Error deleting testimonial: ' . $e->getMessage());
            return redirect()->route('testimonials.index')
                ->with('error', 'Failed to delete testimonial');
        }
    }

    /**
     * Toggle testimonial status
     */
    public function toggleStatus(Testimonials $testimonial)
    {
        try {
            $newStatus = $testimonial->status === 'active' ? 'inactive' : 'active';
            $testimonial->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'status' => $newStatus,
                'message' => 'Testimonial status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling testimonial status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status'
            ], 500);
        }
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(Testimonials $testimonial)
    {
        try {
            $testimonial->update(['featured' => !$testimonial->featured]);

            return response()->json([
                'success' => true,
                'featured' => $testimonial->featured,
                'message' => 'Testimonial featured status updated successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling testimonial featured status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update featured status'
            ], 500);
        }
    }

    /**
     * Reorder testimonials
     */
    public function reorder(Request $request)
    {
        try {
            $orders = $request->input('order', []);
            
            foreach ($orders as $index => $id) {
                Testimonials::where('id', $id)->update(['sort_order' => $index + 1]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Testimonials reordered successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error('Error reordering testimonials: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder testimonials'
            ], 500);
        }
    }

    /**
     * Duplicate a testimonial
     */
    public function duplicate(Testimonials $testimonial)
    {
        try {
            $newTestimonial = $testimonial->replicate();
            $newTestimonial->name = $testimonial->name . ' (Copy)';
            $newTestimonial->status = 'inactive'; // Set copy as inactive by default
            $newTestimonial->featured = false; // Remove featured status from copy
            $newTestimonial->sort_order = Testimonials::max('sort_order') + 1;
            $newTestimonial->save();

            return redirect()->route('testimonials.index')
                ->with('success', 'Testimonial duplicated successfully!');
        } catch (\Exception $e) {
            Log::error('Error duplicating testimonial: ' . $e->getMessage());
            return redirect()->route('testimonials.index')
                ->with('error', 'Failed to duplicate testimonial');
        }
    }

    /**
     * Get testimonials data for homepage
     */
    public function getHomepageData()
    {
        // First try to get featured testimonials, if none exist, get all active testimonials
        $testimonials = Testimonials::active()->featured()->ordered()->take(6)->get();
        
        if ($testimonials->isEmpty()) {
            // If no featured testimonials, get all active testimonials
            $testimonials = Testimonials::active()->ordered()->take(6)->get();
        }
        
        if ($testimonials->isEmpty()) {
            return null;
        }

        return $testimonials->map(function ($testimonial) {
            return [
                'id' => $testimonial->id,
                'name' => $testimonial->name,
                'position' => $testimonial->position,
                'company' => $testimonial->company,
                'comment' => $testimonial->comment,
                'rating' => $testimonial->rating,
                'client_image_url' => $testimonial->client_image_url,
                'status' => $testimonial->status,
                'featured' => $testimonial->featured,
            ];
        });
    }
}
