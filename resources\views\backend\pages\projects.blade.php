@extends('backend.layouts.app')

@section('title', 'Add Project - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Add Project</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Add Project</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Project Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('store.projects') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="project_name" class="form-label">Project Name</label>
                                <input type="text" class="form-control @error('project_name') is-invalid @enderror" 
                                       id="project_name" name="project_name" placeholder="Enter project name" 
                                       value="{{ old('project_name') }}" required>
                                @error('project_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="project_description" class="form-label">Project Description</label>
                                <textarea class="form-control @error('project_description') is-invalid @enderror" 
                                          id="project_description" name="project_description" rows="4" 
                                          placeholder="Enter project description" required>{{ old('project_description') }}</textarea>
                                @error('project_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="project_image" class="form-label">Project Image</label>
                                <input type="file" class="form-control @error('project_image') is-invalid @enderror" 
                                       id="project_image" name="project_image" accept="image/*">
                                @error('project_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Upload a representative image for this project</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Save Project
                        </button>
                        <a href="{{ route('all.projects') }}" class="btn btn-secondary ms-2">
                            <i class="ri-list-check me-1"></i>View All Projects
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Project Guidelines</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Tips for adding projects:</h6>
                    <ul class="mb-0">
                        <li>Use descriptive project names</li>
                        <li>Include key project details and outcomes</li>
                        <li>Upload high-quality project images</li>
                        <li>Highlight technologies and solutions used</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
