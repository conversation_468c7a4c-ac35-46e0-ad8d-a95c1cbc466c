
<div class="shader-slider-container" id="sliderContainer" <?php if(!isset($sliders) || $sliders->count() === 0): ?> style="display: none;" <?php endif; ?>>
    <div class="shader-slider" id="shaderSlider">

        
        <div class="shader-slider-wrapper" id="dynamicSliderWrapper">
            <?php if(isset($sliders) && $sliders->count() > 0): ?>
                
                <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $hasMedia = (isset($slider['media_type']) && $slider['media_type'] === 'video' && $slider['video_url']) || ($slider['slider_one_img_url'] ?? $slider['image_url']);
                        $backgroundStyle = '';
                        $textColorStyle = '';

                        // Only apply custom colors for slides with media
                        if ($hasMedia) {
                            if ($slider['background_color'] ?? false) {
                                $backgroundStyle = 'background-color: ' . $slider['background_color'] . ';';
                            }
                            if ($slider['text_color'] ?? false) {
                                $textColorStyle = 'color: ' . $slider['text_color'] . ';';
                            }
                        }
                    ?>
                    <div class="shader-slide <?php echo e($index === 0 ? 'active' : ''); ?> <?php echo e($hasMedia ? 'has-media' : ''); ?>"
                         data-duration="<?php echo e($slider['display_duration'] ?? 12); ?>"
                         data-text-align="<?php echo e($slider['text_alignment'] ?? 'center'); ?>"
                         style="text-align: <?php echo e($slider['text_alignment'] ?? 'center'); ?>; <?php echo e($backgroundStyle); ?> <?php echo e($textColorStyle); ?>"
                         <?php if($hasMedia && ($slider['background_color'] ?? false)): ?> data-bg-color="<?php echo e($slider['background_color']); ?>" <?php endif; ?>
                         <?php if($hasMedia && ($slider['text_color'] ?? false)): ?> data-text-color="<?php echo e($slider['text_color']); ?>" <?php endif; ?>>
                        <div class="shader-video-bg">
                            <?php if(isset($slider['media_type']) && $slider['media_type'] === 'video' && $slider['video_url']): ?>
                                <video autoplay muted loop class="shader-video">
                                    <source src="<?php echo e($slider['video_url']); ?>" type="video/mp4">
                                </video>
                            <?php elseif($slider['slider_one_img_url'] ?? $slider['image_url']): ?>
                                <img src="<?php echo e($slider['slider_one_img_url'] ?? $slider['image_url']); ?>"
                                     alt="<?php echo e($slider['first_slogan_one'] ?? $slider['title']); ?>"
                                     class="shader-image">
                            <?php endif; ?>
                        </div>
                        <div class="shader-slide-overlay"></div>
                        <div class="shader-slide-content">
                            <div class="container">
                                <div class="shader-content-inner">
                                    
                                    <?php if($slider['second_slogan_one'] ?? $slider['subtitle']): ?>
                                        <span class="shader-subtitle"
                                              data-animation="<?php echo e($slider['animation_settings']['subtitle_animation'] ?? 'fadeInUp'); ?>">
                                            <?php echo e($slider['second_slogan_one'] ?? $slider['subtitle']); ?>

                                        </span>
                                    <?php endif; ?>

                                    
                                    <?php if($slider['first_slogan_one'] ?? $slider['title']): ?>
                                        <h1 class="shader-title"
                                            data-animation="<?php echo e($slider['animation_settings']['title_animation'] ?? 'fadeInRight'); ?>">
                                            <?php echo e($slider['first_slogan_one'] ?? $slider['title']); ?>

                                        </h1>
                                    <?php endif; ?>

                                    
                                    <?php if($slider['first_slogan_two'] ?? $slider['description']): ?>
                                        <p class="shader-description"
                                           data-animation="<?php echo e($slider['animation_settings']['description_animation'] ?? 'fadeInDown'); ?>">
                                            <?php echo e($slider['first_slogan_two'] ?? $slider['description']); ?>

                                        </p>
                                    <?php endif; ?>

                                    
                                    <?php if($slider['second_slogan_two'] ?? $slider['secondary_description']): ?>
                                        <p class="shader-description"
                                           data-animation="<?php echo e($slider['animation_settings']['description_animation'] ?? 'fadeInLeft'); ?>">
                                            <?php echo e($slider['second_slogan_two'] ?? $slider['secondary_description']); ?>

                                        </p>
                                    <?php endif; ?>

                                    
                                    <?php if(isset($slider['button_text']) && $slider['button_text'] && isset($slider['button_link']) && $slider['button_link']): ?>
                                        <div class="shader-buttons"
                                             data-animation="<?php echo e($slider['animation_settings']['button_animation'] ?? 'fadeInUp'); ?>">
                                            <a href="<?php echo e($slider['button_link']); ?>" class="shader-btn shader-btn-primary">
                                                <span><?php echo e($slider['button_text']); ?></span>
                                                <i class="fas fa-arrow-right"></i>
                                            </a>
                                        </div>
                                    <?php endif; ?>


                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                
                <div class="no-sliders-message text-center py-5" style="display: none;">
                    <p class="text-muted">No sliders available</p>
                </div>
            <?php endif; ?>
        </div>



        
        <div class="shader-progress-container" id="sliderProgress">
            <div class="shader-progress-bar" id="sliderProgressBar"></div>
        </div>
    </div>
</div>

<script>
// Initialize slider functionality
document.addEventListener('DOMContentLoaded', function() {
    const sliderWrapper = document.getElementById('dynamicSliderWrapper');
    const hasServerSliders = sliderWrapper && sliderWrapper.children.length > 0;

    if (hasServerSliders) {
        // Server-side sliders are present, initialize the slider
        console.log('Using server-side rendered sliders');
        initializeSlider();
    } else {
        // No server-side sliders, try to load from database
        console.log('No server-side sliders, checking database...');
        loadDynamicSliders();
    }

    // Listen for slider refresh messages from admin panel
    window.addEventListener('message', function(event) {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'REFRESH_SLIDERS') {
            console.log('Refreshing sliders from admin panel update');
            loadDynamicSliders();
        }
    });
});

async function loadDynamicSliders() {
    try {
        // Add cache-busting parameter to ensure fresh data
        const response = await fetch(`/api/sliders?t=${Date.now()}`);
        const sliders = await response.json();

        if (sliders && sliders.length > 0) {
            renderSliders(sliders);
            initializeSlider();
        } else {
            // No database sliders found - hide slider section
            console.log('No database sliders found, hiding slider section');
            hideSliderSection();
        }
    } catch (error) {
        console.error('Error loading sliders:', error);
        // Hide slider section on error
        hideSliderSection();
    }
}

function renderSliders(sliders) {
    const wrapper = document.getElementById('dynamicSliderWrapper');
    if (!wrapper) return;

    // Clear existing content
    wrapper.innerHTML = '';

    sliders.forEach((slider, index) => {
        const slideHtml = createSlideHtml(slider, index === 0);
        wrapper.insertAdjacentHTML('beforeend', slideHtml);
    });

    // Generate navigation controls if there are multiple sliders
    if (sliders.length > 1) {
        generateNavigationControls(sliders.length);
    }
}

function generateNavigationControls(sliderCount) {
    // Generate pagination dots
    const pagination = document.getElementById('sliderPagination');
    if (pagination) {
        pagination.innerHTML = '';
        for (let i = 0; i < sliderCount; i++) {
            const dot = document.createElement('span');
            dot.className = `shader-dot ${i === 0 ? 'active' : ''}`;
            dot.setAttribute('data-slide', i);
            pagination.appendChild(dot);
        }
    }
}

function createSlideHtml(slider, isActive) {
    // Handle both old and new slider formats
    const title = slider.first_slogan_one || slider.title || '';
    const subtitle = slider.second_slogan_one || slider.subtitle || '';
    const description = slider.first_slogan_two || slider.description || '';
    const secondaryDescription = slider.second_slogan_two || slider.secondary_description || '';
    const imageUrl = slider.slider_one_img_url || slider.image_url || '';
    const videoUrl = slider.video_url || '';
    const mediaType = slider.media_type || 'image';
    const buttonText = slider.button_text || '';
    const buttonLink = slider.button_link || '';
    // For slides without media, use transparent background to follow page theme
    // For slides with media, use custom colors if provided
    const backgroundColor = hasMedia ? (slider.background_color || 'transparent') : 'transparent';
    const textColor = hasMedia ? (slider.text_color || 'inherit') : 'inherit';
    const textAlignment = slider.text_alignment || 'center';

    // Create media element based on type
    let mediaElement = '';
    let hasMedia = false;
    if (mediaType === 'video' && videoUrl) {
        mediaElement = `<video autoplay muted loop class="shader-video">
                           <source src="${videoUrl}" type="video/mp4">
                       </video>`;
        hasMedia = true;
    } else if (imageUrl) {
        mediaElement = `<img src="${imageUrl}" alt="${title}" class="shader-image">`;
        hasMedia = true;
    }

    return `
        <div class="shader-slide ${isActive ? 'active' : ''} ${hasMedia ? 'has-media' : ''}"
             data-duration="${slider.display_duration || 12}"
             data-text-align="${textAlignment}"
             style="background-color: ${backgroundColor}; color: ${textColor}; text-align: ${textAlignment};">
            <div class="shader-video-bg">
                ${mediaElement}
            </div>
            <div class="shader-slide-overlay"></div>
            <div class="shader-slide-content">
                <div class="container">
                    <div class="shader-content-inner">
                        ${subtitle ? `<span class="shader-subtitle" data-animation="fadeInUp">${subtitle}</span>` : ''}
                        ${title ? `<h1 class="shader-title" data-animation="fadeInRight">${title}</h1>` : ''}
                        ${description ? `<p class="shader-description" data-animation="fadeInDown">${description}</p>` : ''}
                        ${secondaryDescription ? `<p class="shader-description" data-animation="fadeInLeft">${secondaryDescription}</p>` : ''}
                        ${buttonText && buttonLink ? `
                            <div class="shader-buttons" data-animation="fadeInUp">
                                <a href="${buttonLink}" class="shader-btn shader-btn-primary">
                                    <span>${buttonText}</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
}



function showSliderControls() {
    // Show the entire slider container and navigation when sliders are present
    const sliderContainer = document.getElementById('sliderContainer');
    const pagination = document.getElementById('sliderPagination');
    const navigation = document.getElementById('sliderNavigation');
    const progress = document.getElementById('sliderProgress');

    if (sliderContainer) sliderContainer.style.display = 'block';
    if (pagination) pagination.style.display = 'none'; // Hidden as per design
    if (navigation) navigation.style.display = 'none'; // Hidden as per design
    if (progress) progress.style.display = 'block'; // Show progress bar
}

function hideSliderSection() {
    // Hide the entire slider section when no sliders are available
    const sliderContainer = document.getElementById('sliderContainer');
    const sliderWrapper = document.getElementById('dynamicSliderWrapper');

    if (sliderWrapper) {
        sliderWrapper.innerHTML = '';
    }

    // Hide navigation controls
    const pagination = document.getElementById('sliderPagination');
    const navigation = document.getElementById('sliderNavigation');
    const progress = document.getElementById('sliderProgress');

    if (pagination) pagination.style.display = 'none';
    if (navigation) navigation.style.display = 'none';
    if (progress) progress.style.display = 'none';

    // Hide the entire slider container to remove empty space
    if (sliderContainer) {
        sliderContainer.style.display = 'none';
    }

    console.log('Slider section hidden - no sliders available');
}

function initializeSlider() {
    // Initialize the shader slider if the class exists
    if (typeof ShaderSlider !== 'undefined') {
        const sliderElement = document.getElementById('shaderSlider');
        if (sliderElement) {
            new ShaderSlider(sliderElement, {
                autoplay: true,
                autoplayDelay: 12000, // 12 seconds per slide (increased from 8 seconds)
                animationDuration: 1500, // Slightly longer transition animation
                transitionDuration: 2000, // 2 seconds for slide transition
                enableTouch: false,
                enableKeyboard: false,
                pauseOnHover: true, // Pause when user hovers over slider
                enableParallax: false,
                enableParticles: false,
                transitionEffect: 'fade',
                easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)' // Smooth easing
            });
        }
    }

    // Listen for theme changes and update slider styles
    window.addEventListener('themeChanged', function(event) {
        updateSliderTheme(event.detail.theme);
    });
}

function updateSliderTheme(theme) {
    // Update slider styles to follow theme changes
    const sliderContainer = document.querySelector('.shader-slider-container');
    const slidesWithoutMedia = document.querySelectorAll('.shader-slide:not(.has-media)');

    if (sliderContainer) {
        // Force a repaint to ensure theme changes are applied immediately
        sliderContainer.style.transform = 'translateZ(0)';
        setTimeout(() => {
            sliderContainer.style.transform = '';
        }, 10);
    }

    // Ensure slides without media follow the new theme
    slidesWithoutMedia.forEach(slide => {
        // Remove any inline background colors for slides without media
        if (!slide.classList.contains('has-media')) {
            const currentStyle = slide.getAttribute('style') || '';
            const newStyle = currentStyle.replace(/background-color:[^;]*;?/g, '').replace(/color:[^;]*;?/g, '');
            slide.setAttribute('style', newStyle);

            // Force reflow to apply CSS variables
            slide.style.transform = 'translateZ(0)';
            setTimeout(() => {
                slide.style.transform = '';
            }, 10);
        }
    });

    console.log('Slider theme updated to:', theme);
}
</script>
<?php /**PATH C:\xampp\htdocs\Grandtek\resources\views/frontend/include/sliders.blade.php ENDPATH**/ ?>