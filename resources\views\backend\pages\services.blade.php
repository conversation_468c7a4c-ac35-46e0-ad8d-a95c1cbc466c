@extends('backend.layouts.app')

@section('title', 'Add Service - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Add Service</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Add Service</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Service Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('store.services') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="service_name" class="form-label">Service Name</label>
                                <input type="text" class="form-control @error('service_name') is-invalid @enderror" 
                                       id="service_name" name="service_name" placeholder="Enter service name" 
                                       value="{{ old('service_name') }}" required>
                                @error('service_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="service_desc" class="form-label">Service Description</label>
                                <textarea class="form-control @error('service_desc') is-invalid @enderror" 
                                          id="service_desc" name="service_desc" rows="4" 
                                          placeholder="Enter service description" required>{{ old('service_desc') }}</textarea>
                                @error('service_desc')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="service_img" class="form-label">Service Icon</label>
                                <input type="file" class="form-control @error('service_img') is-invalid @enderror" 
                                       id="service_img" name="service_img" accept="image/*">
                                @error('service_img')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Upload an icon for this service (PNG, JPG, SVG recommended)</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>Save Service
                        </button>
                        <a href="{{ route('all.service') }}" class="btn btn-secondary ms-2">
                            <i class="ri-list-check me-1"></i>View All Services
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Service Guidelines</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Tips for adding services:</h6>
                    <ul class="mb-0">
                        <li>Use clear, descriptive service names</li>
                        <li>Write compelling descriptions that highlight benefits</li>
                        <li>Upload high-quality icons (recommended size: 64x64px)</li>
                        <li>Keep descriptions concise but informative</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
