<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('systems', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->longText('full_description')->nullable();
            $table->string('category')->nullable();
            $table->json('technologies')->nullable(); // Array of technologies used
            $table->string('system_url')->nullable(); // Live demo URL
            $table->string('github_url')->nullable(); // GitHub repository URL
            $table->string('documentation_url')->nullable(); // Documentation URL
            $table->string('system_image')->nullable(); // Main system image
            $table->json('gallery_images')->nullable(); // Array of additional images
            $table->json('features')->nullable(); // Array of key features
            $table->enum('status', ['active', 'inactive', 'development', 'maintenance'])->default('active');
            $table->boolean('featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0.00); // Average rating (0.00 to 5.00)
            $table->integer('total_ratings')->default(0); // Total number of ratings
            $table->integer('views_count')->default(0); // Track system views
            $table->date('launch_date')->nullable();
            $table->string('version')->nullable();
            $table->text('changelog')->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['status', 'featured']);
            $table->index(['sort_order', 'created_at']);
            $table->index('average_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('systems');
    }
};
