<header id="page-topbar">
    <div class="layout-width">
        <div class="navbar-header">
            <div class="d-flex">
                <!-- LOGO - Only visible on mobile -->
                <div class="navbar-brand-box horizontal-logo">
                    <a href="{{ route('dashboard') }}" class="logo logo-dark">
                        <span class="logo-sm">
                            <img src="{{ asset('backend/images/logo-icon.png') }}" alt="GrandTek Logo" style="width: 32px; height: 32px; object-fit: contain;">
                        </span>
                        <span class="logo-lg">
                            <span class="logo-txt">GrandTek</span>
                            <img src="{{ asset('backend/images/logo-icon.png') }}" alt="GrandTek Logo" class="ms-2" style="width: 32px; height: 32px; object-fit: contain;">
                        </span>
                    </a>

                    <a href="{{ route('dashboard') }}" class="logo logo-light">
                        <span class="logo-sm">
                            <img src="{{ asset('backend/images/logo-icon.png') }}" alt="GrandTek Logo" style="width: 32px; height: 32px; object-fit: contain;">
                        </span>
                        <span class="logo-lg">
                            <span class="logo-txt">GrandTek</span>
                            <img src="{{ asset('backend/images/logo-icon.png') }}" alt="GrandTek Logo" class="ms-2" style="width: 32px; height: 32px; object-fit: contain;">
                        </span>
                    </a>
                </div>

                <button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger" id="topnav-hamburger-icon">
                    <span class="hamburger-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </button>

                <!-- App Search-->
                <form class="app-search d-none d-md-block">
                    <div class="position-relative">
                        <input type="text" class="form-control" placeholder="Search here..." autocomplete="off" id="search-options" value="">
                        <span class="ri-search-line search-widget-icon"></span>
                        <span class="ri-close-circle-line search-widget-icon search-widget-icon-close d-none" id="search-close-options"></span>
                    </div>
                </form>
            </div>

            <div class="d-flex align-items-center">
                <!-- Theme Toggle Button -->
                <div class="dropdown header-item">
                    <button type="button" class="btn btn-topbar" id="theme-toggle-btn" title="Toggle Theme">
                        <i class="ri-sun-line fs-22" id="theme-icon"></i>
                    </button>
                </div>

                <!-- Notifications -->
                <div class="dropdown topbar-head-dropdown ms-1 header-item" id="notificationDropdown">
                    <button type="button" class="btn btn-topbar position-relative" id="page-header-notifications-dropdown" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-haspopup="true" aria-expanded="false">
                        <i class='ri-notification-3-line fs-22'></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.625rem; padding: 0.25rem 0.4rem; min-width: 18px;">3<span class="visually-hidden">unread messages</span></span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0" aria-labelledby="page-header-notifications-dropdown">
                        <div class="dropdown-head bg-primary bg-pattern rounded-top">
                            <div class="p-3">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0 fs-16 fw-semibold text-white"> Notifications </h6>
                                    </div>
                                    <div class="col-auto dropdown-tabs">
                                        <span class="badge bg-light-subtle text-body fs-13"> 4 New</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 text-center">
                            <p class="text-muted">No new notifications</p>
                        </div>
                    </div>
                </div>

                <!-- User Profile -->
                <div class="dropdown ms-sm-3 header-item topbar-user">
                    <button type="button" class="btn" id="page-header-user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="d-flex align-items-center">
                            <div class="avatar-placeholder header-profile-user">
                                {{ strtoupper(substr(Auth::user()->name ?? 'AU', 0, 2)) }}
                            </div>
                            <span class="text-start ms-xl-2">
                                <span class="d-none d-xl-inline-block ms-1 fw-semibold user-name-text">{{ Auth::user()->name ?? 'Admin User' }}</span>
                                <span class="d-none d-xl-block ms-1 fs-12 user-name-sub-text">Administrator</span>
                            </span>
                        </span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end">
                        <h6 class="dropdown-header">Welcome {{ Auth::user()->name ?? 'Admin' }}!</h6>
                        <a class="dropdown-item" href="{{ route('profile.view') }}"><i class="ri-user-3-line text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Profile</span></a>
                        <a class="dropdown-item" href="{{ route('messages') }}"><i class="ri-message-3-line text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Messages</span></a>
                        <a class="dropdown-item" href="{{ route('site.setting') }}"><i class="ri-settings-4-line text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Settings</span></a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="{{ route('admin.logout') }}"><i class="ri-logout-circle-r-line text-muted fs-16 align-middle me-1"></i> <span class="align-middle" data-key="t-logout">Logout</span></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
