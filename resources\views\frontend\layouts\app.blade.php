<!DOCTYPE html>
<html lang="en" data-bs-theme="light">

<head>
    <meta charset="utf-8">
    <title>@yield('title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions')</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="@yield('keywords', 'software development Kenya, custom software solutions, web development Nairobi, mobile app development, IT consulting Kenya, software company Kenya, Laravel development, React development, database solutions Kenya')" name="keywords">
    <meta content="@yield('description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and IT solutions. Expert Laravel, React, and database development services in Nairobi and across Kenya.')" name="description">

    <!-- SEO Meta Tags -->
    <meta name="robots" content="@yield('robots', 'index, follow')">
    <meta name="author" content="GrandTek IT Solutions Kenya">
    <meta name="language" content="en-KE">
    <meta name="geo.region" content="KE">
    <meta name="geo.placename" content="Nairobi, Kenya">
    <meta name="geo.position" content="-1.286389;36.817223">
    <meta name="ICBM" content="-1.286389, 36.817223">

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="@yield('og_type', 'website')">
    <meta property="og:title" content="@yield('og_title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions')">
    <meta property="og:description" content="@yield('og_description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and IT solutions. Expert Laravel, React, and database development services in Nairobi and across Kenya.')"
    <meta property="og:url" content="@yield('og_url', url()->current())">
    <meta property="og:site_name" content="GrandTek IT Solutions">
    <meta property="og:image" content="@yield('og_image', asset('assets/img/grandtek-og-image.jpg'))">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_KE">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', 'Professional Software Development Services in Kenya | GrandTek IT Solutions')">
    <meta name="twitter:description" content="@yield('twitter_description', 'Leading software development company in Kenya offering custom web applications, mobile apps, and IT solutions.')">
    <meta name="twitter:image" content="@yield('twitter_image', asset('assets/img/grandtek-twitter-card.jpg'))">
    <meta name="twitter:site" content="@GrandTekKenya">
    <meta name="twitter:creator" content="@GrandTekKenya">

    <!-- Canonical URL -->
    <link rel="canonical" href="@yield('canonical', url()->current())">

    <!-- Alternate Language Links -->
    <link rel="alternate" hreflang="en-ke" href="{{ url()->current() }}">
    <link rel="alternate" hreflang="en" href="{{ url()->current() }}">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('assets/img/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('assets/img/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('assets/img/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ asset('assets/img/site.webmanifest') }}">

    <!-- Critical CSS for Above-the-Fold Content -->
    <style>
        /* Critical CSS for faster initial render */
        body { font-family: 'Inter', sans-serif; margin: 0; padding: 0; }
        .navbar { background-color: #007bff; }
        .hero-section { background: linear-gradient(135deg, #007bff 0%, #17a2b8 100%); }
        .spinner-border { width: 3rem; height: 3rem; }
        .d-none { display: none !important; }
        .d-flex { display: flex !important; }
        .justify-content-center { justify-content: center !important; }
        .align-items-center { align-items: center !important; }
        .position-fixed { position: fixed !important; }
        .w-100 { width: 100% !important; }
        .h-100 { height: 100% !important; }
        .bg-white { background-color: #fff !important; }
        .text-primary { color: #007bff !important; }
    </style>

    <!-- Structured Data -->
    @stack('structured_data')

    <!-- Google Analytics 4 -->
    @if(config('app.env') === 'production' && config('seo.google_analytics_id'))
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('seo.google_analytics_id') }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ config('seo.google_analytics_id') }}', {
            page_title: document.title,
            page_location: window.location.href,
            custom_map: {
                'custom_parameter_1': 'software_development_kenya'
            }
        });

        // Enhanced E-commerce tracking
        gtag('config', '{{ config('seo.google_analytics_id') }}', {
            custom_map: {'custom_parameter_1': 'page_type'},
            send_page_view: false
        });

        // Send page view with custom parameters
        gtag('event', 'page_view', {
            page_title: document.title,
            page_location: window.location.href,
            custom_parameter_1: '@yield("page_type", "general")'
        });
    </script>
    @endif

    <!-- Google Tag Manager -->
    @if(config('app.env') === 'production' && config('seo.google_tag_manager_id'))
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','{{ config('seo.google_tag_manager_id') }}');</script>
    @endif

    <!-- Google Search Console Verification -->
    @if(config('seo.google_search_console_verification'))
    <meta name="google-site-verification" content="{{ config('seo.google_search_console_verification') }}">
    @endif

    <!-- Performance Optimizations -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//www.google-analytics.com">
    <link rel="dns-prefetch" href="//www.googletagmanager.com">

    <!-- Google Web Fonts with Performance Optimization -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Saira:wght@500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Saira:wght@500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet"></noscript>

    <!-- Icon Font Stylesheet -->
    <link rel="icon" href="{{ asset('assets/logo.png') }}" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="{{ asset('assets/lib/animate/animate.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/lib/owlcarousel/assets/owl.carousel.min.css') }}" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="{{ asset('assets/css/style.css') }}" rel="stylesheet">

    <!-- Shader Slider Stylesheet -->
    <link href="{{ asset('assets/css/shader-slider.css') }}" rel="stylesheet">

    @stack('styles')

    <!-- Theme Initialization -->
    <script>
        // Initialize theme on page load
        (function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
        })();
    </script>
</head>

<body>
    <!-- Google Tag Manager (noscript) -->
    @if(config('app.env') === 'production' && config('seo.google_tag_manager_id'))
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ config('seo.google_tag_manager_id') }}"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    @endif

    <!-- Spinner Start -->
    @include('frontend.components.spinner')
    <!-- Spinner End -->

    <!-- Navbar Start -->
    @include('frontend.components.nav')
    <!-- Navbar End -->

    <!-- Main Content -->
    @yield('content')
    <!-- End Main Content -->

    <!-- Footer Start -->
    @include('frontend.components.footer')
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-secondary btn-square rounded-circle back-to-top">
        <i class="fa fa-arrow-up text-white"></i>
    </a>

    <!-- JavaScript Libraries -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('assets/lib/wow/wow.min.js') }}"></script>
    <script src="{{ asset('assets/lib/easing/easing.min.js') }}"></script>
    <script src="{{ asset('assets/lib/waypoints/waypoints.min.js') }}"></script>
    <script src="{{ asset('assets/lib/owlcarousel/owl.carousel.min.js') }}"></script>

    <!-- Template Javascript -->
    <script src="{{ asset('assets/js/main.js') }}"></script>

    <!-- Shader Slider Javascript -->
    <script src="{{ asset('assets/js/shader-slider.js') }}"></script>

    @stack('scripts')
</body>

</html>
