<div class="right-sidebar">
    <div class="switcher-icon">
        <i class="ri-palette-line"></i>
    </div>
    <div class="right-sidebar-content">
        <p class="mb-0">Theme Colors</p>
        <hr>

        <ul class="switcher">
            <li id="theme1" data-theme="theme1"></li>
            <li id="theme2" data-theme="theme2"></li>
            <li id="theme3" data-theme="theme3"></li>
            <li id="theme4" data-theme="theme4"></li>
            <li id="theme5" data-theme="theme5"></li>
            <li id="theme6" data-theme="theme6"></li>
        </ul>

        <p class="mb-0">Gradient Themes</p>
        <hr>

        <ul class="switcher">
            <li id="theme7" data-theme="theme7"></li>
            <li id="theme8" data-theme="theme8"></li>
            <li id="theme9" data-theme="theme9"></li>
            <li id="theme10" data-theme="theme10"></li>
            <li id="theme11" data-theme="theme11"></li>
            <li id="theme12" data-theme="theme12"></li>
            <li id="theme13" data-theme="theme13"></li>
            <li id="theme14" data-theme="theme14"></li>
            <li id="theme15" data-theme="theme15"></li>
        </ul>

        <div class="mt-3">
            <button class="btn btn-sm btn-primary w-100" onclick="resetTheme()">
                <i class="ri-refresh-line me-1"></i>Reset Theme
            </button>
        </div>
    </div>
</div>

<style>
.right-sidebar {
    position: fixed;
    top: 50%;
    right: -250px;
    width: 250px;
    height: auto;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 8px 0 0 8px;
    transition: right 0.3s ease;
    z-index: 1050;
    transform: translateY(-50%);
}

.right-sidebar.active {
    right: 0;
}

.switcher-icon {
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--vz-primary);
    color: white;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px 0 0 8px;
    cursor: pointer;
    font-size: 18px;
}

.right-sidebar-content {
    padding: 20px;
}

.switcher {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.switcher li {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid #ddd;
    transition: all 0.3s ease;
}

.switcher li:hover {
    transform: scale(1.1);
    border-color: var(--vz-primary);
}

/* Theme Colors */
#theme1 { background: linear-gradient(45deg, #667eea 0%, #764ba2 100%); }
#theme2 { background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%); }
#theme3 { background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%); }
#theme4 { background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%); }
#theme5 { background: linear-gradient(45deg, #fa709a 0%, #fee140 100%); }
#theme6 { background: linear-gradient(45deg, #a8edea 0%, #fed6e3 100%); }
#theme7 { background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%); }
#theme8 { background: linear-gradient(45deg, #a18cd1 0%, #fbc2eb 100%); }
#theme9 { background: linear-gradient(45deg, #fad0c4 0%, #ffd1ff 100%); }
#theme10 { background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%); }
#theme11 { background: linear-gradient(45deg, #ff8a80 0%, #ffb74d 100%); }
#theme12 { background: linear-gradient(45deg, #81c784 0%, #aed581 100%); }
#theme13 { background: linear-gradient(45deg, #64b5f6 0%, #42a5f5 100%); }
#theme14 { background: linear-gradient(45deg, #ba68c8 0%, #ab47bc 100%); }
#theme15 { background: linear-gradient(45deg, #ffb74d 0%, #ff8a65 100%); }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const switcher = document.querySelector('.right-sidebar');
    const switcherIcon = document.querySelector('.switcher-icon');
    
    switcherIcon.addEventListener('click', function() {
        switcher.classList.toggle('active');
    });
    
    // Close when clicking outside
    document.addEventListener('click', function(e) {
        if (!switcher.contains(e.target)) {
            switcher.classList.remove('active');
        }
    });
    
    // Theme switching functionality
    document.querySelectorAll('.switcher li').forEach(function(item) {
        item.addEventListener('click', function() {
            const theme = this.getAttribute('data-theme');
            if (theme) {
                document.body.className = theme;
                localStorage.setItem('selectedTheme', theme);
            }
        });
    });
    
    // Load saved theme
    const savedTheme = localStorage.getItem('selectedTheme');
    if (savedTheme) {
        document.body.className = savedTheme;
    }
});

function resetTheme() {
    document.body.className = '';
    localStorage.removeItem('selectedTheme');
}
</script>
