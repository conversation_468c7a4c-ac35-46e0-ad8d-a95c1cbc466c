/*** Spinner Start ***/

#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .8s ease-out, visibility 0s linear .5s;
    z-index: 99999;
 }

 #spinner.show {
     transition: opacity .8s ease-out, visibility 0s linear .0s;
     visibility: visible;
     opacity: 1;
 }

/*** Spinner End ***/


/*** Button Start ***/
.btn {
    font-weight: 600;
    transition: .5s;
}

.btn-square {
    width: 38px;
    height: 38px;
}

.btn-sm-square {
    width: 32px;
    height: 32px;
}

.btn-md-square {
    width: 46px;
    height: 46px;
}

.btn-lg-square {
    width: 58px;
    height: 58px;
}

.btn-square,
.btn-sm-square,
.btn-md-square,
.btn-lg-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
}

.back-to-top {
    position: fixed;
    width: 50px;
    height: 50px;
    right: 30px;
    bottom: 30px;
    z-index: 99;
}

/*** <PERSON><PERSON> End ***/


/*** Topbar Start ***/

.topbar .top-info {
    letter-spacing: 1px;
}

.topbar .top-link {
    display: flex;
    align-items: center;
    justify-content: center;
}

.topbar .top-link a {
    margin-right: 10px;
}

#note {
    width: 500px;
    overflow: hidden;
}

#note small {
    position: relative;
    display: inline-block;
    animation: mymove 5s infinite;
    animation-timing-function: all;
}

@keyframes mymove {
    from {left: -100%;}
    to {left: 100%;}
}

/*** Topbar End ***/


/*** Grand-Style Frontend Navbar ***/

/* CSS Variables for Frontend Theme - Matching Admin */
:root {
  /* Primary Colors - Grand Theme */
  --grand-primary: #556ee6;
  --grand-primary-rgb: 85, 110, 230;
  --grand-secondary: #6c757d;
  --grand-success: #34c38f;
  --grand-info: #50a5f1;
  --grand-warning: #f1b44c;
  --grand-danger: #f46a6a;
  --grand-light: #f8f9fa;
  --grand-dark: #343a40;

  /* Background Colors - Grand Style */
  --grand-body-bg: #ffffff;
  --grand-body-color: #495057;
  --grand-card-bg: #ffffff;
  --grand-border-color: #eff2f7;

  /* Navbar - Light Theme */
  --grand-navbar-bg: #ffffff;
  --grand-navbar-color: #495057;
  --grand-navbar-hover-color: #556ee6;
  --grand-navbar-active-color: #556ee6;
  --grand-navbar-border: #eff2f7;
  --grand-navbar-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);

  /* Fonts - Grand Typography */
  --grand-font-sans-serif: "Poppins", sans-serif;
  --grand-font-size-base: 0.8125rem;
  --grand-font-weight-medium: 500;
  --grand-font-weight-semibold: 600;
}

/* Dark Theme Variables - Grand Style (Black like footer) */
[data-bs-theme="dark"] {
  --grand-body-bg: #000000;
  --grand-body-color: #ffffff;
  --grand-card-bg: #1a1a1a;
  --grand-border-color: #333333;
  --grand-navbar-bg: #000000;
  --grand-navbar-color: #ffffff;
  --grand-navbar-hover-color: #26d48c;
  --grand-navbar-active-color: #26d48c;
  --grand-navbar-border: #333333;
  --grand-navbar-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Apply Grand Theme to Body */
body {
    font-family: var(--grand-font-sans-serif), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background-color: var(--grand-body-bg);
    color: var(--grand-body-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Mode Global Styles */
[data-bs-theme="dark"] {
    color-scheme: dark;
}

[data-bs-theme="dark"] .container-fluid,
[data-bs-theme="dark"] .container {
    background-color: transparent;
}

[data-bs-theme="dark"] .card,
[data-bs-theme="dark"] .bg-light {
    background-color: var(--grand-card-bg) !important;
    color: var(--grand-body-color) !important;
    border-color: var(--grand-border-color) !important;
}

[data-bs-theme="dark"] .text-dark {
    color: var(--grand-body-color) !important;
}

[data-bs-theme="dark"] .text-muted {
    color: #888888 !important;
}

[data-bs-theme="dark"] .border {
    border-color: var(--grand-border-color) !important;
}

/* Dark Mode Button Styles */
[data-bs-theme="dark"] .btn-primary {
    background-color: var(--grand-primary);
    border-color: var(--grand-primary);
}

[data-bs-theme="dark"] .btn-secondary {
    background-color: #26d48c;
    border-color: #26d48c;
    color: #000000;
}

[data-bs-theme="dark"] .btn-outline-light {
    border-color: var(--grand-border-color);
    color: var(--grand-body-color);
}

[data-bs-theme="dark"] .btn-outline-light:hover {
    background-color: var(--grand-primary);
    border-color: var(--grand-primary);
    color: #ffffff;
}

/* Navbar Base Styles - Grand Theme */
.grand-navbar {
    background: var(--grand-navbar-bg) !important;
    border-bottom: 1px solid var(--grand-navbar-border);
    box-shadow: var(--grand-navbar-shadow);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    padding: 0;
    height: 70px;
    position: relative;
    overflow: hidden;
}

.grand-navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(85, 110, 230, 0.05), transparent);
    transition: left 8s ease-in-out;
    z-index: -1;
}

.grand-navbar:hover::before {
    left: 100%;
}

.grand-navbar.scrolled {
    box-shadow: 0 8px 32px rgba(15, 34, 58, 0.2);
    background: var(--grand-navbar-bg) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(85, 110, 230, 0.2);
}

/* Brand/Logo Styles - Grand Theme */
.navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none !important;
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-brand:hover {
    transform: scale(1.02);
}

.navbar-logo {
    border-radius: 0.5rem;
    transition: transform 0.3s ease;
    object-fit: contain;
}

.brand-text {
    font-size: 1.375rem;
    font-weight: var(--grand-font-weight-semibold);
    color: var(--grand-navbar-color);
    letter-spacing: -0.025em;
    margin-left: 0.75rem;
}

.brand-accent {
    color: var(--grand-primary);
}

@media (max-width: 991.98px) {
    .brand-text {
        font-size: 1.25rem;
    }
}

/* Navigation Links - Grand Theme */
.navbar-nav {
    padding: 0;
    align-items: center;
}

.navbar-nav .nav-link {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    color: var(--grand-navbar-color) !important;
    font-size: var(--grand-font-size-base);
    font-weight: var(--grand-font-weight-medium);
    text-decoration: none;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    height: 42px;
    overflow: hidden;
    transform: translateZ(0);
    will-change: transform, background-color, color;
}

/* Advanced hover effects */
.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(85, 110, 230, 0.1), transparent);
    transition: left 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: -1;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--grand-primary), var(--bs-secondary));
    transform: translateX(-50%);
    transition: width 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    border-radius: 1px;
}

.navbar-nav .nav-link:hover {
    color: var(--grand-navbar-hover-color) !important;
    background-color: rgba(85, 110, 230, 0.15);
    transform: translateY(-2px) scale(1.02) translateZ(0);
    box-shadow: 0 8px 25px rgba(85, 110, 230, 0.2);
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

.navbar-nav .nav-link.active {
    color: var(--grand-navbar-active-color) !important;
    background: linear-gradient(135deg, rgba(85, 110, 230, 0.2), rgba(85, 110, 230, 0.1));
    font-weight: var(--grand-font-weight-semibold);
    box-shadow: 0 4px 15px rgba(85, 110, 230, 0.3);
    transform: translateY(-1px) translateZ(0);
    border: 1px solid rgba(85, 110, 230, 0.2);
}

.navbar-nav .nav-link.active::after {
    width: 90% !important;
    background: linear-gradient(90deg, var(--grand-primary), var(--bs-secondary));
    height: 3px;
    animation: activeNavPulse 2s ease-in-out infinite;
}

/* Active navigation pulse animation */
@keyframes activeNavPulse {
    0%, 100% {
        opacity: 1;
        transform: translateX(-50%) scaleX(1);
    }
    50% {
        opacity: 0.7;
        transform: translateX(-50%) scaleX(0.95);
    }
}

.navbar-nav .nav-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.navbar-nav .nav-link:hover i {
    transform: scale(1.1) translateZ(0);
}

/* Click Animation and Ripple Effect */
.navbar-nav .nav-link {
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link:active {
    transform: translateY(0) scale(0.98) translateZ(0);
    transition: transform 0.1s ease;
}

/* Ripple effect on click */
.nav-link-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(85, 110, 230, 0.3);
    transform: scale(0);
    animation: navRipple 0.6s linear;
    pointer-events: none;
}

@keyframes navRipple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced focus states */
.navbar-nav .nav-link:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(85, 110, 230, 0.3);
    background-color: rgba(85, 110, 230, 0.1);
}

/* Navbar brand hover effects */
.navbar-brand:hover .navbar-logo {
    transform: rotate(5deg) scale(1.05);
}

.navbar-brand:hover .brand-text {
    color: var(--grand-primary);
    text-shadow: 0 2px 8px rgba(85, 110, 230, 0.3);
}

/* Smooth Scrolling */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* Dropdown Styles */
.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    vertical-align: middle;
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.navbar .dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg);
}

.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    margin-top: 8px;
    min-width: 280px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dropdown-item {
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    color: var(--bs-dark);
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 8px;
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background-color: var(--bs-primary);
    color: white;
    transform: translateX(5px);
}

.dropdown-item.disabled {
    color: var(--bs-gray);
    background-color: transparent;
    cursor: not-allowed;
}

.dropdown-item.disabled:hover {
    background-color: transparent;
    transform: none;
}

.dropdown-divider {
    margin: 8px 16px;
    border-color: rgba(0, 0, 0, 0.1);
}

/* Desktop Dropdown Animation */
@media (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    .navbar .nav-item:hover .dropdown-menu {
        visibility: visible;
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Search Box Styles - Grand Theme */
.navbar-search-container {
    min-width: 300px;
}

.navbar-search-input {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(15, 34, 58, 0.1);
    transition: all 0.3s ease;
}

.navbar-search-input:focus-within {
    box-shadow: 0 4px 12px rgba(85, 110, 230, 0.2);
    transform: translateY(-1px);
}

.navbar-search-input .form-control {
    border: 1px solid var(--grand-border-color);
    background: var(--grand-card-bg);
    color: var(--grand-text-color);
    font-size: 0.875rem;
}

.navbar-search-input .form-control:focus {
    border-color: var(--grand-primary);
    box-shadow: none;
}

.navbar-search-input .input-group-text {
    border: 1px solid var(--grand-border-color);
    background: var(--grand-card-bg);
}

.search-submit-btn {
    border-radius: 0;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.navbar-search-input.focused {
    box-shadow: 0 4px 12px rgba(85, 110, 230, 0.3);
    transform: translateY(-1px);
}

/* Desktop search positioning */
@media (min-width: 992px) {
    .navbar-search-container {
        margin-right: 2rem;
    }
}

/* Mobile Navigation Styles - Grand Theme */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: var(--grand-navbar-bg);
        border: 1px solid var(--grand-navbar-border);
        border-radius: 0.5rem;
        margin-top: 1rem;
        padding: 1rem;
        box-shadow: var(--grand-navbar-shadow);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Mobile hamburger positioning */
    .navbar-toggler {
        order: 0 !important;
        margin-right: 0.5rem !important;
    }

    .navbar-brand {
        order: 1 !important;
    }

    .navbar-collapse {
        order: 2 !important;
    }

    /* Mobile search styling */
    .navbar-search-container {
        min-width: 100%;
        margin-bottom: 1rem;
        order: 0;
    }

    /* Horizontal mobile menu */
    .mobile-horizontal {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    .mobile-horizontal .nav-item {
        flex: 0 0 auto;
    }

    .mobile-horizontal .nav-link {
        padding: 0.5rem 0.75rem !important;
        margin: 0.25rem !important;
        border-radius: 0.375rem;
        white-space: nowrap;
        font-size: 0.875rem;
        min-width: auto;
        width: auto !important;
        justify-content: center;
    }

    .dropdown-menu {
        background: var(--grand-card-bg);
        border: 1px solid var(--grand-border-color);
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        box-shadow: var(--grand-navbar-shadow);
    }

    /* Show theme toggle on mobile */
    .header-item {
        display: flex !important;
        justify-content: center;
        margin: 1rem 0;
    }
}

/* Mobile Toggle Button - Grand Theme */
.grand-hamburger {
    background: transparent;
    border: none;
    color: var(--grand-navbar-color);
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.grand-hamburger::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(85, 110, 230, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: -1;
}

.grand-hamburger:hover {
    background-color: rgba(85, 110, 230, 0.15);
    color: var(--grand-primary);
    transform: scale(1.1) translateZ(0);
    box-shadow: 0 4px 15px rgba(85, 110, 230, 0.3);
}

.grand-hamburger:hover::before {
    width: 60px;
    height: 60px;
}

.grand-hamburger:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(85, 110, 230, 0.4);
}

.grand-hamburger:active {
    transform: scale(0.95) translateZ(0);
}

.hamburger-icon {
    display: flex;
    flex-direction: column;
    width: 18px;
    height: 14px;
    justify-content: space-between;
}

.hamburger-icon span {
    height: 2px;
    background: currentColor;
    border-radius: 1px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.grand-hamburger:hover .hamburger-icon span {
    background: var(--grand-primary);
    box-shadow: 0 0 8px rgba(85, 110, 230, 0.4);
}

.grand-hamburger.active {
    background: linear-gradient(135deg, rgba(85, 110, 230, 0.2), rgba(85, 110, 230, 0.1));
    box-shadow: 0 0 20px rgba(85, 110, 230, 0.4);
}

.grand-hamburger.active .hamburger-icon span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
    background: var(--grand-primary);
}

.grand-hamburger.active .hamburger-icon span:nth-child(2) {
    opacity: 0;
    transform: translateX(-20px) scale(0);
}

.grand-hamburger.active .hamburger-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
    background: var(--grand-primary);
}

/* Header Items - Grand Theme */
.header-item {
    margin-left: 0.5rem;
}

.btn-topbar {
    width: 42px;
    height: 42px;
    border-radius: 0.5rem;
    border: none;
    background: transparent;
    color: var(--grand-navbar-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
}

.btn-topbar:hover {
    background-color: rgba(85, 110, 230, 0.1);
    color: var(--grand-primary);
    transform: translateY(-1px);
}

.btn-topbar:focus {
    box-shadow: 0 0 0 0.2rem rgba(85, 110, 230, 0.25);
}

/* Theme Toggle Specific Styles */
.grand-theme-toggle {
    position: relative;
}

.grand-theme-toggle i {
    transition: all 0.3s ease;
}

[data-bs-theme="dark"] .grand-theme-toggle i.ri-sun-line {
    transform: rotate(180deg);
}

/* Dark Mode Modal Styles */
[data-bs-theme="dark"] .modal-content {
    background-color: var(--grand-card-bg);
    border: 1px solid var(--grand-border-color);
    color: var(--grand-body-color);
}

[data-bs-theme="dark"] .modal-header {
    border-bottom-color: var(--grand-border-color);
}

[data-bs-theme="dark"] .modal-title {
    color: var(--grand-body-color);
}

[data-bs-theme="dark"] .btn-close {
    filter: invert(1);
}

[data-bs-theme="dark"] .form-control {
    background-color: #2a2a2a;
    border-color: var(--grand-border-color);
    color: var(--grand-body-color);
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #2a2a2a;
    border-color: var(--grand-primary);
    color: var(--grand-body-color);
    box-shadow: 0 0 0 0.2rem rgba(85, 110, 230, 0.25);
}

[data-bs-theme="dark"] .form-control::placeholder {
    color: #888888;
}

/* Dark Mode Dropdown Styles */
[data-bs-theme="dark"] .dropdown-menu {
    background-color: var(--grand-card-bg);
    border-color: var(--grand-border-color);
}

[data-bs-theme="dark"] .dropdown-item {
    color: var(--grand-body-color);
}

[data-bs-theme="dark"] .dropdown-item:hover {
    background-color: var(--grand-primary);
    color: #ffffff;
}

[data-bs-theme="dark"] .dropdown-item.disabled {
    color: #666666;
}

[data-bs-theme="dark"] .dropdown-divider {
    border-color: var(--grand-border-color);
}

/* Dark Mode Badge Styles */
[data-bs-theme="dark"] .badge.bg-secondary {
    background-color: #555555 !important;
    color: #ffffff;
}



/* Dark Mode Section Backgrounds */
[data-bs-theme="dark"] .bg-primary {
    background-color: var(--grand-primary) !important;
}

[data-bs-theme="dark"] .container-fluid:not(.footer):not(.bg-primary):not(.bg-dark) {
    background-color: var(--grand-body-bg) !important;
}

/* Dark Mode Text Colors */
[data-bs-theme="dark"] h1,
[data-bs-theme="dark"] h2,
[data-bs-theme="dark"] h3,
[data-bs-theme="dark"] h4,
[data-bs-theme="dark"] h5,
[data-bs-theme="dark"] h6 {
    color: var(--grand-body-color) !important;
}

[data-bs-theme="dark"] p {
    color: var(--grand-body-color);
}

[data-bs-theme="dark"] .text-white {
    color: var(--grand-body-color) !important;
}

/* Dark Mode Link Colors */
[data-bs-theme="dark"] a:not(.btn):not(.navbar-brand):not(.nav-link) {
    color: #26d48c;
}

[data-bs-theme="dark"] a:not(.btn):not(.navbar-brand):not(.nav-link):hover {
    color: #1ea06b;
}

/* Enhanced Footer Styles */
.grand-footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%) !important;
    border-top: 1px solid rgba(85, 110, 230, 0.2);
    position: relative;
    overflow: hidden;
}

.grand-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(85, 110, 230, 0.05) 0%, transparent 70%);
    z-index: 0;
}

.grand-footer .container {
    position: relative;
    z-index: 1;
}

/* Dark theme footer adjustments */
[data-bs-theme="dark"] .grand-footer {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%) !important;
    border-top: 1px solid rgba(85, 110, 230, 0.3);
}

[data-bs-theme="dark"] .grand-footer::before {
    background: radial-gradient(ellipse at top, rgba(85, 110, 230, 0.08) 0%, transparent 70%);
}

/* Light theme footer */
[data-bs-theme="light"] .grand-footer {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border-top: 1px solid rgba(85, 110, 230, 0.2);
}

[data-bs-theme="light"] .grand-footer::before {
    background: radial-gradient(ellipse at top, rgba(85, 110, 230, 0.03) 0%, transparent 70%);
}

/* Dark Mode Page Header */
[data-bs-theme="dark"] .page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(../img/carousel-1.jpg) center center no-repeat !important;
}

/* Badge Styles */
.badge {
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 12px;
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.dropdown-item:focus,
.search-btn:focus {
    outline: 2px solid var(--bs-secondary);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .navbar-nav .nav-link {
        border: 1px solid transparent;
    }

    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        border-color: var(--bs-secondary);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .navbar,
    .navbar-brand,
    .navbar-logo,
    .nav-link,
    .dropdown-toggle::after,
    .dropdown-menu,
    .dropdown-item,
    .search-btn {
        transition: none;
    }
}

/*** Enhanced Navbar End ***/


/*** Page Header Start ***/

.page-header {
    background: linear-gradient(rgba(0, 0, 0, .6), rgba(0, 0, 0, .6)), url(../img/carousel-1.jpg) center center no-repeat;
    background-size: cover;
}

.page-header .breadcrumb-item+.breadcrumb-item::before {
    color: var(--bs-white);
}

.page-header .breadcrumb-item,
.page-header .breadcrumb-item a {
    font-size: 18px;
    color: var(--bs-white);
}

/*** Page Header End ***/


/*** Services Start ***/

.services .services-item {
    box-shadow: 0 0 60px rgba(0, 0, 0, .2);
    width: 100%;
    height: 100%;
    border-radius: 10px;
    padding: 10px 0;
    position: relative;
}


.services-content::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    border-radius: 10px 10px 0 0;
    background: rgba(3, 43, 243, 0.8);
    transition: .5s;
}

.services-content::after {
    top: 0;
    bottom: auto;
    border-radius: 10px 10px 10px 10px;
}

.services-item:hover .services-content::after {
    height: 100%;
    opacity: 1;
    transition: .5s;
}

.services-item:hover .services-content-icon {
    position: relative;
    z-index: 2;
}

.services-item .services-content-icon i,
.services-item .services-content-icon p {
    transition: .5s;
}

.services-item:hover .services-content-icon i {
    color: var(--bs-secondary) !important;
}

.services-item:hover .services-content-icon p {
    color: var(--bs-white);
}

/*** Services End ***/


/*** Project Start ***/

.project-img {
    position: relative;
    padding: 15px;
}

.project-img::before {
    content: "";
    position: absolute;
    width: 150px;
    height: 150px;
    top: 0;
    left: 0;
    background: var(--bs-secondary);
    border-radius: 10px;
    opacity: 1;
    z-index: -1;
    transition: .5s;
}

.project-img::after {
    content: "";
    width: 150px;
    height: 150px;
    position: absolute;
    right: 0;
    bottom: 0;
    background: var(--bs-primary);
    border-radius: 10px;
    opacity: 1;
    z-index: -1;
    transition: .5s;
}

.project-content {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
}

.project-content a {
    display: inline-block;
    padding: 20px 25px;
    background: var(--bs-primary);
    border-radius: 10px;
}

.project-item:hover .project-content {
    opacity: 1;
    transition: .5s;
}

.project-item:hover .project-img::before,
.project-item:hover .project-img::after {
    opacity: 0;
}

/*** Project End ***/


/*** Blog Start ***/
.blog-item .blog-btn {
    z-index: 2;
}

.blog-btn .blog-btn-icon {
    height: 50px;
    position: relative;
    overflow: hidden;
}

.blog-btn-icon .blog-icon-2 {
    display: flex;
    position: absolute;
    top: 6px;
    left: -140px;

}

.blog-btn-icon:hover .blog-icon-2 {
    transition: 1s;
    left: 5px;
    top: 6px;
    padding-bottom: 5px;
}
.blog-icon-1 {
    position: relative;
    top: -4px;
}
.blog-btn-icon:hover .blog-icon-1 {
    top: 0;
    right: -140px;
    transition: 1s;
}

/*** Blog End ***/


/*** Team Start ***/

.team-item {
    border-top: 30px solid var(--bs-secondary) !important;
    background: rgba(239, 239, 241, 0.8);
}

.team-content::before {
    height: 200px;
    display: block;
    content: "";
    position: relative;
    top: -101px;
    background: var(--bs-secondary);
    clip-path: polygon(50% 50%, 100% 50%, 50% 100%, 0% 50%);
    padding: 60px;
    opacity: 1;
}

.team-img-icon {
    position: relative;
    margin-top: -200px;
    padding: 30px;
    padding-bottom: 0;
}

.team-img {
    border: 15px solid var(--bs-white);
}

.team-img img {
    border: 10px solid var(--bs-secondary);
    transition: .5s;
}

.team-item:hover h4 {
    color: var(--bs-primary);
    transition: .5s;
}

.team-item:hover .team-img img {
    transform: scale(1.05);
    border: 10px solid var(--bs-secondary);
}

.team-carousel .owl-stage {
    position: relative;
    width: 100%;
    height: 100%;
}

.team-carousel .owl-nav {
    position: absolute;
    top: -100px;
    right: 50px;
    display: flex;
}

.team-carousel .owl-nav .owl-prev,
.team-carousel .owl-nav .owl-next {
    width: 56px;
    height: 56px;
    border-radius: 56px;
    margin-left: 15px;
    background: var(--bs-secondary);
    color: var(--bs-white);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: .5s;
}

.team-carousel .owl-nav .owl-prev:hover,
.team-carousel .owl-nav .owl-next:hover {
    background: var(--bs-primary);
    color: var(--bs-white);
}

@media (max-width: 992px) {
    .team-carousel {
        margin-top: 3rem;
    }

    .team-carousel .owl-nav {
        top: -85px;
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        margin-left: -15px;
    }
}

/*** Team End ***/


/*** Testimonial Start ***/

.testimonial-item {
    background: #e3f0eb;

}

.testimonial-carousel .owl-dots {
    margin-top: 15px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.testimonial-carousel .owl-dot {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    width: 15px;
    height: 15px;
    background: #c1dad0;
    border-radius: 15px;
    transition: .5s;
}

.testimonial-carousel .owl-dot.active {
    width: 30px;
    background: var(--bs-primary);
}

.testimonial-carousel .owl-item.center {
    position: relative;
    z-index: 1;
}

.testimonial-carousel .owl-item .testimonial-item {
    transition: .5s;
}

.testimonial-carousel .owl-item.center .testimonial-item {
    background: #FFFFFF !important;
    box-shadow: 0 0 30px #DDDDDD;
}

/*** Testimonial End ***/


/*** Contact Start ***/
.contact-detail::before {
    position: absolute;
    content: "";
    height: 50%;
    width: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(rgb(210, 243, 235, 1), rgba(230, 250, 245, .3)), url(../img/background.jpg) center center no-repeat;
    background-size: cover;
    border-radius: 10px;
    z-index: -1;
}

.contact-map {
    background: #26d48c;
}

.contact-form {
    background: #26d48c;
}

/*** Contact End ***/


/*** Enhanced Footer Styles ***/

.grand-footer .short-link a,
.grand-footer .help-link a,
.grand-footer .contact-link a {
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    display: inline-block;
    padding: 0.25rem 0;
}

.grand-footer .short-link a::before,
.grand-footer .help-link a::before,
.grand-footer .contact-link a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--bs-secondary), var(--grand-primary));
    transition: width 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.grand-footer .short-link a:hover,
.grand-footer .help-link a:hover,
.grand-footer .contact-link a:hover {
    letter-spacing: 1px;
    color: var(--bs-secondary) !important;
    transform: translateX(5px);
}

.grand-footer .short-link a:hover::before,
.grand-footer .help-link a:hover::before,
.grand-footer .contact-link a:hover::before {
    width: 100%;
}

/* Enhanced social media buttons */
.grand-footer .hightech-link a {
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.grand-footer .hightech-link a::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: 0;
}

.grand-footer .hightech-link a:hover {
    background: var(--bs-secondary) !important;
    border: 0;
    transform: translateY(-3px) scale(1.1) translateZ(0);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.grand-footer .hightech-link a:hover::before {
    width: 60px;
    height: 60px;
}

.grand-footer .hightech-link a i {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.grand-footer .hightech-link a:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* Footer brand animation */
.grand-footer h1 {
    transition: all 0.3s ease;
}

.grand-footer h1:hover {
    transform: scale(1.02);
    text-shadow: 0 0 20px rgba(85, 110, 230, 0.3);
}

/* Footer section headings */
.grand-footer .h3 {
    position: relative;
    transition: color 0.3s ease;
}

.grand-footer .h3:hover {
    color: var(--bs-secondary) !important;
}

/* Enhanced footer text animations */
.grand-footer p {
    transition: color 0.3s ease;
}

.grand-footer:hover p {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Dark theme specific footer enhancements */
[data-bs-theme="dark"] .grand-footer .text-light,
[data-bs-theme="dark"] .grand-footer .text-white {
    color: rgba(255, 255, 255, 0.9) !important;
}

[data-bs-theme="dark"] .grand-footer .text-secondary {
    color: var(--bs-secondary) !important;
}

/* Light theme specific footer enhancements */
[data-bs-theme="light"] .grand-footer .text-light,
[data-bs-theme="light"] .grand-footer .text-white {
    color: #333333 !important;
}

[data-bs-theme="light"] .grand-footer h1 {
    color: #333333 !important;
}

[data-bs-theme="light"] .grand-footer h1 .text-secondary {
    color: var(--bs-secondary) !important;
}

[data-bs-theme="light"] .grand-footer p {
    color: #666666 !important;
}

[data-bs-theme="light"] .grand-footer .h3 {
    color: #333333 !important;
}

[data-bs-theme="light"] .grand-footer a:not(.btn) {
    color: #555555 !important;
}

[data-bs-theme="light"] .grand-footer a:not(.btn):hover {
    color: var(--grand-primary) !important;
}

[data-bs-theme="light"] .grand-footer .border-primary {
    border-color: rgba(85, 110, 230, 0.2) !important;
}

[data-bs-theme="light"] .grand-footer hr {
    border-color: rgba(85, 110, 230, 0.2) !important;
}

/*** Footer End ***/
