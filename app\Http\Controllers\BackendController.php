<?php

namespace App\Http\Controllers;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Setting;
use App\Models\Company;
use App\Models\Sliders;
use App\Models\Services;
use App\Models\Aboutus;
use App\Models\Messages;
use App\Models\Project;
use App\Models\Team;
use App\Models\Testimonials;


class BackendController extends Controller
{
    public function adminProfile(){
        $id= Auth::user()->id;
        $profileData=User::find($id);
        return view('backend.pages.admin_profile',compact('profileData'));
    }
    public function siteSetting(){
        $sitesetting = Setting::orderBy('created_at', 'desc')->first();

        return view('backend.pages.site',compact('sitesetting'));
    }
    public function adminSliders(){
        return view('backend.pages.sliders');
    }
    public function adminServices(){
        return view('backend.pages.services');
    }
    public function adminProjects(){
        return view('backend.pages.projects');
    }
    public function adminTestimonials(){
        return view('backend.pages.testimonials');
    }
    public function adminTeams(){
        return view('backend.pages.teams');
    }
    public function adminMessages(){
        return view('backend.pages.messages');
    }

    public function adminAllServices(){
        return view('backend.pages.allservices');
    }
    public function adminAllProjects(){
        return view('backend.pages.allprojects');
    }
    public function adminAllTestimonial(){
        return view('backend.pages.alltestimonials');
    }
    public function adminAllTeams(){
        return view('backend.pages.allteams');
    }
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function storeSettings(Request $request)
    {
        $validateddata = $request->validate([
            'location' => 'required|string|max:255',
            'email' => 'required|string|max:100',
            'advert_slider' => 'required|string|max:100',
            'facebook' => 'required|string|max:150',
            'twitter' => 'required|string|max:150',
            'instagram' => 'required|string|max:150',
            'linkedin' => 'required|string|max:150',
        ]);

        $sitesetting = new Setting();
        $sitesetting->location = $validateddata['location'];
        $sitesetting->email = $validateddata['email'];
        $sitesetting->slider_advert = $validateddata['advert_slider'];
        $sitesetting->facebook = $validateddata['facebook'];
        $sitesetting->twitter = $validateddata['twitter'];
        $sitesetting->instagram = $validateddata['instagram'];
        $sitesetting->linkedin = $validateddata['linkedin'];


        $sitesetting->save();

        return view('backend.pages.site');
    }

    public function storeCompanyinfo(Request $request)
    {
        $validateddata = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:100',


        ]);


        $company = new Company();
        $company->name = $validateddata['name'];
        $company->phone = $validateddata['phone'];



        $company->save();

        return view('backend.pages.site');
    }

    public function storeSliders(Request $request)
    {
        $validatedData = $request->validate([
            'first_slogan_one' => 'required|string|max:255',
            'second_slogan_one' => 'required|string|max:100',
            'slider_one_img' => 'required|file|max:100',
            'first_slogan_two' => 'required|string|max:150',
            'second_slogan_two' => 'required|string|max:150',
            'slider_two_img' => 'required|file|max:150',
        ]);


        $sliders = new Sliders();
        $sliders->first_slogan_one = $validatedData['first_slogan_one'];
        $sliders->second_slogan_one = $validatedData['second_slogan_one'];
        $sliders->slider_one_img = $request->file('slider_one_img')->store('public/slider_images');
        $sliders->first_slogan_two = $validatedData['first_slogan_two'];
        $sliders->second_slogan_two = $validatedData['second_slogan_two'];
        $sliders->slogan_two_img = $request->file('slider_two_img')->store('public/slider_images');

        $sliders->save();

        return view('backend.pages.sliders');
    }


    public function storeAboutus(Request $request)
    {
        $validatedData = $request->validate([
            'aboutus_img_one' => 'required|string|max:255',
            'aboutus_img_two' => 'required|string|max:100',
            'header_tittle' => 'required|file|max:100',
            'company_description' => 'required|string|max:150',
        ]);


        $aboutus = new Aboutus();
        $aboutus->aboutus_img_one = $request->file('slider_one_img')->store('public/aboutus_img');
        $aboutus->aboutus_img_two = $request->file('slider_one_img')->store('public/aboutus_img');
        $aboutus->tittle = $validatedData['header_tittle'];
        $aboutus->description = $validatedData['company_description'];


        $aboutus->save();

        return view('sliders');
    }

    public function storeServices(Request $request)
    {
        $validatedData = $request->validate([
            'service_name' => 'required|string|max:255',
            'service_desc' => 'required|string|max:100',
            'service_img' => 'required|file|max:100',

        ]);




        $service = new Services();
        $service->name = $validatedData['service_name'];
        $service->Description = $validatedData['service_desc'];
        $service->service_image = $request->file('service_img')->store('public/services_img');

        $service->save();

        return view('backend.pages.services');
    }

    public function storeProjects(Request $request)
    {
        $validatedData = $request->validate([
            'project_name' => 'required|string|max:255',
            'project_description' => 'required|string|max:100',
            'project_image' => 'required|file|max:100',

        ]);




        $project = new Project();
        $project->name = $validatedData['project_name'];
        $project->description = $validatedData['project_description'];
        $project->project_image = $request->file('project_image')->store('public/project_img');

        $project->save();

        return view('backend.pages.projects');
    }


    public function storeTeam(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:100',
            'facebook' => 'required|string|max:100',
            'twitter' => 'required|string|max:100',
            'instagram' => 'required|string|max:100',
            'linkedin' => 'required|string|max:100',
            'image' => 'required|file|max:100',


        ]);

        $team = new Team();
        $team->name = $validatedData['name'];
        $team->designation = $validatedData['designation'];
        $team->facebook = $validatedData['facebook'];
        $team->twitter = $validatedData['twitter'];
        $team->instagaram = $validatedData['instagram'];
        $team->linkedin = $validatedData['linkedin'];
        $team->team_image = $request->file('image')->store('public/team');

        $team->save();

        return view('backend.pages.teams');
    }
    public function storeMessage(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|max:100',
            'project' => 'required|string|max:100',
            'message' => 'required|string|max:100',


        ]);

        $Messages = new Messages();
        $Messages->name = $validatedData['name'];
        $Messages->email = $validatedData['email'];
        $Messages->project = $validatedData['project'];
        $Messages->message = $validatedData['message'];
        $Messages->save();


        return view('frontend.index');


    }





}
