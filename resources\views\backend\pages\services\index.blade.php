@extends('backend.layouts.app')

@section('title', 'Services Management - GrandTek Admin')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Services Management</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Services</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">
                    <i class="ri-service-line me-2"></i>Services Management
                </h4>
                <div>
                    <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Add New Service
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($services->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>Service Name</th>
                                    <th>Description</th>
                                    <th>Icon/Image</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($services as $service)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>
                                        <strong>{{ $service->name }}</strong>
                                        @if($service->short_description)
                                            <br><small class="text-muted">{{ Str::limit($service->short_description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>{{ Str::limit($service->description, 80) }}</td>
                                    <td class="text-center">
                                        @if($service->service_image_url)
                                            <img src="{{ $service->service_image_url }}" alt="{{ $service->name }}" 
                                                 class="rounded" style="width: 40px; height: 40px; object-fit: cover;">
                                        @elseif($service->icon_class)
                                            <i class="{{ $service->icon_class }} text-primary fs-4"></i>
                                        @else
                                            <i class="ri-service-line text-muted fs-4"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $service->status === 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($service->status) }}
                                        </span>
                                    </td>
                                    <td>{{ $service->sort_order }}</td>
                                    <td>{{ $service->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.services.edit', $service->id) }}">
                                                        <i class="ri-edit-line me-1"></i>Edit
                                                    </a>
                                                </li>
                                                <li>
                                                    <button type="button" class="dropdown-item" 
                                                            onclick="toggleStatus({{ $service->id }})">
                                                        <i class="ri-toggle-line me-1"></i>
                                                        {{ $service->status === 'active' ? 'Deactivate' : 'Activate' }}
                                                    </button>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button type="button" class="dropdown-item text-danger" 
                                                            onclick="deleteService({{ $service->id }})">
                                                        <i class="ri-delete-bin-line me-1"></i>Delete
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="ri-service-line display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No Services Found</h5>
                        <p class="text-muted mb-4">Create your first service to get started.</p>
                        <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add New Service
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this service? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function toggleStatus(id) {
    fetch(`/admin/services/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating status');
    });
}

function deleteService(id) {
    document.getElementById('deleteForm').action = `/admin/services/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

@endsection
