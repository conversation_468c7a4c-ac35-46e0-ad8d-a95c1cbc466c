@extends('backend.layouts.app')

@section('title', 'Create New Project')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Create New Project</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('projects.index') }}">Projects</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Project Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">Project Information</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('projects.store') }}" method="POST" enctype="multipart/form-data" id="projectForm">
                    @csrf
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Title -->
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Project Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                               id="title" name="title" value="{{ old('title') }}" required>
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Slug -->
                                    <div class="mb-3">
                                        <label for="slug" class="form-label">URL Slug</label>
                                        <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                               id="slug" name="slug" value="{{ old('slug') }}">
                                        <div class="form-text">Leave empty to auto-generate from title</div>
                                        @error('slug')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Description -->
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Short Description <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                                  id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                                        <div class="form-text">Brief description for project cards (max 500 characters)</div>
                                        @error('description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Short Description -->
                                    <div class="mb-3">
                                        <label for="short_description" class="form-label">Card Description</label>
                                        <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                                  id="short_description" name="short_description" rows="2">{{ old('short_description') }}</textarea>
                                        <div class="form-text">Optional shorter description for project cards (max 300 characters)</div>
                                        @error('short_description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Full Description -->
                                    <div class="mb-3">
                                        <label for="full_description" class="form-label">Full Description</label>
                                        <textarea class="form-control @error('full_description') is-invalid @enderror" 
                                                  id="full_description" name="full_description" rows="8">{{ old('full_description') }}</textarea>
                                        <div class="form-text">Detailed project description for the project detail page</div>
                                        @error('full_description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Project Images -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Project Images</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Main Project Image -->
                                    <div class="mb-3">
                                        <label for="project_image" class="form-label">Main Project Image</label>
                                        <input type="file" class="form-control @error('project_image') is-invalid @enderror" 
                                               id="project_image" name="project_image" accept="image/*">
                                        <div class="form-text">Upload main project image (JPEG, PNG, JPG, GIF, WebP - Max: 5MB)</div>
                                        @error('project_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div id="project_image_preview" class="mt-2"></div>
                                    </div>

                                    <!-- Gallery Images -->
                                    <div class="mb-3">
                                        <label for="gallery_images" class="form-label">Gallery Images</label>
                                        <input type="file" class="form-control @error('gallery_images.*') is-invalid @enderror" 
                                               id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                        <div class="form-text">Upload multiple gallery images (JPEG, PNG, JPG, GIF, WebP - Max: 5MB each)</div>
                                        @error('gallery_images.*')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div id="gallery_images_preview" class="mt-2 row g-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Project Details Sidebar -->
                        <div class="col-lg-4">
                            <!-- Project Settings -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Project Settings</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                            <option value="active" {{ old('status') === 'active' ? 'selected' : '' }}>Active</option>
                                            <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            <option value="completed" {{ old('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="in_progress" {{ old('status') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Featured -->
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                                                   {{ old('featured') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="featured">
                                                Featured Project
                                            </label>
                                        </div>
                                        <div class="form-text">Featured projects appear on the homepage</div>
                                    </div>

                                    <!-- Category -->
                                    <div class="mb-3">
                                        <label for="category" class="form-label">Category</label>
                                        <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                               id="category" name="category" value="{{ old('category') }}">
                                        @error('category')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Client Name -->
                                    <div class="mb-3">
                                        <label for="client_name" class="form-label">Client Name</label>
                                        <input type="text" class="form-control @error('client_name') is-invalid @enderror" 
                                               id="client_name" name="client_name" value="{{ old('client_name') }}">
                                        @error('client_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Project URL -->
                                    <div class="mb-3">
                                        <label for="project_url" class="form-label">Project URL</label>
                                        <input type="url" class="form-control @error('project_url') is-invalid @enderror" 
                                               id="project_url" name="project_url" value="{{ old('project_url') }}">
                                        <div class="form-text">Live project URL (optional)</div>
                                        @error('project_url')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Project Timeline -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Project Timeline</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Start Date -->
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">Start Date</label>
                                        <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                               id="start_date" name="start_date" value="{{ old('start_date') }}">
                                        @error('start_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- End Date -->
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">End Date</label>
                                        <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                               id="end_date" name="end_date" value="{{ old('end_date') }}">
                                        <div class="form-text">Leave empty if project is ongoing</div>
                                        @error('end_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Technologies -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Technologies Used</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="technologies_input" class="form-label">Technologies</label>
                                        <input type="text" class="form-control" id="technologies_input" 
                                               placeholder="Type technology and press Enter">
                                        <div class="form-text">Add technologies one by one</div>
                                        <div id="technologies_list" class="mt-2"></div>
                                        <input type="hidden" name="technologies" id="technologies_hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Settings -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <!-- Meta Title -->
                                            <div class="mb-3">
                                                <label for="meta_title" class="form-label">Meta Title</label>
                                                <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                                                       id="meta_title" name="meta_title" value="{{ old('meta_title') }}">
                                                <div class="form-text">Leave empty to use project title</div>
                                                @error('meta_title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Meta Keywords -->
                                            <div class="mb-3">
                                                <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                                <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror" 
                                                       id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords') }}">
                                                <div class="form-text">Comma-separated keywords</div>
                                                @error('meta_keywords')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <!-- Meta Description -->
                                            <div class="mb-3">
                                                <label for="meta_description" class="form-label">Meta Description</label>
                                                <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                                          id="meta_description" name="meta_description" rows="4">{{ old('meta_description') }}</textarea>
                                                <div class="form-text">Leave empty to use short description</div>
                                                @error('meta_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('projects.index') }}" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-1"></i>Back to Projects
                                </a>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Create Project
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.technology-tag {
    display: inline-block;
    background: var(--vz-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin: 0.125rem;
    font-size: 0.875rem;
}

.technology-tag .remove-tech {
    margin-left: 0.5rem;
    cursor: pointer;
    opacity: 0.8;
}

.technology-tag .remove-tech:hover {
    opacity: 1;
}

.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.gallery-preview-item {
    position: relative;
    display: inline-block;
}

.gallery-preview-item .remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from title
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');

    titleInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.dataset.autoGenerated) {
            const slug = this.value
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugInput.value = slug;
            slugInput.dataset.autoGenerated = 'true';
        }
    });

    slugInput.addEventListener('input', function() {
        if (this.value) {
            this.dataset.autoGenerated = 'false';
        }
    });

    // Technologies management
    const technologiesInput = document.getElementById('technologies_input');
    const technologiesList = document.getElementById('technologies_list');
    const technologiesHidden = document.getElementById('technologies_hidden');
    let technologies = [];

    technologiesInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const tech = this.value.trim();
            if (tech && !technologies.includes(tech)) {
                technologies.push(tech);
                updateTechnologiesDisplay();
                this.value = '';
            }
        }
    });

    function updateTechnologiesDisplay() {
        technologiesList.innerHTML = technologies.map(tech =>
            `<span class="technology-tag">
                ${tech}
                <span class="remove-tech" onclick="removeTechnology('${tech}')">&times;</span>
            </span>`
        ).join('');
        technologiesHidden.value = JSON.stringify(technologies);
    }

    window.removeTechnology = function(tech) {
        technologies = technologies.filter(t => t !== tech);
        updateTechnologiesDisplay();
    };

    // Image preview functionality
    document.getElementById('project_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('project_image_preview');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" class="image-preview" alt="Project Image Preview">`;
            };
            reader.readAsDataURL(file);
        } else {
            preview.innerHTML = '';
        }
    });

    // Gallery images preview
    document.getElementById('gallery_images').addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        const preview = document.getElementById('gallery_images_preview');

        preview.innerHTML = '';

        files.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'col-md-3 col-sm-4 col-6';
                div.innerHTML = `
                    <div class="gallery-preview-item">
                        <img src="${e.target.result}" class="image-preview w-100" alt="Gallery Image ${index + 1}">
                        <button type="button" class="remove-image" onclick="removeGalleryImage(${index})">&times;</button>
                    </div>
                `;
                preview.appendChild(div);
            };
            reader.readAsDataURL(file);
        });
    });

    window.removeGalleryImage = function(index) {
        const input = document.getElementById('gallery_images');
        const dt = new DataTransfer();

        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });

        input.files = dt.files;
        input.dispatchEvent(new Event('change'));
    };

    // Form validation
    document.getElementById('projectForm').addEventListener('submit', function(e) {
        console.log('Form submission started');

        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        const status = document.getElementById('status').value;

        console.log('Form data:', {
            title: title,
            description: description,
            status: status,
            technologies: document.getElementById('technologies_hidden').value
        });

        if (!title || !description || !status) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ri-loader-4-line me-1 spinner-border spinner-border-sm"></i>Creating...';

        console.log('Form validation passed, submitting...');
    });

    // Character counters
    const descriptionTextarea = document.getElementById('description');
    const shortDescriptionTextarea = document.getElementById('short_description');

    function addCharacterCounter(element, maxLength) {
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        element.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - element.value.length;
            counter.textContent = `${element.value.length}/${maxLength} characters`;
            counter.className = `form-text text-end ${remaining < 50 ? 'text-warning' : remaining < 0 ? 'text-danger' : ''}`;
        }

        element.addEventListener('input', updateCounter);
        updateCounter();
    }

    addCharacterCounter(descriptionTextarea, 500);
    addCharacterCounter(shortDescriptionTextarea, 300);
});
</script>
@endpush
