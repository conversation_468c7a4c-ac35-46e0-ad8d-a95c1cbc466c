<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Team extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'designation',
        'bio',
        'email',
        'phone',
        'facebook',
        'twitter',
        'instagram',
        'linkedin',
        'team_image',
        'status',
        'sort_order',
    ];

    protected $casts = [
        'sort_order' => 'integer',
    ];

    /**
     * Scope to get only active team members
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get team members ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get the full URL for team member image
     */
    public function getTeamImageUrlAttribute()
    {
        if ($this->team_image) {
            // Check if it's already a full URL
            if (filter_var($this->team_image, FILTER_VALIDATE_URL)) {
                return $this->team_image;
            }
            // Check if it's a storage path
            if (Storage::disk('public')->exists($this->team_image)) {
                return Storage::url($this->team_image);
            }
            // Fallback to asset path
            return asset($this->team_image);
        }
        return null;
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => 'bg-success',
            'inactive' => 'bg-secondary',
        ];

        $class = $badges[$this->status] ?? 'bg-secondary';
        $text = ucfirst($this->status);
        return "<span class='badge {$class}'>{$text}</span>";
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-assign sort order when creating
        static::creating(function ($team) {
            if (is_null($team->sort_order)) {
                $team->sort_order = static::max('sort_order') + 1;
            }
            if (is_null($team->status)) {
                $team->status = 'active';
            }
        });

        // Clean up files when deleting
        static::deleting(function ($team) {
            if ($team->team_image && Storage::disk('public')->exists($team->team_image)) {
                Storage::disk('public')->delete($team->team_image);
            }
        });
    }
}
