<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SeoSetting;

class SeoSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'GrandTek IT Solutions',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'sort_order' => 1,
            ],
            [
                'key' => 'site_description',
                'value' => 'Leading software development company in Kenya offering custom web applications, mobile apps, and comprehensive IT solutions.',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'Site Description',
                'description' => 'Default meta description for your website',
                'sort_order' => 2,
            ],
            [
                'key' => 'site_keywords',
                'value' => 'software development Kenya, web development Nairobi, mobile app development, Laravel development, React development, IT solutions Kenya',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'Site Keywords',
                'description' => 'Default keywords for your website',
                'sort_order' => 3,
            ],
            [
                'key' => 'site_url',
                'value' => 'https://grandtek.co.ke',
                'type' => 'url',
                'group' => 'general',
                'label' => 'Site URL',
                'description' => 'The main URL of your website',
                'sort_order' => 4,
            ],

            // Meta Tags
            [
                'key' => 'default_og_image',
                'value' => 'https://grandtek.co.ke/images/og-default.jpg',
                'type' => 'url',
                'group' => 'meta',
                'label' => 'Default OG Image',
                'description' => 'Default Open Graph image for social sharing',
                'sort_order' => 1,
            ],
            [
                'key' => 'twitter_site',
                'value' => '@GrandTekIT',
                'type' => 'text',
                'group' => 'meta',
                'label' => 'Twitter Site Handle',
                'description' => 'Your Twitter handle for Twitter Cards',
                'sort_order' => 2,
            ],
            [
                'key' => 'facebook_app_id',
                'value' => '',
                'type' => 'text',
                'group' => 'meta',
                'label' => 'Facebook App ID',
                'description' => 'Facebook App ID for Open Graph',
                'sort_order' => 3,
            ],

            // Analytics & Tracking
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Google Analytics ID',
                'description' => 'Google Analytics 4 Measurement ID (G-XXXXXXXXXX)',
                'sort_order' => 1,
            ],
            [
                'key' => 'google_tag_manager_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Google Tag Manager ID',
                'description' => 'Google Tag Manager Container ID (GTM-XXXXXXX)',
                'sort_order' => 2,
            ],
            [
                'key' => 'google_search_console_verification',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Google Search Console Verification',
                'description' => 'Google Search Console verification meta tag content',
                'sort_order' => 3,
            ],
            [
                'key' => 'bing_verification',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Bing Verification',
                'description' => 'Bing Webmaster Tools verification code',
                'sort_order' => 4,
            ],

            // Social Media
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/grandtekit',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Your Facebook page URL',
                'sort_order' => 1,
            ],
            [
                'key' => 'twitter_url',
                'value' => 'https://twitter.com/grandtekit',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Your Twitter profile URL',
                'sort_order' => 2,
            ],
            [
                'key' => 'linkedin_url',
                'value' => 'https://linkedin.com/company/grandtek-it-solutions',
                'type' => 'url',
                'group' => 'social',
                'label' => 'LinkedIn URL',
                'description' => 'Your LinkedIn company page URL',
                'sort_order' => 3,
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/grandtekit',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Your Instagram profile URL',
                'sort_order' => 4,
            ],

            // Local SEO
            [
                'key' => 'business_name',
                'value' => 'GrandTek IT Solutions',
                'type' => 'text',
                'group' => 'local',
                'label' => 'Business Name',
                'description' => 'Official business name for local SEO',
                'sort_order' => 1,
            ],
            [
                'key' => 'business_address',
                'value' => 'Nairobi, Kenya',
                'type' => 'text',
                'group' => 'local',
                'label' => 'Business Address',
                'description' => 'Business address for local SEO',
                'sort_order' => 2,
            ],
            [
                'key' => 'business_phone',
                'value' => '+254 700 000 000',
                'type' => 'text',
                'group' => 'local',
                'label' => 'Business Phone',
                'description' => 'Business phone number',
                'sort_order' => 3,
            ],
            [
                'key' => 'business_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'local',
                'label' => 'Business Email',
                'description' => 'Business email address',
                'sort_order' => 4,
            ],
            [
                'key' => 'business_hours',
                'value' => 'Monday-Friday: 8:00 AM - 6:00 PM',
                'type' => 'text',
                'group' => 'local',
                'label' => 'Business Hours',
                'description' => 'Business operating hours',
                'sort_order' => 5,
            ],

            // Technical SEO
            [
                'key' => 'robots_txt_additions',
                'value' => '',
                'type' => 'textarea',
                'group' => 'technical',
                'label' => 'Robots.txt Additions',
                'description' => 'Additional content for robots.txt file',
                'sort_order' => 1,
            ],
            [
                'key' => 'sitemap_priority_default',
                'value' => '0.5',
                'type' => 'text',
                'group' => 'technical',
                'label' => 'Default Sitemap Priority',
                'description' => 'Default priority for sitemap entries (0.0 - 1.0)',
                'sort_order' => 2,
            ],
            [
                'key' => 'sitemap_changefreq_default',
                'value' => 'weekly',
                'type' => 'text',
                'group' => 'technical',
                'label' => 'Default Change Frequency',
                'description' => 'Default change frequency for sitemap entries',
                'sort_order' => 3,
            ],
            [
                'key' => 'canonical_url_force',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'technical',
                'label' => 'Force Canonical URLs',
                'description' => 'Always include canonical URLs in meta tags',
                'sort_order' => 4,
            ],
        ];

        foreach ($settings as $setting) {
            SeoSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
