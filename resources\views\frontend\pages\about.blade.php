@extends('frontend.layouts.app')

@section('title', $about ? $about->meta_title ?? $about->tittle : 'About Us - Grandtek IT Solutions')
@section('description', $about ? $about->meta_description ?? Str::limit($about->description, 160) : 'Learn more about GrandTek IT Solutions - Your partner in innovative technology solutions and exceptional service.')
@section('keywords', $about ? $about->meta_keywords : 'about us, IT solutions, software development, technology services, Kenya, GrandTek')

@push('styles')
<style>
.about-hero {
    background: linear-gradient(135deg, var(--grand-primary) 0%, var(--grand-info) 100%);
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.about-hero .container {
    position: relative;
    z-index: 2;
}

.stats-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(85, 110, 230, 0.1);
}

.stats-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stats-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--grand-primary);
    margin-bottom: 0.5rem;
    display: block;
}

.stats-icon {
    font-size: 2.5rem;
    color: var(--grand-primary);
    margin-bottom: 1rem;
}

.mission-vision-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    padding: 2rem;
    height: 100%;
    border-left: 4px solid var(--grand-primary);
}

.values-list {
    list-style: none;
    padding: 0;
}

.values-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.values-list li:last-child {
    border-bottom: none;
}

.values-list li i {
    color: var(--grand-success);
    margin-right: 1rem;
    font-size: 1.2rem;
}

.team-preview {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 3rem 0;
}

.team-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.team-card:hover {
    transform: translateY(-5px);
}

.team-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--grand-primary), var(--grand-info));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
}

.section-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--grand-primary), transparent);
    margin: 4rem 0;
}
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="about-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 text-white fw-bold mb-4">
                    {{ $about ? $about->tittle : 'About GrandTek IT Solutions' }}
                </h1>
                <p class="lead text-white-50 mb-4">
                    {{ $about ? $about->description : 'Empowering businesses with cutting-edge technology and comprehensive IT services.' }}
                </p>
                <div class="d-flex gap-3">
                    <a href="#our-story" class="btn btn-light btn-lg">
                        <i class="fas fa-book-open me-2"></i>Our Story
                    </a>
                    <a href="#team" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-users me-2"></i>Meet Our Team
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                @if($about && $about->aboutus_img_one)
                    <div class="position-relative">
                        <img src="{{ $about->aboutus_img_one_url }}" 
                             alt="{{ $about->tittle }}" 
                             class="img-fluid rounded-3 shadow-lg">
                        @if($about->aboutus_img_two)
                            <div class="position-absolute" style="bottom: -20px; right: -20px; width: 60%;">
                                <img src="{{ $about->aboutus_img_two_url }}" 
                                     alt="Secondary Image" 
                                     class="img-fluid rounded-3 shadow">
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
@if($about && ($about->years_experience > 0 || $about->projects_completed > 0 || $about->happy_clients > 0 || $about->team_members > 0))
<section class="py-5" style="margin-top: -50px;">
    <div class="container">
        <div class="row g-4">
            @foreach($about->statistics as $stat)
                @if($stat['number'] > 0)
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <i class="{{ $stat['icon'] }} stats-icon"></i>
                            <span class="stats-number">{{ $stat['number'] }}{{ $stat['suffix'] }}</span>
                            <p class="text-muted mb-0">{{ $stat['label'] }}</p>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Our Story Section -->
<section id="our-story" class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-4">Our Story</h2>
                <p class="lead text-muted mb-5">
                    {{ $about ? $about->short_description : 'With a focus on innovation and excellence, we specialize in custom software development that is tailored to meet the unique needs of our clients.' }}
                </p>
            </div>
        </div>
        
        @if($about && $about->full_description)
            <div class="row">
                <div class="col-12">
                    <div class="bg-light rounded-3 p-4">
                        <p class="mb-0">{{ $about->full_description }}</p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Mission & Vision Section -->
@if($about && ($about->mission || $about->vision))
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-4">Mission & Vision</h2>
                <p class="lead text-muted">Our guiding principles that drive everything we do</p>
            </div>
        </div>
        
        <div class="row g-4">
            @if($about->mission)
                <div class="col-lg-6">
                    <div class="mission-vision-card">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-bullseye text-primary fs-3 me-3"></i>
                            <h4 class="mb-0">Our Mission</h4>
                        </div>
                        <p class="text-muted mb-0">{{ $about->mission }}</p>
                    </div>
                </div>
            @endif
            
            @if($about->vision)
                <div class="col-lg-6">
                    <div class="mission-vision-card">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-eye text-primary fs-3 me-3"></i>
                            <h4 class="mb-0">Our Vision</h4>
                        </div>
                        <p class="text-muted mb-0">{{ $about->vision }}</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</section>
@endif

<!-- Values Section -->
@if($about && $about->values && count($about->values) > 0)
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center mb-5">
                <h2 class="display-5 fw-bold mb-4">Our Values</h2>
                <p class="lead text-muted">The core principles that guide our work and relationships</p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <ul class="values-list">
                    @foreach($about->values as $value)
                        <li>
                            <i class="fas fa-check-circle"></i>
                            <span>{{ $value }}</span>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
</section>
@endif

<div class="section-divider"></div>

<!-- Team Preview Section -->
@if($team && $team->count() > 0)
<section id="team" class="team-preview">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-4">Meet Our Team</h2>
                <p class="lead text-muted">The talented individuals behind our success</p>
            </div>
        </div>
        
        <div class="row g-4">
            @foreach($team->take(4) as $member)
                <div class="col-lg-3 col-md-6">
                    <div class="team-card">
                        @if($member->team_image)
                            <img src="{{ asset('storage/' . $member->team_image) }}" 
                                 alt="{{ $member->name }}" 
                                 class="team-avatar">
                        @else
                            <div class="team-avatar">
                                {{ strtoupper(substr($member->name, 0, 1)) }}
                            </div>
                        @endif
                        <h5 class="mb-1">{{ $member->name }}</h5>
                        <p class="text-muted small mb-3">{{ $member->designation }}</p>
                        
                        @if($member->facebook || $member->twitter || $member->linkedin || $member->instagram)
                            <div class="social-links">
                                @if($member->facebook)
                                    <a href="{{ $member->facebook }}" class="text-primary me-2" target="_blank">
                                        <i class="fab fa-facebook"></i>
                                    </a>
                                @endif
                                @if($member->twitter)
                                    <a href="{{ $member->twitter }}" class="text-info me-2" target="_blank">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                @endif
                                @if($member->linkedin)
                                    <a href="{{ $member->linkedin }}" class="text-primary me-2" target="_blank">
                                        <i class="fab fa-linkedin"></i>
                                    </a>
                                @endif
                                @if($member->instagram)
                                    <a href="{{ $member->instagram }}" class="text-danger" target="_blank">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
        
        @if($team->count() > 4)
            <div class="text-center mt-5">
                <a href="#team-section" class="btn btn-primary btn-lg">
                    <i class="fas fa-users me-2"></i>View All Team Members
                </a>
            </div>
        @endif
    </div>
</section>
@endif

<!-- Call to Action Section -->
<section class="py-5 bg-primary">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2 class="text-white mb-4">Ready to Work With Us?</h2>
                <p class="text-white-50 mb-4 lead">
                    Let's discuss how we can help transform your business with innovative technology solutions.
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="#contact" class="btn btn-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>Get In Touch
                    </a>
                    <a href="#services" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-cogs me-2"></i>Our Services
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
