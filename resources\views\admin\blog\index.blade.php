@extends('admin.layouts.app')

@section('title', 'Blog Management')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Blog Management</h1>
        <a href="{{ route('admin.blog.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create Post
        </a>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.blog.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        @foreach($statuses as $status)
                            <option value="{{ $status }}" {{ request('status') === $status ? 'selected' : '' }}>
                                {{ ucfirst($status) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-7">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="Search by title, excerpt, or content..."
                           value="{{ request('search') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Blog Posts Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Blog Posts ({{ $posts->total() }})</h6>
                @if($posts->count() > 0)
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="toggleBulkActions()">
                        <i class="fas fa-tasks me-2"></i>Bulk Actions
                    </button>
                @endif
            </div>
        </div>
        <div class="card-body">
            @if($posts->count() > 0)
                <form id="bulkForm" action="{{ route('admin.blog.bulk') }}" method="POST" style="display: none;">
                    @csrf
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <select name="action" class="form-select" required>
                                <option value="">Select Action</option>
                                <option value="publish">Publish</option>
                                <option value="draft">Move to Draft</option>
                                <option value="archive">Archive</option>
                                <option value="delete">Delete</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary me-2">Apply</button>
                            <button type="button" class="btn btn-secondary" onclick="toggleBulkActions()">Cancel</button>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th id="bulkCheckboxHeader" style="display: none;">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Post</th>
                                <th>Status</th>
                                <th>SEO</th>
                                <th>Stats</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($posts as $post)
                            <tr>
                                <td class="bulkCheckbox" style="display: none;">
                                    <input type="checkbox" name="posts[]" value="{{ $post->id }}" 
                                           form="bulkForm" class="form-check-input post-checkbox">
                                </td>
                                <td>
                                    <div class="d-flex align-items-start">
                                        @if($post->featured_image)
                                            <img src="{{ Storage::url($post->featured_image) }}" 
                                                 alt="{{ $post->title }}" 
                                                 class="me-3 rounded" 
                                                 style="width: 60px; height: 40px; object-fit: cover;">
                                        @endif
                                        <div>
                                            <strong>{{ Str::limit($post->title, 40) }}</strong>
                                            @if($post->featured)
                                                <span class="badge bg-warning text-dark ms-2">Featured</span>
                                            @endif
                                            <br>
                                            <small class="text-muted">{{ Str::limit($post->excerpt, 60) }}</small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>{{ $post->reading_time }} min read
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($post->status === 'published')
                                        <span class="badge bg-success">Published</span>
                                    @elseif($post->status === 'draft')
                                        <span class="badge bg-warning">Draft</span>
                                    @else
                                        <span class="badge bg-secondary">Archived</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="small">
                                        @if($post->meta_title)
                                            <i class="fas fa-check text-success" title="Has Meta Title"></i>
                                        @else
                                            <i class="fas fa-times text-danger" title="No Meta Title"></i>
                                        @endif
                                        
                                        @if($post->meta_description)
                                            <i class="fas fa-check text-success" title="Has Meta Description"></i>
                                        @else
                                            <i class="fas fa-times text-danger" title="No Meta Description"></i>
                                        @endif
                                        
                                        @if($post->og_title || $post->og_description)
                                            <i class="fab fa-facebook text-primary" title="Has OG Tags"></i>
                                        @endif
                                        
                                        @if($post->twitter_title || $post->twitter_description)
                                            <i class="fab fa-twitter text-info" title="Has Twitter Cards"></i>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <small>
                                        <i class="fas fa-eye me-1"></i>{{ $post->views_count }}<br>
                                        @if($post->categories)
                                            <span class="badge bg-light text-dark">{{ count($post->categories) }} categories</span>
                                        @endif
                                    </small>
                                </td>
                                <td>
                                    <small>
                                        @if($post->published_at)
                                            <strong>Published:</strong><br>
                                            {{ $post->published_at->format('M j, Y') }}
                                        @else
                                            <strong>Created:</strong><br>
                                            {{ $post->created_at->format('M j, Y') }}
                                        @endif
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.blog.edit', $post->id) }}" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('admin.blog.seo', $post->id) }}" 
                                           class="btn btn-outline-info" title="SEO">
                                            <i class="fas fa-search"></i>
                                        </a>
                                        @if($post->status === 'published')
                                            <a href="{{ route('blog.show', $post->slug) }}" 
                                               class="btn btn-outline-success" title="View" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endif
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deletePost({{ $post->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Showing {{ $posts->firstItem() }} to {{ $posts->lastItem() }} of {{ $posts->total() }} results
                    </div>
                    {{ $posts->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No blog posts found</h5>
                    <p class="text-muted">Start creating engaging content for your audience.</p>
                    <a href="{{ route('admin.blog.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Your First Post
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this blog post? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleBulkActions() {
    const bulkForm = document.getElementById('bulkForm');
    const bulkCheckboxes = document.querySelectorAll('.bulkCheckbox');
    const bulkCheckboxHeader = document.getElementById('bulkCheckboxHeader');
    
    if (bulkForm.style.display === 'none') {
        bulkForm.style.display = 'block';
        bulkCheckboxHeader.style.display = 'table-cell';
        bulkCheckboxes.forEach(cb => cb.style.display = 'table-cell');
    } else {
        bulkForm.style.display = 'none';
        bulkCheckboxHeader.style.display = 'none';
        bulkCheckboxes.forEach(cb => cb.style.display = 'none');
        document.getElementById('selectAll').checked = false;
        document.querySelectorAll('.post-checkbox').forEach(cb => cb.checked = false);
    }
}

document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.post-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

function deletePost(id) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/blog/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
