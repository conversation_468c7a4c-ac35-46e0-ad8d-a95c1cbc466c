<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Services\SEOService;

class SEOMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only process HTML responses
        if ($response->headers->get('Content-Type') && 
            strpos($response->headers->get('Content-Type'), 'text/html') !== false) {
            
            $this->injectSEOData($request, $response);
        }

        return $response;
    }

    /**
     * Inject SEO data into the response
     */
    private function injectSEOData(Request $request, $response)
    {
        $content = $response->getContent();
        
        // Add organization schema to all pages
        $organizationSchema = SEOService::getOrganizationSchema();
        $schemaScript = '<script type="application/ld+json">' . json_encode($organizationSchema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
        
        // Add breadcrumb schema for non-home pages
        if ($request->path() !== '/') {
            $breadcrumbs = $this->generateBreadcrumbs($request);
            if (!empty($breadcrumbs)) {
                $breadcrumbSchema = SEOService::getBreadcrumbSchema($breadcrumbs);
                $schemaScript .= "\n" . '<script type="application/ld+json">' . json_encode($breadcrumbSchema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
            }
        }
        
        // Inject before closing head tag
        $content = str_replace('</head>', $schemaScript . "\n</head>", $content);
        
        $response->setContent($content);
    }

    /**
     * Generate breadcrumbs based on current route
     */
    private function generateBreadcrumbs(Request $request): array
    {
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')]
        ];

        $path = trim($request->path(), '/');
        $segments = explode('/', $path);

        $currentPath = '';
        foreach ($segments as $segment) {
            $currentPath .= '/' . $segment;
            
            // Generate breadcrumb name based on segment
            $name = $this->generateBreadcrumbName($segment, $currentPath);
            if ($name) {
                $breadcrumbs[] = [
                    'name' => $name,
                    'url' => url($currentPath)
                ];
            }
        }

        return $breadcrumbs;
    }

    /**
     * Generate breadcrumb name from segment
     */
    private function generateBreadcrumbName(string $segment, string $path): ?string
    {
        $nameMap = [
            'about' => 'About Us',
            'services' => 'Our Services',
            'projects' => 'Our Projects',
            'systems' => 'Our Systems',
            'contact' => 'Contact Us',
            'blog' => 'Blog',
            'team' => 'Our Team'
        ];

        if (isset($nameMap[$segment])) {
            return $nameMap[$segment];
        }

        // For dynamic segments, try to get the actual name from database
        if (str_contains($path, '/service/')) {
            // Try to get service name
            $slug = basename($path);
            $service = \App\Models\Services::where('slug', $slug)->first();
            return $service ? $service->name : ucwords(str_replace('-', ' ', $slug));
        }

        if (str_contains($path, '/project/')) {
            // Try to get project name
            $slug = basename($path);
            $project = \App\Models\Project::where('slug', $slug)->first();
            return $project ? $project->title : ucwords(str_replace('-', ' ', $slug));
        }

        if (str_contains($path, '/system/')) {
            // Try to get system name
            $slug = basename($path);
            $system = \App\Models\System::where('slug', $slug)->first();
            return $system ? $system->title : ucwords(str_replace('-', ' ', $slug));
        }

        // Default: convert slug to title case
        return ucwords(str_replace('-', ' ', $segment));
    }
}
