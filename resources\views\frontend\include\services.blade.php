@if($services && $services->count() > 0)
<div class="container-fluid services py-5 mb-5" id="services">
    <div class="container">
        <div class="text-center mx-auto pb-5 wow fadeIn" data-wow-delay=".3s" style="max-width: 600px;">
            <h5 class="text-primary">Our Services</h5>
            <h1>Services Built Specifically For Your Business</h1>
        </div>
        <div class="row g-5 services-inner">
            @foreach($services as $index => $service)
                <div class="col-md-6 col-lg-4 wow fadeIn" data-wow-delay="{{ 0.3 + ($index * 0.2) }}s">
                    <div class="services-item bg-light">
                        <div class="p-4 text-center services-content">
                            <div class="services-content-icon">
                                @if($service['service_image_url'])
                                    <img src="{{ $service['service_image_url'] }}" alt="{{ $service['name'] }}" 
                                         class="mb-4" style="width: 80px; height: 80px; object-fit: contain;">
                                @else
                                    <i class="{{ $service['icon_class'] }} fa-7x mb-4 text-primary"></i>
                                @endif
                                <h4 class="mb-3">{{ $service['name'] }}</h4>
                                <p class="mb-4">
                                    {{ $service['short_description'] ?: $service['description'] }}
                                </p>
                                <a href="{{ route('service.detail', $service['slug']) }}" class="btn btn-secondary text-white px-5 py-3 rounded-pill">
                                    <i class="ri-eye-line me-2"></i>View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
@endif
