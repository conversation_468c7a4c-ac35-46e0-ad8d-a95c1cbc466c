<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class FixSlidersTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'slider:fix-table';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add missing status and sort_order columns to sliders table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking sliders table structure...');

        try {
            // Check if status column exists
            if (!Schema::hasColumn('sliders', 'status')) {
                $this->info('Adding status column...');
                DB::statement('ALTER TABLE sliders ADD COLUMN status ENUM("active", "inactive") DEFAULT "active" AFTER slogan_two_img');
                $this->info('✓ Status column added');
            } else {
                $this->info('✓ Status column already exists');
            }

            // Check if sort_order column exists
            if (!Schema::hasColumn('sliders', 'sort_order')) {
                $this->info('Adding sort_order column...');
                DB::statement('ALTER TABLE sliders ADD COLUMN sort_order INT DEFAULT 0 AFTER status');
                $this->info('✓ Sort_order column added');
                
                // Update existing records with sort order
                $this->info('Updating existing records with sort order...');
                $sliders = DB::table('sliders')->orderBy('id')->get();
                foreach ($sliders as $index => $slider) {
                    DB::table('sliders')
                        ->where('id', $slider->id)
                        ->update([
                            'sort_order' => $index + 1,
                            'status' => 'active'
                        ]);
                }
                $this->info("✓ Updated {$sliders->count()} existing records");
            } else {
                $this->info('✓ Sort_order column already exists');
            }

            $this->info('✅ Sliders table structure is now correct!');
            
            // Show current table structure
            $this->info("\nCurrent sliders table structure:");
            $columns = DB::select('DESCRIBE sliders');
            $this->table(['Field', 'Type', 'Null', 'Key', 'Default', 'Extra'], 
                array_map(function($col) {
                    return [
                        $col->Field,
                        $col->Type,
                        $col->Null,
                        $col->Key,
                        $col->Default,
                        $col->Extra
                    ];
                }, $columns)
            );

        } catch (\Exception $e) {
            $this->error('Error fixing sliders table: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
