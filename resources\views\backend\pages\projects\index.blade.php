@extends('backend.layouts.app')

@section('title', 'Projects Management')

@section('content')
<!-- <PERSON> Header -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Projects Management</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Projects</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button type="button" class="btn btn-outline-primary" id="reorderBtn">
                    <i class="ri-drag-move-line me-1"></i>Reorder Projects
                </button>
                <button type="button" class="btn btn-outline-info" onclick="showPreview()">
                    <i class="ri-eye-line me-1"></i>Preview
                </button>
            </div>
            <a href="{{ route('projects.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>Add New Project
            </a>
        </div>
    </div>
</div>

<!-- Projects Grid -->
<div class="row" id="projectsContainer">
    @forelse($projects as $project)
        <div class="col-xl-4 col-lg-6 col-md-6 project-item" data-id="{{ $project->id }}">
            <div class="card project-card h-100">
                <!-- Project Image -->
                <div class="project-media-preview position-relative">
                    @if($project->project_image_url)
                        <img src="{{ $project->project_image_url }}" class="card-img-top" 
                             style="height: 200px; object-fit: cover;" alt="{{ $project->title }}">
                    @else
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="ri-image-line text-muted" style="font-size: 3rem;"></i>
                        </div>
                    @endif
                    
                    <!-- Status Badge -->
                    <div class="status-badge position-absolute top-0 start-0 m-2">
                        {!! $project->status_badge !!}
                    </div>
                    
                    <!-- Featured Badge -->
                    @if($project->featured)
                        <div class="featured-badge position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning"><i class="ri-star-line"></i> Featured</span>
                        </div>
                    @endif
                    
                    <!-- Category Badge -->
                    @if($project->category)
                        <div class="category-badge position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-info">{{ $project->category }}</span>
                        </div>
                    @endif
                </div>

                <!-- Project Content -->
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-0">{{ $project->title }}</h5>
                        <div class="drag-handle text-muted" style="display: none;">
                            <i class="ri-drag-move-2-line"></i>
                        </div>
                    </div>
                    
                    @if($project->client_name)
                        <p class="text-muted small mb-2">
                            <i class="ri-user-line me-1"></i>{{ $project->client_name }}
                        </p>
                    @endif
                    
                    <p class="card-text text-muted">{{ Str::limit($project->description, 100) }}</p>
                    
                    @if($project->technologies && count($project->technologies) > 0)
                        <div class="mb-3">
                            @foreach(array_slice($project->technologies, 0, 3) as $tech)
                                <span class="badge bg-light text-dark me-1">{{ $tech }}</span>
                            @endforeach
                            @if(count($project->technologies) > 3)
                                <span class="badge bg-secondary">+{{ count($project->technologies) - 3 }} more</span>
                            @endif
                        </div>
                    @endif
                    
                    @if($project->start_date)
                        <p class="text-muted small mb-2">
                            <i class="ri-calendar-line me-1"></i>
                            {{ $project->start_date->format('M Y') }}
                            @if($project->end_date)
                                - {{ $project->end_date->format('M Y') }}
                            @endif
                        </p>
                    @endif
                </div>

                <!-- Project Actions -->
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="project-actions d-flex">
                            <a href="{{ route('projects.show', $project->id) }}" class="btn btn-sm btn-outline-info me-1" title="View">
                                <i class="ri-eye-line"></i>
                            </a>
                            <a href="{{ route('projects.edit', $project->id) }}" class="btn btn-sm btn-outline-primary me-1" title="Edit">
                                <i class="ri-edit-line"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-secondary me-1" 
                                    onclick="duplicateProject({{ $project->id }})" title="Duplicate">
                                <i class="ri-file-copy-line"></i>
                            </button>
                        </div>
                        <div class="d-flex">
                            <button type="button" class="btn btn-sm btn-outline-{{ $project->featured ? 'warning' : 'secondary' }} me-1"
                                    onclick="toggleFeatured({{ $project->id }})" title="{{ $project->featured ? 'Remove from Featured' : 'Mark as Featured' }}">
                                <i class="ri-star-{{ $project->featured ? 'fill' : 'line' }}"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-{{ $project->status === 'active' ? 'warning' : 'success' }} me-1"
                                    onclick="toggleStatus({{ $project->id }})" title="{{ $project->status === 'active' ? 'Deactivate' : 'Activate' }}">
                                <i class="ri-{{ $project->status === 'active' ? 'pause' : 'play' }}-line"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger"
                                    onclick="deleteProject({{ $project->id }})" title="Delete">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ri-folder-open-line text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 mb-3">No Projects Found</h4>
                    <p class="text-muted mb-4">You haven't created any projects yet. Start by adding your first project.</p>
                    <a href="{{ route('projects.create') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i>Create Your First Project
                    </a>
                </div>
            </div>
        </div>
    @endforelse
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Projects Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="previewContainer" style="min-height: 500px; background: #f8f9fa;">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    <!-- Toasts will be dynamically added here -->
</div>
@endsection

@push('styles')
<style>
.project-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.project-item.reorder-mode .project-card {
    cursor: move;
    border-color: var(--vz-primary);
}

.featured-badge, .category-badge {
    z-index: 2;
}

.status-badge .badge {
    font-size: 0.7rem;
}

.drag-handle {
    cursor: move;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    transform: scale(1.05);
}

/* Preview Modal Styles */
.preview-projects-wrapper {
    padding: 2rem;
}

.preview-project-item {
    margin-bottom: 2rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    overflow: hidden;
    background: white;
}

.preview-project-img {
    height: 200px;
    object-fit: cover;
}

.preview-project-content {
    padding: 1rem;
}
</style>
@endpush

@push('scripts')
<!-- Sortable.js for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
let sortable = null;
let isReorderMode = false;

document.addEventListener('DOMContentLoaded', function() {
    initializeReorderMode();
});

// Initialize reorder mode
function initializeReorderMode() {
    const reorderBtn = document.getElementById('reorderBtn');
    const container = document.getElementById('projectsContainer');

    reorderBtn.addEventListener('click', function() {
        isReorderMode = !isReorderMode;

        if (isReorderMode) {
            enableReorderMode();
            reorderBtn.innerHTML = '<i class="ri-save-line me-1"></i>Save Order';
            reorderBtn.classList.remove('btn-outline-primary');
            reorderBtn.classList.add('btn-success');
        } else {
            disableReorderMode();
            reorderBtn.innerHTML = '<i class="ri-drag-move-line me-1"></i>Reorder Projects';
            reorderBtn.classList.remove('btn-success');
            reorderBtn.classList.add('btn-outline-primary');
        }
    });
}

function enableReorderMode() {
    const container = document.getElementById('projectsContainer');
    const items = container.querySelectorAll('.project-item');

    // Show drag handles
    items.forEach(item => {
        item.classList.add('reorder-mode');
        const dragHandle = item.querySelector('.drag-handle');
        if (dragHandle) {
            dragHandle.style.display = 'block';
        }
    });

    // Initialize sortable
    sortable = Sortable.create(container, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        handle: '.project-card',
        onEnd: function(evt) {
            // Auto-save order when item is moved
            saveOrder();
        }
    });
}

function disableReorderMode() {
    const container = document.getElementById('projectsContainer');
    const items = container.querySelectorAll('.project-item');

    // Hide drag handles
    items.forEach(item => {
        item.classList.remove('reorder-mode');
        const dragHandle = item.querySelector('.drag-handle');
        if (dragHandle) {
            dragHandle.style.display = 'none';
        }
    });

    // Destroy sortable
    if (sortable) {
        sortable.destroy();
        sortable = null;
    }
}

function saveOrder() {
    const items = document.querySelectorAll('.project-item');
    const order = Array.from(items).map(item => item.dataset.id);

    fetch('/admin/projects/reorder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ order: order })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to update order');
    });
}

// Toggle status
function toggleStatus(id) {
    const projectCard = document.querySelector(`[data-id="${id}"]`);
    const statusBadge = projectCard.querySelector('.status-badge .badge');
    const toggleBtn = projectCard.querySelector(`button[onclick="toggleStatus(${id})"]`);

    fetch(`/admin/projects/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update status badge
            const newStatus = data.status;
            const badgeClasses = {
                'active': 'bg-success',
                'inactive': 'bg-secondary',
                'completed': 'bg-primary',
                'in_progress': 'bg-warning'
            };

            statusBadge.className = `badge ${badgeClasses[newStatus]}`;
            statusBadge.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1).replace('_', ' ');

            // Update toggle button
            const isActive = newStatus === 'active';
            toggleBtn.className = `btn btn-sm btn-outline-${isActive ? 'warning' : 'success'} me-1`;
            toggleBtn.innerHTML = `<i class="ri-${isActive ? 'pause' : 'play'}-line"></i>`;
            toggleBtn.title = isActive ? 'Deactivate' : 'Activate';

            showToast('success', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to toggle status');
    });
}

// Toggle featured
function toggleFeatured(id) {
    const projectCard = document.querySelector(`[data-id="${id}"]`);
    const featuredBadge = projectCard.querySelector('.featured-badge');
    const toggleBtn = projectCard.querySelector(`button[onclick="toggleFeatured(${id})"]`);

    fetch(`/admin/projects/${id}/toggle-featured`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update featured badge
            if (data.featured) {
                if (!featuredBadge) {
                    const newBadge = document.createElement('div');
                    newBadge.className = 'featured-badge position-absolute top-0 end-0 m-2';
                    newBadge.innerHTML = '<span class="badge bg-warning"><i class="ri-star-line"></i> Featured</span>';
                    projectCard.querySelector('.project-media-preview').appendChild(newBadge);
                }
                toggleBtn.classList.remove('btn-outline-secondary');
                toggleBtn.classList.add('btn-outline-warning');
                toggleBtn.innerHTML = '<i class="ri-star-fill"></i>';
                toggleBtn.title = 'Remove from Featured';
            } else {
                if (featuredBadge) {
                    featuredBadge.remove();
                }
                toggleBtn.classList.remove('btn-outline-warning');
                toggleBtn.classList.add('btn-outline-secondary');
                toggleBtn.innerHTML = '<i class="ri-star-line"></i>';
                toggleBtn.title = 'Mark as Featured';
            }

            showToast('success', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to toggle featured status');
    });
}

// Delete project
function deleteProject(id) {
    if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
        fetch(`/admin/projects/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => {
            if (response.ok) {
                showToast('success', 'Project deleted successfully!');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('error', 'Failed to delete project');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Failed to delete project');
        });
    }
}

// Duplicate project
function duplicateProject(id) {
    fetch(`/admin/projects/${id}/duplicate`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => {
        if (response.ok) {
            showToast('success', 'Project duplicated successfully!');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('error', 'Failed to duplicate project');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to duplicate project');
    });
}

// Show preview modal
function showPreview() {
    const previewContainer = document.getElementById('previewContainer');
    previewContainer.innerHTML = '<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"></div></div>';

    // Simulate loading homepage projects section
    setTimeout(() => {
        previewContainer.innerHTML = `
            <div class="preview-projects-wrapper">
                <div class="text-center mb-5">
                    <h5 class="text-primary">Our Projects</h5>
                    <h1>Our Recently Completed Projects</h1>
                </div>
                <div class="row g-4">
                    ${Array.from(document.querySelectorAll('.project-item')).slice(0, 6).map(item => {
                        const img = item.querySelector('img');
                        const title = item.querySelector('.card-title').textContent;
                        const description = item.querySelector('.card-text').textContent;
                        const category = item.querySelector('.category-badge .badge')?.textContent || 'General';

                        return `
                            <div class="col-md-6 col-lg-4">
                                <div class="preview-project-item">
                                    <img src="${img ? img.src : '/assets/img/placeholder.jpg'}" class="w-100 preview-project-img" alt="${title}">
                                    <div class="preview-project-content">
                                        <h5 class="text-secondary">${title}</h5>
                                        <p class="text-muted small">${category}</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }, 500);

    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// Toast notification function
function showToast(type, message) {
    const toastContainer = document.querySelector('.toast-container');
    const toastId = 'toast-' + Date.now();

    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="ri-${type === 'success' ? 'check' : 'error-warning'}-line me-2"></i>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toast = new bootstrap.Toast(document.getElementById(toastId));
    toast.show();

    // Remove toast element after it's hidden
    document.getElementById(toastId).addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}
</script>
@endpush
