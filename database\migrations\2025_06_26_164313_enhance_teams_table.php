<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            // Fix the typo: instagaram -> instagram
            if (Schema::hasColumn('teams', 'instagaram') && !Schema::hasColumn('teams', 'instagram')) {
                $table->renameColumn('instagaram', 'instagram');
            }

            // Add new fields for better team management
            if (!Schema::hasColumn('teams', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('team_image');
            }

            if (!Schema::hasColumn('teams', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('status');
            }

            if (!Schema::hasColumn('teams', 'bio')) {
                $table->text('bio')->nullable()->after('designation');
            }

            if (!Schema::hasColumn('teams', 'email')) {
                $table->string('email')->nullable()->after('bio');
            }

            if (!Schema::hasColumn('teams', 'phone')) {
                $table->string('phone')->nullable()->after('email');
            }

            // Make social media fields nullable
            $table->string('facebook')->nullable()->change();
            $table->string('twitter')->nullable()->change();
            $table->string('linkedin')->nullable()->change();
            $table->string('team_image')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            // Rename back if needed
            if (Schema::hasColumn('teams', 'instagram') && !Schema::hasColumn('teams', 'instagaram')) {
                $table->renameColumn('instagram', 'instagaram');
            }

            // Remove added columns
            $columnsToRemove = ['status', 'sort_order', 'bio', 'email', 'phone'];
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('teams', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
